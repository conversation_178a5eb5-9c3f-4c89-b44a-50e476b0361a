"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CanonicalizeUValue = CanonicalizeUValue;
var utils_1 = require("./utils");
function CanonicalizeUValue(ukey, uvalue) {
    // TODO: Implement algorithm for CanonicalizeUValue per https://tc39.es/ecma402/#sec-canonicalizeuvalue
    var lowerValue = uvalue.toLowerCase();
    (0, utils_1.invariant)(ukey !== undefined, "ukey must be defined");
    var canonicalized = lowerValue;
    return canonicalized;
}
