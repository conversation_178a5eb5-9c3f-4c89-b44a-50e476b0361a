{"version": 3, "sources": ["../../../src/export/routes/pages.ts"], "names": ["exportPages", "ExportedPagesFiles", "req", "res", "path", "page", "query", "htmlFilepath", "htmlFilename", "ampPath", "subFolders", "outDir", "ampValidator<PERSON>ath", "pagesDataDir", "buildExport", "isDynamic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderOpts", "components", "fileWriter", "ampState", "ampFirs<PERSON>", "pageConfig", "amp", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "hybrid", "inAmpMode", "isInAmpMode", "hybridAmp", "getServerSideProps", "Error", "SERVER_PROPS_EXPORT_ERROR", "getStaticProps", "endsWith", "renderResult", "Component", "RenderResult", "fromStatic", "optimizeFonts", "process", "env", "__NEXT_OPTIMIZE_FONTS", "JSON", "stringify", "optimizeCss", "__NEXT_OPTIMIZE_CSS", "lazyRenderPagesPage", "err", "isBailoutToCSRError", "ssgNotFound", "metadata", "isNotFound", "ampValidations", "validateAmp", "rawAmpHtml", "ampPageName", "validatorPath", "validator", "AmpHtmlValidator", "getInstance", "result", "validateString", "errors", "filter", "e", "severity", "warnings", "length", "push", "html", "isNull", "toUnchunkedString", "ampRenderResult", "ampSkipValidation", "ampHtmlFilename", "join", "ampHtmlFilepath", "exists", "fileExists", "FileType", "File", "ampHtml", "pageData", "dataFile", "replace", "NEXT_DATA_SUFFIX", "revalidate"], "mappings": ";;;;;;;;;;;;;;;;;;IA6BsBA,WAAW;eAAXA;;;qEAvBG;sBACJ;yBAKO;2BAIrB;8BAC6B;yEACP;4BACQ;8BACD;;;;;;;UAElBC;;;;;GAAAA,uBAAAA;AAOX,eAAeD,YACpBE,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,KAAyB,EACzBC,YAAoB,EACpBC,YAAoB,EACpBC,OAAe,EACfC,UAAmB,EACnBC,MAAc,EACdC,gBAAoC,EACpCC,YAAoB,EACpBC,WAAoB,EACpBC,SAAkB,EAClBC,kBAA2B,EAC3BC,UAAsB,EACtBC,UAAoC,EACpCC,UAAsB;QAGVD,wBAEFA;IAHV,MAAME,WAAW;QACfC,UAAUH,EAAAA,yBAAAA,WAAWI,UAAU,qBAArBJ,uBAAuBK,GAAG,MAAK;QACzCC,UAAUC,QAAQnB,MAAMiB,GAAG;QAC3BG,QAAQR,EAAAA,0BAAAA,WAAWI,UAAU,qBAArBJ,wBAAuBK,GAAG,MAAK;IACzC;IAEA,MAAMI,YAAYC,IAAAA,oBAAW,EAACR;IAC9B,MAAMS,YAAYT,SAASM,MAAM;IAEjC,IAAIR,WAAWY,kBAAkB,EAAE;QACjC,MAAM,IAAIC,MAAM,CAAC,eAAe,EAAE1B,KAAK,EAAE,EAAE2B,oCAAyB,CAAC,CAAC;IACxE;IAEA,mDAAmD;IACnD,uBAAuB;IACvB,IAAI,CAAClB,eAAeI,WAAWe,cAAc,IAAI,CAAClB,WAAW;QAC3D;IACF;IAEA,IAAIG,WAAWe,cAAc,IAAI,CAAC1B,aAAa2B,QAAQ,CAAC,UAAU;QAChE,0DAA0D;QAC1D3B,gBAAgB;QAChBC,gBAAgB;IAClB;IAEA,IAAI2B;IAEJ,IAAI,OAAOjB,WAAWkB,SAAS,KAAK,UAAU;QAC5CD,eAAeE,qBAAY,CAACC,UAAU,CAACpB,WAAWkB,SAAS;QAE3D,IAAIpB,oBAAoB;YACtB,MAAM,IAAIe,MACR,CAAC,uCAAuC,EAAE3B,KAAK,mLAAmL,CAAC;QAEvO;IACF,OAAO;QACL;;;;;KAKC,GACD,IAAIa,WAAWsB,aAAa,EAAE;YAC5BC,QAAQC,GAAG,CAACC,qBAAqB,GAAGC,KAAKC,SAAS,CAChD3B,WAAWsB,aAAa;QAE5B;QACA,IAAItB,WAAW4B,WAAW,EAAE;YAC1BL,QAAQC,GAAG,CAACK,mBAAmB,GAAGH,KAAKC,SAAS,CAAC;QACnD;QACA,IAAI;YACFT,eAAe,MAAMY,IAAAA,iCAAmB,EACtC7C,KACAC,KACAE,MACAC,OACAW;QAEJ,EAAE,OAAO+B,KAAK;YACZ,IAAI,CAACC,IAAAA,iCAAmB,EAACD,MAAM,MAAMA;QACvC;IACF;IAEA,MAAME,cAAcf,gCAAAA,aAAcgB,QAAQ,CAACC,UAAU;IAErD,MAAMC,iBAAkC,EAAE;IAE1C,MAAMC,cAAc,OAClBC,YACAC,aACAC;QAEA,MAAMC,YAAY,MAAMC,yBAAgB,CAACC,WAAW,CAACH;QACrD,MAAMI,SAASH,UAAUI,cAAc,CAACP;QACxC,MAAMQ,SAASF,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;QAC1D,MAAMC,WAAWN,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;QAE5D,IAAIC,SAASC,MAAM,IAAIL,OAAOK,MAAM,EAAE;YACpCf,eAAegB,IAAI,CAAC;gBAClBhE,MAAMmD;gBACNK,QAAQ;oBACNE;oBACAI;gBACF;YACF;QACF;IACF;IAEA,MAAMG,OACJnC,gBAAgB,CAACA,aAAaoC,MAAM,GAAGpC,aAAaqC,iBAAiB,KAAK;IAE5E,IAAIC;IAEJ,IAAI9C,aAAa,CAACV,WAAWyD,iBAAiB,EAAE;QAC9C,IAAI,CAACxB,aAAa;YAChB,MAAMI,YAAYgB,MAAMlE,MAAMQ;QAChC;IACF,OAAO,IAAIiB,WAAW;QACpB,MAAM8C,kBAAkBjE,aACpBkE,IAAAA,UAAI,EAACnE,SAAS,gBACd,CAAC,EAAEA,QAAQ,KAAK,CAAC;QAErB,MAAMoE,kBAAkBD,IAAAA,UAAI,EAACjE,QAAQgE;QAErC,MAAMG,SAAS,MAAMC,IAAAA,sBAAU,EAACF,iBAAiBG,oBAAQ,CAACC,IAAI;QAC9D,IAAI,CAACH,QAAQ;YACX,IAAI;gBACFL,kBAAkB,MAAM1B,IAAAA,iCAAmB,EACzC7C,KACAC,KACAE,MACA;oBAAE,GAAGC,KAAK;oBAAEiB,KAAK;gBAAI,GACrBN;YAEJ,EAAE,OAAO+B,KAAK;gBACZ,IAAI,CAACC,IAAAA,iCAAmB,EAACD,MAAM,MAAMA;YACvC;YAEA,MAAMkC,UACJT,mBAAmB,CAACA,gBAAgBF,MAAM,GACtCE,gBAAgBD,iBAAiB,KACjC;YACN,IAAI,CAACvD,WAAWyD,iBAAiB,EAAE;gBACjC,MAAMpB,YAAY4B,SAAS7E,OAAO,UAAUO;YAC9C;YAEA,MAAMO,uBAEJ0D,iBACAK,SACA;QAEJ;IACF;IAEA,MAAM/B,WAAWhB,CAAAA,gCAAAA,aAAcgB,QAAQ,MAAIsB,mCAAAA,gBAAiBtB,QAAQ,KAAI,CAAC;IACzE,IAAIA,SAASgC,QAAQ,EAAE;QACrB,MAAMC,WAAWR,IAAAA,UAAI,EACnB/D,cACAL,aAAa6E,OAAO,CAAC,WAAWC,2BAAgB;QAGlD,MAAMnE,mBAEJiE,UACAzC,KAAKC,SAAS,CAACO,SAASgC,QAAQ,GAChC;QAGF,IAAItD,WAAW;YACb,MAAMV,4BAEJiE,SAASC,OAAO,CAAC,WAAW,cAC5B1C,KAAKC,SAAS,CAACO,SAASgC,QAAQ,GAChC;QAEJ;IACF;IAEA,IAAI,CAACjC,aAAa;QAChB,qEAAqE;QACrE,MAAM/B,mBAAoCZ,cAAc+D,MAAM;IAChE;IAEA,OAAO;QACLjB;QACAkC,YAAYpC,SAASoC,UAAU,IAAI;QACnCrC;IACF;AACF"}