{"version": 3, "sources": ["../../../src/shared/lib/is-plain-object.ts"], "names": ["getObjectClassLabel", "value", "Object", "prototype", "toString", "call", "isPlainObject", "getPrototypeOf", "hasOwnProperty"], "mappings": "AAAA,OAAO,SAASA,oBAAoBC,KAAU;IAC5C,OAAOC,OAAOC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ;AACxC;AAEA,OAAO,SAASK,cAAcL,KAAU;IACtC,IAAID,oBAAoBC,WAAW,mBAAmB;QACpD,OAAO;IACT;IAEA,MAAME,YAAYD,OAAOK,cAAc,CAACN;IAExC;;;;;;;;GAQC,GACD,OAAOE,cAAc,QAAQA,UAAUK,cAAc,CAAC;AACxD"}