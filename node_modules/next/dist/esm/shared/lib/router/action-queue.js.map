{"version": 3, "sources": ["../../../../src/shared/lib/router/action-queue.ts"], "names": ["isThenable", "ACTION_REFRESH", "ACTION_SERVER_ACTION", "ACTION_NAVIGATE", "ACTION_RESTORE", "reducer", "React", "startTransition", "ActionQueueContext", "createContext", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "needsRefresh", "dispatch", "type", "origin", "window", "location", "prevState", "state", "Error", "payload", "actionResult", "handleResult", "nextState", "discarded", "devToolsInstance", "send", "resolve", "then", "err", "reject", "dispatchAction", "resolvers", "deferred<PERSON><PERSON><PERSON>", "Promise", "newAction", "last", "createMutableActionQueue", "result"], "mappings": "AAAA,SACEA,UAAU,EAIVC,cAAc,EACdC,oBAAoB,EACpBC,eAAe,EACfC,cAAc,QACT,iEAAgE;AAEvE,SAASC,OAAO,QAAQ,2DAA0D;AAClF,OAAOC,SAASC,eAAe,QAAQ,QAAO;AAsB9C,OAAO,MAAMC,qBACXF,MAAMG,aAAa,CAA8B,MAAK;AAExD,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF,OAAO;YACL,4DAA4D;YAC5D,IAAID,YAAYM,YAAY,EAAE;gBAC5BN,YAAYM,YAAY,GAAG;gBAC3BN,YAAYO,QAAQ,CAClB;oBACEC,MAAMlB;oBACNmB,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAR;YAEJ;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMW,YAAYZ,YAAYa,KAAK;IACnC,IAAI,CAACD,WAAW;QACd,sFAAsF;QACtF,MAAM,IAAIE,MAAM;IAClB;IAEAd,YAAYE,OAAO,GAAGG;IAEtB,MAAMU,UAAUV,OAAOU,OAAO;IAC9B,MAAMC,eAAehB,YAAYK,MAAM,CAACO,WAAWG;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAIb,OAAOc,SAAS,EAAE;YACpB;QACF;QAEAnB,YAAYa,KAAK,GAAGK;QAEpB,IAAIlB,YAAYoB,gBAAgB,EAAE;YAChCpB,YAAYoB,gBAAgB,CAACC,IAAI,CAACN,SAASG;QAC7C;QAEAnB,oBAAoBC,aAAaC;QACjCI,OAAOiB,OAAO,CAACJ;IACjB;IAEA,8DAA8D;IAC9D,IAAI7B,WAAW2B,eAAe;QAC5BA,aAAaO,IAAI,CAACN,cAAc,CAACO;YAC/BzB,oBAAoBC,aAAaC;YACjCI,OAAOoB,MAAM,CAACD;QAChB;IACF,OAAO;QACLP,aAAaD;IACf;AACF;AAEA,SAASU,eACP1B,WAAiC,EACjCe,OAAuB,EACvBd,QAA8B;IAE9B,IAAI0B,YAGA;QAAEL,SAASrB;QAAUwB,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIV,QAAQP,IAAI,KAAKf,gBAAgB;QACnC,6DAA6D;QAC7D,MAAMmC,kBAAkB,IAAIC,QAAwB,CAACP,SAASG;YAC5DE,YAAY;gBAAEL;gBAASG;YAAO;QAChC;QAEA7B,gBAAgB;YACd,oGAAoG;YACpG,iEAAiE;YACjEK,SAAS2B;QACX;IACF;IAEA,MAAME,YAA6B;QACjCf;QACAZ,MAAM;QACNmB,SAASK,UAAUL,OAAO;QAC1BG,QAAQE,UAAUF,MAAM;IAC1B;IAEA,8BAA8B;IAC9B,IAAIzB,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAY+B,IAAI,GAAGD;QAEnB1B,UAAU;YACRJ;YACAK,QAAQyB;YACR7B;QACF;IACF,OAAO,IACLc,QAAQP,IAAI,KAAKhB,mBACjBuB,QAAQP,IAAI,KAAKf,gBACjB;QACA,+EAA+E;QAC/E,oHAAoH;QACpHO,YAAYE,OAAO,CAACiB,SAAS,GAAG;QAEhC,4CAA4C;QAC5CnB,YAAY+B,IAAI,GAAGD;QAEnB,2GAA2G;QAC3G,IAAI9B,YAAYE,OAAO,CAACa,OAAO,CAACP,IAAI,KAAKjB,sBAAsB;YAC7DS,YAAYM,YAAY,GAAG;QAC7B;QAEAF,UAAU;YACRJ;YACAK,QAAQyB;YACR7B;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAY+B,IAAI,KAAK,MAAM;YAC7B/B,YAAY+B,IAAI,CAAC5B,IAAI,GAAG2B;QAC1B;QACA9B,YAAY+B,IAAI,GAAGD;IACrB;AACF;AAEA,OAAO,SAASE;IACd,MAAMhC,cAAoC;QACxCa,OAAO;QACPN,UAAU,CAACQ,SAAyBd,WAClCyB,eAAe1B,aAAae,SAASd;QACvCI,QAAQ,OAAOQ,OAAuBR;YACpC,IAAIQ,UAAU,MAAM;gBAClB,MAAM,IAAIC,MAAM;YAClB;YACA,MAAMmB,SAASvC,QAAQmB,OAAOR;YAC9B,OAAO4B;QACT;QACA/B,SAAS;QACT6B,MAAM;IACR;IAEA,OAAO/B;AACT"}