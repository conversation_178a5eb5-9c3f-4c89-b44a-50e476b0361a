{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/is-dynamic.ts"], "names": ["extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "TEST_ROUTE", "isDynamicRoute", "route", "interceptedRoute", "test"], "mappings": "AAAA,SACEA,mCAAmC,EACnCC,0BAA0B,QACrB,wDAAuD;AAE9D,qCAAqC;AACrC,MAAMC,aAAa;AAEnB,OAAO,SAASC,eAAeC,KAAa;IAC1C,IAAIH,2BAA2BG,QAAQ;QACrCA,QAAQJ,oCAAoCI,OAAOC,gBAAgB;IACrE;IAEA,OAAOH,WAAWI,IAAI,CAACF;AACzB"}