{"version": 3, "sources": ["../../../../src/shared/lib/i18n/get-locale-redirect.ts"], "names": ["acceptLanguage", "denormalizePagePath", "detectDomainLocale", "formatUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getLocaleFromCookie", "i18n", "headers", "nextLocale", "NEXT_LOCALE", "toLowerCase", "locales", "find", "locale", "undefined", "detectLocale", "domainLocale", "preferredLocale", "pathLocale", "defaultLocale", "getAcceptPreferredLocale", "Array", "isArray", "err", "getLocaleRedirect", "nextConfig", "urlParsed", "localeDetection", "pathname", "detectedLocale", "preferredDomain", "domains", "isPDomain", "domain", "isPLocale", "scheme", "http", "rlocale", "basePath", "trailingSlash"], "mappings": "AAEA,SAASA,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,mBAAmB,QAAQ,qCAAoC;AACxE,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,SAAS,QAAQ,6BAA4B;AACtD,SAASC,eAAe,QAAQ,8CAA6C;AAe7E,SAASC,oBACPC,IAAgB,EAChBC,OAA8D;IAA9DA,IAAAA,oBAAAA,UAA4D,CAAC;QAE1CH,8BAAAA;IAAnB,MAAMI,cAAaJ,mBAAAA,gBACjBG,WAAW,CAAC,0BADKH,+BAAAA,iBAEdK,WAAW,qBAFGL,6BAEDM,WAAW;IAC7B,OAAOF,aACHF,KAAKK,OAAO,CAACC,IAAI,CAAC,CAACC,SAAWL,eAAeK,OAAOH,WAAW,MAC/DI;AACN;AAEA,SAASC,aAAa,KAYrB;IAZqB,IAAA,EACpBT,IAAI,EACJC,OAAO,EACPS,YAAY,EACZC,eAAe,EACfC,UAAU,EAOX,GAZqB;IAapB,OACEA,eACAF,gCAAAA,aAAcG,aAAa,KAC3Bd,oBAAoBC,MAAMC,YAC1BU,mBACAX,KAAKa,aAAa;AAEtB;AAEA,SAASC,yBACPd,IAAgB,EAChBC,OAA0D;IAE1D,IACEA,CAAAA,2BAAAA,OAAS,CAAC,kBAAkB,KAC5B,CAACc,MAAMC,OAAO,CAACf,OAAO,CAAC,kBAAkB,GACzC;QACA,IAAI;YACF,OAAOP,eAAeO,OAAO,CAAC,kBAAkB,EAAED,KAAKK,OAAO;QAChE,EAAE,OAAOY,KAAK,CAAC;IACjB;AACF;AAEA,OAAO,SAASC,kBAAkB,KAOxB;IAPwB,IAAA,EAChCL,aAAa,EACbH,YAAY,EACZE,UAAU,EACVX,OAAO,EACPkB,UAAU,EACVC,SAAS,EACD,GAPwB;IAQhC,IACED,WAAWnB,IAAI,IACfmB,WAAWnB,IAAI,CAACqB,eAAe,KAAK,SACpC1B,oBAAoByB,UAAUE,QAAQ,MAAM,KAC5C;QACA,MAAMX,kBAAkBG,yBAAyBK,WAAWnB,IAAI,EAAEC;QAClE,MAAMsB,iBAAiBd,aAAa;YAClCT,MAAMmB,WAAWnB,IAAI;YACrBW;YACAV;YACAW;YACAF;QACF;QAEA,MAAMc,kBAAkB5B,mBACtBuB,WAAWnB,IAAI,CAACyB,OAAO,EACvBjB,WACAG;QAGF,IAAID,gBAAgBc,iBAAiB;YACnC,MAAME,YAAYF,gBAAgBG,MAAM,KAAKjB,aAAaiB,MAAM;YAChE,MAAMC,YAAYJ,gBAAgBX,aAAa,KAAKF;YACpD,IAAI,CAACe,aAAa,CAACE,WAAW;gBAC5B,MAAMC,SAAS,AAAC,SAAML,CAAAA,gBAAgBM,IAAI,GAAG,KAAK,GAAE;gBACpD,MAAMC,UAAUH,YAAY,KAAKjB;gBACjC,OAAO,AAAGkB,SAAO,QAAKL,gBAAgBG,MAAM,GAAC,MAAGI;YAClD;QACF;QAEA,IAAIR,eAAenB,WAAW,OAAOS,cAAcT,WAAW,IAAI;YAChE,OAAOP,UAAU;gBACf,GAAGuB,SAAS;gBACZE,UAAU,AAAGH,CAAAA,WAAWa,QAAQ,IAAI,EAAC,IAAE,MAAGT,iBACxCJ,CAAAA,WAAWc,aAAa,GAAG,MAAM,EAAC;YAEtC;QACF;IACF;AACF"}