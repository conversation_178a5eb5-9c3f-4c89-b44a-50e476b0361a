{"version": 3, "sources": ["../../../src/shared/lib/error-source.ts"], "names": ["symbolError", "Symbol", "for", "getErrorSource", "error", "decorateServerError", "type", "Object", "defineProperty", "writable", "enumerable", "configurable", "value"], "mappings": "AAAA,MAAMA,cAAcC,OAAOC,GAAG,CAAC;AAE/B,OAAO,SAASC,eAAeC,KAAY;IACzC,OAAO,AAACA,KAAa,CAACJ,YAAY,IAAI;AACxC;AAIA,OAAO,SAASK,oBAAoBD,KAAY,EAAEE,IAAqB;IACrEC,OAAOC,cAAc,CAACJ,OAAOJ,aAAa;QACxCS,UAAU;QACVC,YAAY;QACZC,cAAc;QACdC,OAAON;IACT;AACF"}