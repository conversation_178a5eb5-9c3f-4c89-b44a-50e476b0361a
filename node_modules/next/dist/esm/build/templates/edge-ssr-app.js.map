{"version": 3, "sources": ["../../../src/build/templates/edge-ssr-app.ts"], "names": ["self", "adapter", "getRender", "IncrementalCache", "renderToHTMLOrFlight", "renderToHTML", "pageMod", "PAGE_TYPES", "setReferenceManifestsSingleton", "createServerModuleMap", "Document", "appMod", "errorMod", "error500Mod", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "__BUILD_MANIFEST", "prerenderManifest", "__PRERENDER_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "subresourceIntegrityManifest", "sriEnabled", "__SUBRESOURCE_INTEGRITY_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "pageName", "render", "pagesType", "APP", "dev", "page", "isServerComponent", "serverActions", "config", "nextConfig", "buildId", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "handler"], "mappings": "IAyCoBA;AAzCpB,OAAO,2BAA0B;AACjC,SAASC,OAAO,QAAQ,2BAA0B;AAClD,SAASC,SAAS,QAAQ,iDAAgD;AAC1E,SAASC,gBAAgB,QAAQ,qCAAoC;AAErE,SAASC,wBAAwBC,YAAY,QAAQ,qCAAoC;AACzF,YAAYC,aAAa,eAAc;AAMvC,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,8BAA8B,QAAQ,2CAA0C;AACzF,SAASC,qBAAqB,QAAQ,uCAAsC;AAG5E,0CAA0C;AAE1C,MAAMC,WAAyB;AAC/B,MAAMC,SAAS;AACf,MAAMC,WAAW;AACjB,MAAMC,cAAc;AAQpB,oBAAoB;AACpB,2BAA2B;AAC3B,aAAa;AACb,uBAAuB;AACvB,oBAAoB;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,gBAA+BnB,KAAKoB,gBAAgB;AAC1D,MAAMC,oBAAoBP,eAAed,KAAKsB,oBAAoB;AAClE,MAAMC,wBAAwBT,eAAed,KAAKwB,yBAAyB;AAC3E,MAAMC,eAAczB,uBAAAA,KAAK0B,cAAc,qBAAnB1B,oBAAqB,CAAC,WAAW;AACrD,MAAM2B,oBAAoBb,eAAed,KAAK4B,qBAAqB;AACnE,MAAMC,+BAA+BC,aACjChB,eAAed,KAAK+B,gCAAgC,IACpDb;AACJ,MAAMc,mBAAmBlB,eAAed,KAAKiC,oBAAoB;AAEjE,MAAMC,4BACJpB,eAAed,KAAKmC,qCAAqC,KAAK,EAAE;AAElE,IAAIV,eAAeE,mBAAmB;IACpCnB,+BAA+B;QAC7B4B,yBAAyBX;QACzBY,uBAAuBV;QACvBW,iBAAiB7B,sBAAsB;YACrC4B,uBAAuBV;YACvBY,UAAU;QACZ;IACF;AACF;AAEA,MAAMC,SAAStC,UAAU;IACvBuC,WAAWlC,WAAWmC,GAAG;IACzBC;IACAC,MAAM;IACNjC;IACAL;IACAM;IACAC;IACAH;IACAS;IACAE;IACAhB;IACAkB;IACAa,yBAAyBS,oBAAoBpB,cAAc;IAC3DY,uBAAuBQ,oBAAoBlB,oBAAoB;IAC/DmB,eAAeD,oBAAoBC,gBAAgB5B;IACnDW;IACAkB,QAAQC;IACRC,SAAS;IACTjB;IACAkB;IACAhB;AACF;AAEA,OAAO,MAAMiB,eAAe7C,QAAO;AAEnC,eAAe,SAAS8C,SAASC,IAA4C;IAC3E,OAAOpD,QAAQ;QACb,GAAGoD,IAAI;QACPlD;QACAmD,SAASd;IACX;AACF"}