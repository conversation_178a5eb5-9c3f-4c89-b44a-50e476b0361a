{"version": 3, "sources": ["../../../src/build/templates/app-route.ts"], "names": ["AppRouteRouteModule", "RouteKind", "patchFetch", "_patchFetch", "userland", "routeModule", "definition", "kind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "originalPathname"], "mappings": "AAAA,SACEA,mBAAmB,QAEd,8DAA6D;AACpE,SAASC,SAAS,QAAQ,iCAAgC;AAC1D,SAASC,cAAcC,WAAW,QAAQ,+BAA8B;AAExE,YAAYC,cAAc,eAAc;AAOxC,2EAA2E;AAC3E,UAAU;AACV,0BAA0B;AAE1B,MAAMC,cAAc,IAAIL,oBAAoB;IAC1CM,YAAY;QACVC,MAAMN,UAAUO,SAAS;QACzBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,kBAAkB;IAClBC;IACAV;AACF;AAEA,2EAA2E;AAC3E,2EAA2E;AAC3E,mCAAmC;AACnC,MAAM,EAAEW,mBAAmB,EAAEC,4BAA4B,EAAEC,WAAW,EAAE,GACtEZ;AAEF,MAAMa,mBAAmB;AAEzB,SAAShB;IACP,OAAOC,YAAY;QAAEc;QAAaD;IAA6B;AACjE;AAEA,SACEX,WAAW,EACXU,mBAAmB,EACnBC,4BAA4B,EAC5BC,WAAW,EACXC,gBAAgB,EAChBhB,UAAU,KACX"}