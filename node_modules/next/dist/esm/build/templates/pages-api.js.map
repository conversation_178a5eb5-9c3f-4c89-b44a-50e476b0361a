{"version": 3, "sources": ["../../../src/build/templates/pages-api.ts"], "names": ["PagesAPIRouteModule", "RouteKind", "hoist", "userland", "config", "routeModule", "definition", "kind", "PAGES_API", "page", "pathname", "bundlePath", "filename"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,8DAA6D;AACjG,SAASC,SAAS,QAAQ,iCAAgC;AAE1D,SAASC,KAAK,QAAQ,YAAW;AAEjC,4BAA4B;AAC5B,YAAYC,cAAc,eAAc;AAExC,wDAAwD;AACxD,eAAeD,MAAMC,UAAU,WAAU;AAEzC,oBAAoB;AACpB,OAAO,MAAMC,SAASF,MAAMC,UAAU,UAAS;AAE/C,4DAA4D;AAC5D,OAAO,MAAME,cAAc,IAAIL,oBAAoB;IACjDM,YAAY;QACVC,MAAMN,UAAUO,SAAS;QACzBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAT;AACF,GAAE"}