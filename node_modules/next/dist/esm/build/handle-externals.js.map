{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "names": ["WEBPACK_LAYERS", "defaultOverrides", "BARREL_OPTIMIZATION_PREFIX", "path", "NODE_BASE_ESM_RESOLVE_OPTIONS", "NODE_BASE_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "isWebpackAppLayer", "isWebpackServerOnlyLayer", "normalizePathSep", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "nodeModulesRegex", "containsImportInPackages", "request", "packages", "some", "pkg", "startsWith", "isResourceInPackages", "resource", "packageNames", "packageDirMapping", "p", "has", "get", "sep", "includes", "join", "replace", "resolveExternal", "dir", "esmExternalsConfig", "context", "isEsmRequested", "optOutBundlingPackages", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "nodeResolveOptions", "baseEsmResolveOptions", "baseResolveOptions", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "preferEsm", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "makeExternalHandler", "config", "optOutBundlingPackageRegex", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "test", "notExternalModules", "resolveNextExternal", "serverSideRendering", "isRelative", "fullRequest", "resolveResult", "undefined", "Error", "externalType", "transpilePackages", "Map", "pkgRes", "set", "dirname", "resolvedBundlingOptOutRes", "resolveBundlingOptOutPackages", "resolvedRes", "shouldBeBundled", "bundlePagesExternals", "isOptOutBundling", "isExternal"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mBAAkB;AAEjD,SAASC,gBAAgB,QAAQ,yBAAwB;AACzD,SAASC,0BAA0B,QAAQ,0BAAyB;AACpE,OAAOC,UAAU,gCAA+B;AAChD,SACEC,6BAA6B,EAC7BC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,QACf,mBAAkB;AACzB,SAASC,iBAAiB,EAAEC,wBAAwB,QAAQ,UAAS;AAErE,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,CAAC,EAAEF,SAAS,EAAEF,gBAAgB,EAAE,EAAEC,gBAAgB,CAAC;AAGrD,MAAMI,mBAAmB;AAEzB,SAASC,yBACPC,OAAe,EACfC,QAAkB;IAElB,OAAOA,SAASC,IAAI,CAClB,CAACC,MAAQH,YAAYG,OAAOH,QAAQI,UAAU,CAACD,MAAM;AAEzD;AAEA,OAAO,SAASE,qBACdC,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,IAAI,CAACD,cAAc,OAAO;IAC1B,OAAOA,aAAaL,IAAI,CAAC,CAACO,IACxBD,qBAAqBA,kBAAkBE,GAAG,CAACD,KACvCH,SAASF,UAAU,CAACI,kBAAkBG,GAAG,CAACF,KAAM1B,KAAK6B,GAAG,IACxDN,SAASO,QAAQ,CACf9B,KAAK6B,GAAG,GACN7B,KAAK+B,IAAI,CAAC,gBAAgBL,EAAEM,OAAO,CAAC,OAAOhC,KAAK6B,GAAG,KACnD7B,KAAK6B,GAAG;AAGpB;AAEA,OAAO,eAAeI,gBACpBC,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACfnB,OAAe,EACfoB,cAAuB,EACvBC,sBAAgC,EAChCC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBvC,wBAAwB,EACjDwC,qBAA0BvC,oBAAoB,EAC9CwC,wBAA6B3C,6BAA6B,EAC1D4C,qBAA0B3C,yBAAyB;IAEnD,MAAM4C,eAAe,CAAC,CAACX;IACvB,MAAMY,oBAAoBZ,uBAAuB;IAEjD,IAAIa,MAAqB;IACzB,IAAIC,QAAiB;IAErB,MAAMC,mBACJJ,gBACAT,kBACA,mEAAmE;IACnE,2EAA2E;IAC3E,2EAA2E;IAC3E,CAACrB,yBAAyBC,SAASqB,0BAC/B;QAAC;QAAM;KAAM,GACb;QAAC;KAAM;IAEb,KAAK,MAAMa,aAAaD,iBAAkB;QACxC,MAAME,UAAUb,WACdY,YAAYT,oBAAoBC;QAGlC,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;YACD,CAACK,KAAKC,MAAM,GAAG,MAAMG,QAAQhB,SAASnB;QACzC,EAAE,OAAOoC,KAAK;YACZL,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACX,kBAAkBY,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIP,iBAAiB;YACnB,OAAO;gBAAEc,UAAUd,gBAAgBQ;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIP,kBAAkB;YACpB,IAAIc;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAAclB,WAClBU,QAAQL,wBAAwBC;gBAEjC,CAACU,SAASC,UAAU,GAAG,MAAMC,YAAYvB,KAAKjB;YACjD,EAAE,OAAOoC,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,2EAA2E;YAC3E,wDAAwD;YACxD,IAAID,YAAYP,OAAOC,UAAUO,WAAW;gBAC1CR,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEA,OAAO,SAASS,oBAAoB,EAClCC,MAAM,EACNrB,sBAAsB,EACtBsB,0BAA0B,EAC1B1B,GAAG,EAMJ;QAE2ByB;IAD1B,IAAIE;IACJ,MAAMd,oBAAoBY,EAAAA,uBAAAA,OAAOG,YAAY,qBAAnBH,qBAAqBb,YAAY,MAAK;IAEhE,OAAO,eAAeiB,gBACpB3B,OAAe,EACfnB,OAAe,EACf+C,cAAsB,EACtBC,KAA8B,EAC9B1B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAM2B,UACJjD,QAAQI,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBrB,KAAKmE,KAAK,CAACC,UAAU,CAACnD,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBoD,QAAQC,QAAQ,KAAK,WAAWtE,KAAKuE,KAAK,CAACH,UAAU,CAACnD;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMuD,aAAanE,kBAAkB4D;QAErC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,aAAaO,IAAI,CAACxD,UAAU;gBAC9B,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IAAIT,mBAAmBiE,IAAI,CAACxD,YAAY,CAACuD,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAEvD,QAAQ,CAAC;YAC9B;YAEA,MAAMyD,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAACxD,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQa,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIb,QAAQI,UAAU,CAACtB,6BAA6B;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAMsC,iBAAiB2B,mBAAmB;QAE1C,4DAA4D;QAC5D,yFAAyF;QACzF,4DAA4D;QAC5D,IACE1D,yBAAyB2D,UACzBhD,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQI,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqDoD,IAAI,CAACxD,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8CwD,IAAI,CAACxD,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,8DAA8DwD,IAAI,CAChExD,YAEF,4CAA4CwD,IAAI,CAACxD,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,sEAAsEwD,IAAI,CACxExD,YAEF,2CAA2CwD,IAAI,CAACxD,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;YAC5B;YAEA,OAAO0D,oBAAoB1D;QAC7B;QAEA,gFAAgF;QAChF,qEAAqE;QACrE,oDAAoD;QACpD,gEAAgE;QAChE,IAAIgD,UAAUpE,eAAe+E,mBAAmB,EAAE;YAChD,MAAMC,aAAa5D,QAAQI,UAAU,CAAC;YACtC,MAAMyD,cAAcD,aAChBtE,iBAAiBP,KAAK+B,IAAI,CAACK,SAASnB,YACpCA;YAEJ,+CAA+C;YAC/C,IAAID,yBAAyB8D,aAAaxC,yBAAyB;gBACjE,OAAOwC;YACT;YACA,OAAOH,oBAAoBG;QAC7B;QAEA,6FAA6F;QAC7F,MAAMC,gBAAgB,MAAM9C,gBAC1BC,KACAyB,OAAOG,YAAY,CAAChB,YAAY,EAChCV,SACAnB,SACAoB,gBACAC,wBACAC,YACA2B,UAAUS,sBAAsBK;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAczB,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAIrC,YAAY,oBAAoB;YAClC8D,cAAc/B,GAAG,GAAGlD,gBAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAEkD,GAAG,EAAEC,KAAK,EAAE,GAAG8B;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAAC/B,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACX,kBAAkBY,SAAS,CAACF,qBAAqB,CAACmB,SAAS;YAC9D,MAAM,IAAIe,MACR,CAAC,cAAc,EAAEhE,QAAQ,2HAA2H,CAAC;QAEzJ;QAEA,MAAMiE,eAAejC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2CwB,IAAI,CAACzB,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2ByB,IAAI,CAACzB,QAChC,8BAA8ByB,IAAI,CAACzB,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIW,OAAOwB,iBAAiB,IAAI,CAACtB,6BAA6B;YAC5DA,8BAA8B,IAAIuB;YAClC,8DAA8D;YAC9D,KAAK,MAAMhE,OAAOuC,OAAOwB,iBAAiB,CAAE;gBAC1C,MAAME,SAAS,MAAMpD,gBACnBC,KACAyB,OAAOG,YAAY,CAAChB,YAAY,EAChCV,SACAhB,MAAM,iBACNiB,gBACAC,wBACAC,YACA2B,UAAUS,sBAAsBK;gBAElC,IAAIK,OAAOrC,GAAG,EAAE;oBACda,4BAA4ByB,GAAG,CAAClE,KAAKpB,KAAKuF,OAAO,CAACF,OAAOrC,GAAG;gBAC9D;YACF;QACF;QAEA,MAAMwC,4BAA4BC,8BAA8B;YAC9DC,aAAa1C;YACbY;YACAD;YACAE;YACAZ;YACAuB;YACAP;YACAiB;YACAjE;QACF;QACA,IAAIuE,2BAA2B;YAC7B,OAAOA;QACT;QAEA,2CAA2C;QAC3C;IACF;AACF;AAEA,SAASC,8BAA8B,EACrCC,WAAW,EACX9B,0BAA0B,EAC1BD,MAAM,EACNE,2BAA2B,EAC3BZ,KAAK,EACLuB,UAAU,EACVP,KAAK,EACLiB,YAAY,EACZjE,OAAO,EAWR;IACC,MAAM0E,kBACJrE,qBACEoE,aACA/B,OAAOwB,iBAAiB,EACxBtB,gCAEDZ,SAASuB,cACT,CAACA,cAAcb,OAAOG,YAAY,CAAC8B,oBAAoB;IAE1D,IAAI7E,iBAAiB0D,IAAI,CAACiB,cAAc;QACtC,MAAMG,mBAAmBjC,2BAA2Ba,IAAI,CAACiB;QACzD,IAAIpF,yBAAyB2D,QAAQ;YACnC,IAAI4B,kBAAkB;gBACpB,OAAO,CAAC,EAAEX,aAAa,CAAC,EAAEjE,QAAQ,CAAC,CAAC,2BAA2B;;YACjE;QACF,OAAO,IAAI,CAAC0E,mBAAmBE,kBAAkB;YAC/C,OAAO,CAAC,EAAEX,aAAa,CAAC,EAAEjE,QAAQ,CAAC,CAAC,0CAA0C;;QAChF;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS0D,oBAAoBrB,QAAgB;IAC3C,MAAMwC,aAAajF,gBAAgB4D,IAAI,CAACnB;IAExC,sFAAsF;IACtF,sGAAsG;IACtG,IAAIwC,YAAY;QACd,oGAAoG;QACpG,oCAAoC;QACpC,OAAO,CAAC,SAAS,EAAEvF,iBACjB+C,SAAStB,OAAO,CAAC,oBAAoB,cACrC,CAAC;IACL;AACF"}