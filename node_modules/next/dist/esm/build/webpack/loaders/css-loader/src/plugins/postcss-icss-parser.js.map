{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/postcss-icss-parser.ts"], "names": ["extractICSS", "replaceValueSymbols", "replaceSymbols", "normalizeUrl", "resolveRequests", "requestify", "plugin", "options", "postcssPlugin", "OnceExit", "root", "importReplacements", "Object", "create", "icssImports", "icssExports", "imports", "Map", "tasks", "url", "tokens", "keys", "length", "normalizedUrl", "prefix", "queryParts", "split", "pop", "join", "request", "rootContext", "doResolve", "resolver", "context", "resolvedUrl", "Set", "push", "results", "Promise", "all", "index", "item", "newUrl", "importKey", "importName", "get", "size", "set", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "icss", "api", "dedupe", "replacementIndex", "token", "entries", "replacement<PERSON>ame", "localName", "replacements", "name", "value", "exports", "postcss"], "mappings": "AAAA,SACEA,WAAW,EACXC,mBAAmB,EACnBC,cAAc,QACT,gCAA+B;AAEtC,SAASC,YAAY,EAAEC,eAAe,EAAEC,UAAU,QAAQ,WAAU;AAEpE,MAAMC,SAAS,CAACC,UAAe,CAAC,CAAC;IAC/B,OAAO;QACLC,eAAe;QACf,MAAMC,UAASC,IAAS;YACtB,MAAMC,qBAAqBC,OAAOC,MAAM,CAAC;YACzC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAE,GAAGf,YAAYU;YACjD,MAAMM,UAAU,IAAIC;YACpB,MAAMC,QAAQ,EAAE;YAEhB,wCAAwC;YACxC,IAAK,MAAMC,OAAOL,YAAa;gBAC7B,MAAMM,SAASN,WAAW,CAACK,IAAI;gBAE/B,IAAIP,OAAOS,IAAI,CAACD,QAAQE,MAAM,KAAK,GAAG;oBAEpC;gBACF;gBAEA,IAAIC,gBAAgBJ;gBACpB,IAAIK,SAAS;gBAEb,MAAMC,aAAaF,cAAcG,KAAK,CAAC;gBAEvC,IAAID,WAAWH,MAAM,GAAG,GAAG;oBACzBC,gBAAgBE,WAAWE,GAAG;oBAC9BH,SAASC,WAAWG,IAAI,CAAC;gBAC3B;gBAEA,MAAMC,UAAUxB,WACdF,aAAaoB,eAAe,OAC5BhB,QAAQuB,WAAW;gBAErB,MAAMC,YAAY;oBAChB,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE,GAAG1B;oBAC9B,MAAM2B,cAAc,MAAM9B,gBAAgB4B,UAAUC,SAAS;2BACxD,IAAIE,IAAI;4BAACZ;4BAAeM;yBAAQ;qBACpC;oBAED,IAAI,CAACK,aAAa;wBAChB;oBACF;oBAEA,6CAA6C;oBAC7C,OAAO;wBAAEf,KAAKe;wBAAaV;wBAAQJ;oBAAO;gBAC5C;gBAEAF,MAAMkB,IAAI,CAACL;YACb;YAEA,MAAMM,UAAU,MAAMC,QAAQC,GAAG,CAACrB;YAElC,IAAK,IAAIsB,QAAQ,GAAGA,SAASH,QAAQf,MAAM,GAAG,GAAGkB,QAAS;gBACxD,MAAMC,OAAOJ,OAAO,CAACG,MAAM;gBAE3B,IAAI,CAACC,MAAM;oBAET;gBACF;gBAEA,MAAMC,SAASD,KAAKjB,MAAM,GAAG,CAAC,EAAEiB,KAAKjB,MAAM,CAAC,CAAC,EAAEiB,KAAKtB,GAAG,CAAC,CAAC,GAAGsB,KAAKtB,GAAG;gBACpE,MAAMwB,YAAYD;gBAClB,IAAIE,aAAa5B,QAAQ6B,GAAG,CAACF;gBAE7B,IAAI,CAACC,YAAY;oBACfA,aAAa,CAAC,0BAA0B,EAAE5B,QAAQ8B,IAAI,CAAC,GAAG,CAAC;oBAC3D9B,QAAQ+B,GAAG,CAACJ,WAAWC;oBAEvBrC,QAAQS,OAAO,CAACoB,IAAI,CAAC;wBACnBY,MAAM;wBACNJ;wBACAzB,KAAKZ,QAAQ0C,UAAU,CAACP;wBACxBQ,MAAM;wBACNV;oBACF;oBAEAjC,QAAQ4C,GAAG,CAACf,IAAI,CAAC;wBAAEQ;wBAAYQ,QAAQ;wBAAMZ;oBAAM;gBACrD;gBAEA,KAAK,MAAM,CAACa,kBAAkBC,MAAM,IAAI1C,OAAOS,IAAI,CACjDoB,KAAKrB,MAAM,EACXmC,OAAO,GAAI;oBACX,MAAMC,kBAAkB,CAAC,0BAA0B,EAAEhB,MAAM,aAAa,EAAEa,iBAAiB,GAAG,CAAC;oBAC/F,MAAMI,YAAYhB,KAAKrB,MAAM,CAACkC,MAAM;oBAEpC3C,kBAAkB,CAAC2C,MAAM,GAAGE;oBAE5BjD,QAAQmD,YAAY,CAACtB,IAAI,CAAC;wBAAEoB;wBAAiBZ;wBAAYa;oBAAU;gBACrE;YACF;YAEA,IAAI7C,OAAOS,IAAI,CAACV,oBAAoBW,MAAM,GAAG,GAAG;gBAC9CpB,eAAeQ,MAAMC;YACvB;YAEA,KAAK,MAAMgD,QAAQ/C,OAAOS,IAAI,CAACN,aAAc;gBAC3C,MAAM6C,QAAQ3D,oBAAoBc,WAAW,CAAC4C,KAAK,EAAEhD;gBAErDJ,QAAQsD,OAAO,CAACzB,IAAI,CAAC;oBAAEuB;oBAAMC;gBAAM;YACrC;QACF;IACF;AACF;AAEAtD,OAAOwD,OAAO,GAAG;AAEjB,eAAexD,OAAM"}