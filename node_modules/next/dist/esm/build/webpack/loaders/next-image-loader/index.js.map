{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-image-loader/index.ts"], "names": ["path", "loaderUtils", "getImageSize", "getBlurImage", "nextImage<PERSON><PERSON><PERSON>", "content", "imageLoaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "options", "getOptions", "compilerType", "isDev", "assetPrefix", "basePath", "context", "rootContext", "opts", "interpolatedName", "interpolateName", "outputPath", "extension", "imageSizeSpan", "imageSize", "catch", "err", "Error", "name", "dataURL", "blurDataURL", "width", "blur<PERSON>idth", "height", "blurHeight", "tracing", "bind", "stringifiedData", "traceFn", "JSON", "stringify", "src", "emitFile", "join", "raw"], "mappings": "AAEA,OAAOA,UAAU,OAAM;AACvB,OAAOC,iBAAiB,mCAAkC;AAC1D,SAASC,YAAY,QAAQ,qCAAoC;AACjE,SAASC,YAAY,QAAQ,SAAQ;AASrC,SAASC,gBAA2BC,OAAe;IACjD,MAAMC,kBAAkB,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACzD,OAAOF,gBAAgBG,YAAY,CAAC;QAClC,MAAMC,UAAmB,IAAI,CAACC,UAAU;QACxC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAE,GAAGL;QACvD,MAAMM,UAAU,IAAI,CAACC,WAAW;QAEhC,MAAMC,OAAO;YAAEF;YAASX;QAAQ;QAChC,MAAMc,mBAAmBlB,YAAYmB,eAAe,CAClD,IAAI,EACJ,uCACAF;QAEF,MAAMG,aAAaP,cAAc,WAAWK;QAC5C,IAAIG,YAAYrB,YAAYmB,eAAe,CAAC,IAAI,EAAE,SAASF;QAC3D,IAAII,cAAc,OAAO;YACvBA,YAAY;QACd;QAEA,MAAMC,gBAAgBjB,gBAAgBE,UAAU,CAAC;QACjD,MAAMgB,YAAY,MAAMD,cAAcd,YAAY,CAAC,IACjDP,aAAaG,SAASiB,WAAWG,KAAK,CAAC,CAACC,MAAQA;QAGlD,IAAIF,qBAAqBG,OAAO;YAC9B,MAAMD,MAAMF;YACZE,IAAIE,IAAI,GAAG;YACX,MAAMF;QACR;QAEA,MAAM,EACJG,SAASC,WAAW,EACpBC,OAAOC,SAAS,EAChBC,QAAQC,UAAU,EACnB,GAAG,MAAM/B,aAAaE,SAASiB,WAAWE,WAAW;YACpDT;YACAM;YACAR;YACAsB,SAAS7B,gBAAgBE,UAAU,CAAC4B,IAAI,CAAC9B;QAC3C;QAEA,MAAM+B,kBAAkB/B,gBACrBE,UAAU,CAAC,wBACX8B,OAAO,CAAC,IACPC,KAAKC,SAAS,CAAC;gBACbC,KAAKpB;gBACLY,QAAQT,UAAUS,MAAM;gBACxBF,OAAOP,UAAUO,KAAK;gBACtBD;gBACAE;gBACAE;YACF;QAGJ,IAAItB,iBAAiB,UAAU;YAC7B,IAAI,CAAC8B,QAAQ,CAACvB,kBAAkBd,SAAS;QAC3C,OAAO;YACL,IAAI,CAACqC,QAAQ,CACX1C,KAAK2C,IAAI,CACP,MACA9B,SAASD,iBAAiB,gBAAgB,KAAK,MAC/CO,mBAEFd,SACA;QAEJ;QAEA,OAAO,CAAC,eAAe,EAAEgC,gBAAgB,CAAC,CAAC;IAC7C;AACF;AACA,OAAO,MAAMO,MAAM,KAAI;AACvB,eAAexC,gBAAe"}