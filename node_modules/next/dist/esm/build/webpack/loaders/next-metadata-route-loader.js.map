{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-route-loader.ts"], "names": ["fs", "path", "imageExtMimeTypeMap", "getLoaderModuleNamedExports", "errorOnBadHandler", "resourcePath", "JSON", "stringify", "cacheHeader", "none", "longCache", "revalidate", "getFilenameAndExtension", "filename", "basename", "name", "ext", "split", "getContentType", "getStaticAssetRouteCode", "fileBaseName", "cache", "process", "env", "NODE_ENV", "code", "promises", "readFile", "toString", "getDynamicTextRouteCode", "getDynamicImageRouteCode", "getDynamicSiteMapRouteCode", "page", "loaderContext", "staticGenerationCode", "exportNames", "reExportNames", "filter", "hasGenerateSiteMaps", "includes", "length", "join", "nextMetadataRouterLoader", "isDynamic", "filePath", "getOptions", "addDependency"], "mappings": "AACA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,SAASC,mBAAmB,QAAQ,yBAAwB;AAC5D,SAASC,2BAA2B,QAAQ,UAAS;AAErD,SAASC,kBAAkBC,YAAoB;IAC7C,OAAO,CAAC;;kDAEwC,EAAEC,KAAKC,SAAS,CAC5DF,cACA;;EAEJ,CAAC;AACH;AAEA,MAAMG,cAAc;IAClBC,MAAM;IACNC,WAAW;IACXC,YAAY;AACd;AAUA,OAAO,SAASC,wBAAwBP,YAAoB;IAC1D,MAAMQ,WAAWZ,KAAKa,QAAQ,CAACT;IAC/B,MAAM,CAACU,MAAMC,IAAI,GAAGH,SAASI,KAAK,CAAC,KAAK;IACxC,OAAO;QAAEF;QAAMC;IAAI;AACrB;AAEA,SAASE,eAAeb,YAAoB;IAC1C,IAAI,EAAEU,IAAI,EAAEC,GAAG,EAAE,GAAGJ,wBAAwBP;IAC5C,IAAIW,QAAQ,OAAOA,MAAM;IAEzB,IAAID,SAAS,aAAaC,QAAQ,OAAO,OAAO;IAChD,IAAID,SAAS,WAAW,OAAO;IAC/B,IAAIA,SAAS,UAAU,OAAO;IAC9B,IAAIA,SAAS,YAAY,OAAO;IAEhC,IAAIC,QAAQ,SAASA,QAAQ,UAAUA,QAAQ,SAASA,QAAQ,OAAO;QACrE,OAAOd,mBAAmB,CAACc,IAAI;IACjC;IACA,OAAO;AACT;AAEA,eAAeG,wBACbd,YAAoB,EACpBe,YAAoB;IAEpB,MAAMC,QACJD,iBAAiB,YACb,uCACAE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBhB,YAAYC,IAAI,GAChBD,YAAYE,SAAS;IAC3B,MAAMe,OAAO,CAAC;;;;oBAII,EAAEnB,KAAKC,SAAS,CAACW,eAAeb,eAAe;2BACxC,EAAEC,KAAKC,SAAS,CACvC,AAAC,CAAA,MAAMP,GAAG0B,QAAQ,CAACC,QAAQ,CAACtB,aAAY,EAAGuB,QAAQ,CAAC,WACpD;;;;;;;uBAOmB,EAAEtB,KAAKC,SAAS,CAACc,OAAO;;;;;;AAM/C,CAAC;IACC,OAAOI;AACT;AAEA,SAASI,wBAAwBxB,YAAoB;IACnD,OAAO,CAAC;;;oBAGU,EAAEC,KAAKC,SAAS,CAACF,cAAc;;;oBAG/B,EAAEC,KAAKC,SAAS,CAACW,eAAeb,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACK,wBAAwBP,cAAcU,IAAI,EAAE;;AAE9E,EAAEX,kBAAkBC,cAAc;;;;;;;;;uBASX,EAAEC,KAAKC,SAAS,CAACC,YAAYG,UAAU,EAAE;;;;AAIhE,CAAC;AACD;AAEA,iCAAiC;AACjC,SAASmB,yBAAyBzB,YAAoB;IACpD,OAAO,CAAC;;;0BAGgB,EAAEC,KAAKC,SAAS,CAACF,cAAc;;;;;;;AAOzD,EAAED,kBAAkBC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;AAyBlC,CAAC;AACD;AAEA,eAAe0B,2BACb1B,YAAoB,EACpB2B,IAAY,EACZC,aAAyC;IAEzC,IAAIC,uBAAuB;IAE3B,MAAMC,cAAc,MAAMhC,4BACxBE,cACA4B;IAEF,iDAAiD;IACjD,MAAMG,gBAAgBD,YAAYE,MAAM,CACtC,CAACtB,OAASA,SAAS,aAAaA,SAAS;IAG3C,MAAMuB,sBAAsBH,YAAYI,QAAQ,CAAC;IACjD,IACEjB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBc,uBACAN,KAAKO,QAAQ,CAAC,sBACd;QACAL,uBAAuB,CAAC;;;;;;;;;;;IAWxB,CAAC;IACH;IAEA,MAAMT,OAAO,CAAC;;0BAEU,EAAEnB,KAAKC,SAAS,CAACF,cAAc;;;;;;oBAMrC,EAAEC,KAAKC,SAAS,CAACW,eAAeb,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACK,wBAAwBP,cAAcU,IAAI,EAAE;;AAE9E,EAAEX,kBAAkBC,cAAc;;AAElC,EAAE,GAAG,wCAAwC,IAAG;AAChD,EACE+B,cAAcI,MAAM,GAAG,IACnB,CAAC,SAAS,EAAEJ,cAAcK,IAAI,CAAC,MAAM,QAAQ,EAAEnC,KAAKC,SAAS,CAC3DF,cACA,EAAE,CAAC,GACL,GACL;;;;EAIC,EACE,GAAG,2FAA2F,IAC/F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAkCoB,EAAEC,KAAKC,SAAS,CAACC,YAAYG,UAAU,EAAE;;;;;AAKhE,EAAEuB,qBAAqB;AACvB,CAAC;IACC,OAAOT;AACT;AAEA,gFAAgF;AAChF,oDAAoD;AACpD,MAAMiB,2BACJ;IACE,MAAM,EAAEV,IAAI,EAAEW,SAAS,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAACC,UAAU;IACrD,MAAM,EAAE9B,MAAMK,YAAY,EAAE,GAAGR,wBAAwBgC;IACvD,IAAI,CAACE,aAAa,CAACF;IAEnB,IAAInB,OAAO;IACX,IAAIkB,cAAc,KAAK;QACrB,IAAIvB,iBAAiB,YAAYA,iBAAiB,YAAY;YAC5DK,OAAOI,wBAAwBe;QACjC,OAAO,IAAIxB,iBAAiB,WAAW;YACrCK,OAAO,MAAMM,2BAA2Ba,UAAUZ,MAAM,IAAI;QAC9D,OAAO;YACLP,OAAOK,yBAAyBc;QAClC;IACF,OAAO;QACLnB,OAAO,MAAMN,wBAAwByB,UAAUxB;IACjD;IAEA,OAAOK;AACT;AAEF,eAAeiB,yBAAwB"}