{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-action-entry-loader.ts"], "names": ["generateActionId", "nextFlightActionEntryLoader", "actions", "getOptions", "actionList", "JSON", "parse", "individualActions", "map", "path", "names", "name", "id", "flat", "stringify", "join"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,UAAS;AAM1C,SAASC;IACP,MAAM,EAAEC,OAAO,EAAE,GAAuC,IAAI,CAACC,UAAU;IAEvE,MAAMC,aAAaC,KAAKC,KAAK,CAACJ;IAC9B,MAAMK,oBAAoBH,WACvBI,GAAG,CAAC,CAAC,CAACC,MAAMC,MAAM;QACjB,OAAOA,MAAMF,GAAG,CAAC,CAACG;YAChB,MAAMC,KAAKZ,iBAAiBS,MAAME;YAClC,OAAO;gBAACC;gBAAIH;gBAAME;aAAK;QACzB;IACF,GACCE,IAAI;IAEP,OAAO,CAAC;;AAEV,EAAEN,kBACCC,GAAG,CAAC,CAAC,CAACI,IAAIH,MAAME,KAAK;QACpB,OAAO,CAAC,CAAC,EAAEC,GAAG,2CAA2C,EAAEP,KAAKS,SAAS,CACvEL,MACA,kBAAkB,EAAEJ,KAAKS,SAAS,CAACH,MAAM,GAAG,CAAC;IACjD,GACCI,IAAI,CAAC,MAAM;;;;;;;;;;AAUd,EAAER,kBACCC,GAAG,CAAC,CAAC,CAACI,GAAG;QACR,OAAO,CAAC,GAAG,EAAEA,GAAG,wBAAwB,EAAEA,GAAG,GAAG,CAAC;IACnD,GACCG,IAAI,CAAC,MAAM;;AAEd,CAAC;AACD;AAEA,eAAed,4BAA2B"}