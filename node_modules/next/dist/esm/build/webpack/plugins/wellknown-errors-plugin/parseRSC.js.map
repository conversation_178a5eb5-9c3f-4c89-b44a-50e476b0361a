{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseRSC.ts"], "names": ["getModuleTrace", "formatModuleTrace", "SimpleWebpackError", "formatRSCErrorMessage", "message", "isPagesDir", "fileName", "formattedMessage", "formattedVerboseMessage", "NEXT_RSC_ERR_REACT_API", "NEXT_RSC_ERR_SERVER_IMPORT", "NEXT_RSC_ERR_CLIENT_IMPORT", "NEXT_RSC_ERR_CLIENT_METADATA_EXPORT", "NEXT_RSC_ERR_CONFLICT_METADATA_EXPORT", "NEXT_RSC_ERR_CLIENT_DIRECTIVE", "NEXT_RSC_ERR_CLIENT_DIRECTIVE_PAREN", "NEXT_RSC_ERR_INVALID_API", "NEXT_RSC_ERR_ERROR_FILE_SERVER_COMPONENT", "test", "matches", "match", "replace", "shouldAddUseClient", "getRscError", "err", "module", "compilation", "compiler", "moduleTrace", "formattedError", "formattedModuleTrace", "lastInternalFileName", "invalidImportMessage", "error", "stack"], "mappings": "AAEA,SAASA,cAAc,EAAEC,iBAAiB,QAAQ,mBAAkB;AACpE,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,SAASC,sBACPC,OAAe,EACfC,UAAmB,EACnBC,QAAgB;IAEhB,IAAIC,mBAAmBH;IACvB,IAAII,0BAA0B;IAE9B,oEAAoE;IACpE,2BAA2B;IAC3B,MAAMC,yBAAyB;IAC/B,MAAMC,6BAA6B;IACnC,MAAMC,6BAA6B;IACnC,MAAMC,sCACJ;IACF,MAAMC,wCACJ;IACF,MAAMC,gCAAgC;IACtC,MAAMC,sCACJ;IACF,MAAMC,2BAA2B;IACjC,MAAMC,2CACJ;IAEF,IAAIR,uBAAuBS,IAAI,CAACd,UAAU;QACxC,MAAMe,UAAUf,QAAQgB,KAAK,CAACX;QAC9B,IAAIU,WAAWA,OAAO,CAAC,EAAE,KAAK,aAAa;YACzCZ,mBAAmB,CAAC,sQAAsQ,CAAC;QAC7R,OAAO;YACLA,mBAAmBH,QAAQiB,OAAO,CAChCZ,wBACA,CAAC,4PAA4P,CAAC;QAElQ;QACAD,0BACE;IACJ,OAAO,IAAIE,2BAA2BQ,IAAI,CAACd,UAAU;QACnD,IAAIkB,qBAAqB;QACzB,MAAMH,UAAUf,QAAQgB,KAAK,CAACV;QAC9B,OAAQS,WAAWA,OAAO,CAAC,EAAE;YAC3B,KAAK;gBACH,qEAAqE;gBACrEZ,mBAAmB,CAAC,2OAA2O,CAAC;gBAChQ;YACF,KAAK;gBACH,4EAA4E;gBAC5EA,mBAAmB,CAAC,kKAAkK,CAAC;gBACvLe,qBAAqB;gBACrB;YACF;gBACEf,mBAAmBH,QAAQiB,OAAO,CAChCX,4BACA,CAAC,8PAA8P,CAAC;QAEtQ;QACAF,0BAA0Bc,qBACtB,8EACA;IACN,OAAO,IAAIX,2BAA2BO,IAAI,CAACd,UAAU;QACnD,IAAIC,YAAY;YACdE,mBAAmBH,QAAQiB,OAAO,CAChCV,4BACA,CAAC,+NAA+N,CAAC;YAEnOH,0BAA0B;QAC5B,OAAO;YACLD,mBAAmBH,QAAQiB,OAAO,CAChCV,4BACA,CAAC,+OAA+O,CAAC;YAEnPH,0BACE;QACJ;IACF,OAAO,IAAIM,8BAA8BI,IAAI,CAACd,UAAU;QACtDG,mBAAmBH,QAAQiB,OAAO,CAChCP,+BACA,CAAC,iIAAiI,CAAC;QAErIN,0BAA0B;IAC5B,OAAO,IAAIO,oCAAoCG,IAAI,CAACd,UAAU;QAC5DG,mBAAmBH,QAAQiB,OAAO,CAChCN,qCACA,CAAC,8PAA8P,CAAC;QAElQP,0BAA0B;IAC5B,OAAO,IAAIQ,yBAAyBE,IAAI,CAACd,UAAU;QACjDG,mBAAmBH,QAAQiB,OAAO,CAChCL,0BACA,CAAC,qHAAqH,CAAC;QAEzHR,0BAA0B;IAC5B,OAAO,IAAIS,yCAAyCC,IAAI,CAACd,UAAU;QACjEG,mBAAmBH,QAAQiB,OAAO,CAChCJ,0CACA,CAAC,IAAI,EAAEX,SAAS,kMAAkM,CAAC;QAErNE,0BAA0B;IAC5B,OAAO,IAAII,oCAAoCM,IAAI,CAACd,UAAU;QAC5DG,mBAAmBH,QAAQiB,OAAO,CAChCT,qCACA,CAAC,+PAA+P,CAAC;QAGnQJ,0BAA0B;IAC5B,OAAO,IAAIK,sCAAsCK,IAAI,CAACd,UAAU;QAC9DG,mBAAmBH,QAAQiB,OAAO,CAChCR,uCACA,CAAC,sLAAsL,CAAC;QAG1LL,0BAA0B;IAC5B;IAEA,OAAO;QAACD;QAAkBC;KAAwB;AACpD;AAEA,yEAAyE;AACzE,4DAA4D;AAC5D,OAAO,SAASe,YACdjB,QAAgB,EAChBkB,GAAU,EACVC,MAAW,EACXC,WAAgC,EAChCC,QAA0B;IAE1B,IAAI,CAACH,IAAIpB,OAAO,IAAI,CAAC,gBAAgBc,IAAI,CAACM,IAAIpB,OAAO,GAAG;QACtD,OAAO;IACT;IAEA,MAAM,EAAEC,UAAU,EAAEuB,WAAW,EAAE,GAAG5B,eAClCyB,QACAC,aACAC;IAGF,MAAME,iBAAiB1B,sBACrBqB,IAAIpB,OAAO,EACXC,YACAC;IAGF,MAAM,EAAEwB,oBAAoB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAE,GACxE/B,kBAAkB0B,UAAUC;IAE9B,MAAMK,QAAQ,IAAI/B,mBAChB6B,sBACA,kCACEF,cAAc,CAAC,EAAE,GACjBG,uBACAH,cAAc,CAAC,EAAE,GACjBC;IAGJ,8CAA8C;IAC9CG,MAAMC,KAAK,GAAG;IAEd,OAAOD;AACT"}