{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNotFoundError.ts"], "names": ["bold", "cyan", "green", "red", "yellow", "SimpleWebpackError", "createOriginalStackFrame", "getModuleTrace", "input", "compilation", "visitedModules", "Set", "moduleTrace", "current", "module", "has", "add", "origin", "moduleGraph", "get<PERSON><PERSON><PERSON>", "push", "getSourceFrame", "fileName", "result", "loc", "dependencies", "map", "d", "filter", "Boolean", "originalSource", "source", "rootDirectory", "options", "context", "modulePath", "frame", "arguments", "file", "methodName", "lineNumber", "start", "line", "column", "originalCodeFrame", "originalStackFrame", "toString", "getFormattedFileName", "loaders", "find", "loader", "test", "JSON", "parse", "resourceResolveData", "query", "slice", "path", "formattedFileName", "getNotFoundError", "name", "message", "errorMessage", "error", "replace", "importTrace", "readableIdentifier", "requestShortener", "length", "join", "err", "getImageError", "page", "rawRequest", "importedFile", "buffer", "split", "some", "includes", "concat"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,QAAQ,6BAA4B;AAC3E,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SAASC,wBAAwB,QAAQ,oEAAmE;AAG5G,6IAA6I;AAC7I;;;;;;;;;;;;;;;;;;;;;;AAsBA,GACA,SAASC,eAAeC,KAAU,EAAEC,WAAgB;IAClD,MAAMC,iBAAiB,IAAIC;IAC3B,MAAMC,cAAc,EAAE;IACtB,IAAIC,UAAUL,MAAMM,MAAM;IAC1B,MAAOD,QAAS;QACd,IAAIH,eAAeK,GAAG,CAACF,UAAU,OAAM,mDAAmD;QAC1FH,eAAeM,GAAG,CAACH;QACnB,MAAMI,SAASR,YAAYS,WAAW,CAACC,SAAS,CAACN;QACjD,IAAI,CAACI,QAAQ;QACbL,YAAYQ,IAAI,CAAC;YAAEH;YAAQH,QAAQD;QAAQ;QAC3CA,UAAUI;IACZ;IAEA,OAAOL;AACT;AAEA,eAAeS,eACbb,KAAU,EACVc,QAAa,EACbb,WAAgB;IAEhB,IAAI;YAoBYc,uCAAAA,4BACJA,mCAAAA;QApBV,MAAMC,MACJhB,MAAMgB,GAAG,IAAIhB,MAAMiB,YAAY,CAACC,GAAG,CAAC,CAACC,IAAWA,EAAEH,GAAG,EAAEI,MAAM,CAACC,QAAQ,CAAC,EAAE;QAC3E,MAAMC,iBAAiBtB,MAAMM,MAAM,CAACgB,cAAc;QAElD,MAAMP,SAAS,MAAMjB,yBAAyB;YAC5CyB,QAAQD;YACRE,eAAevB,YAAYwB,OAAO,CAACC,OAAO;YAC1CC,YAAYb;YACZc,OAAO;gBACLC,WAAW,EAAE;gBACbC,MAAMhB;gBACNiB,YAAY;gBACZC,YAAYhB,IAAIiB,KAAK,CAACC,IAAI;gBAC1BC,QAAQnB,IAAIiB,KAAK,CAACE,MAAM;YAC1B;QACF;QAEA,OAAO;YACLP,OAAOb,CAAAA,0BAAAA,OAAQqB,iBAAiB,KAAI;YACpCJ,YAAYjB,CAAAA,2BAAAA,6BAAAA,OAAQsB,kBAAkB,sBAA1BtB,wCAAAA,2BAA4BiB,UAAU,qBAAtCjB,sCAAwCuB,QAAQ,OAAM;YAClEH,QAAQpB,CAAAA,2BAAAA,8BAAAA,OAAQsB,kBAAkB,sBAA1BtB,oCAAAA,4BAA4BoB,MAAM,qBAAlCpB,kCAAoCuB,QAAQ,OAAM;QAC5D;IACF,EAAE,OAAM;QACN,OAAO;YAAEV,OAAO;YAAII,YAAY;YAAIG,QAAQ;QAAG;IACjD;AACF;AAEA,SAASI,qBACPzB,QAAgB,EAChBR,MAAW,EACX0B,UAAmB,EACnBG,MAAe;QAGb7B;IADF,KACEA,kBAAAA,OAAOkC,OAAO,qBAAdlC,gBAAgBmC,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;QACA,mFAAmF;QACnF,2CAA2C;QAC3C,OAAOE,KAAKC,KAAK,CAACvC,OAAOwC,mBAAmB,CAACC,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;IACnE,OAAO;QACL,IAAIC,oBAA4BzD,KAAKqB;QACrC,IAAIkB,cAAcG,QAAQ;YACxBe,qBAAqB,CAAC,CAAC,EAAEtD,OAAOoC,YAAY,CAAC,EAAEpC,OAAOuC,QAAQ,CAAC;QACjE;QAEA,OAAOe;IACT;AACF;AAEA,OAAO,eAAeC,iBACpBlD,WAAgC,EAChCD,KAAU,EACVc,QAAgB,EAChBR,MAAW;IAEX,IACEN,MAAMoD,IAAI,KAAK,yBACf,CACEpD,CAAAA,MAAMoD,IAAI,KAAK,sBACf,gCAAgCT,IAAI,CAAC3C,MAAMqD,OAAO,CAAA,GAEpD;QACA,OAAO;IACT;IAEA,IAAI;QACF,MAAM,EAAEzB,KAAK,EAAEI,UAAU,EAAEG,MAAM,EAAE,GAAG,MAAMtB,eAC1Cb,OACAc,UACAb;QAGF,MAAMqD,eAAetD,MAAMuD,KAAK,CAACF,OAAO,CACrCG,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,wBAAwB,CAAC,eAAe,EAAE9D,MAAM,MAAM,CAAC,CAAC;QAEnE,MAAM+D,cAAc;YAClB,MAAMrD,cAAcL,eAAeC,OAAOC,aACvCiB,GAAG,CAAC,CAAC,EAAET,MAAM,EAAE,GACdA,OAAOiD,kBAAkB,CAACzD,YAAY0D,gBAAgB,GAEvDvC,MAAM,CACL,CAACgC,OACCA,QACA,CAAC,0FAA0FT,IAAI,CAC7FS,SAEF,CAAC,+BAA+BT,IAAI,CAACS,SACrC,CAAC,mBAAmBT,IAAI,CAACS;YAE/B,IAAIhD,YAAYwD,MAAM,KAAK,GAAG,OAAO;YAErC,OAAO,CAAC,sCAAsC,EAAExD,YAAYyD,IAAI,CAAC,MAAM,CAAC;QAC1E;QAEA,IAAIR,UACF1D,IAAIH,KAAK,uBACT,CAAC,EAAE,EAAE8D,aAAa,CAAC,GACnB,OACA1B,QACCA,CAAAA,UAAU,KAAK,OAAO,EAAC,IACxB,0DACA6B;QAEF,MAAMP,oBAAoBX,qBACxBzB,UACAR,QACA0B,YACAG;QAGF,OAAO,IAAItC,mBAAmBqD,mBAAmBG;IACnD,EAAE,OAAOS,KAAK;QACZ,8CAA8C;QAC9C,OAAO9D;IACT;AACF;AAEA,OAAO,eAAe+D,cACpB9D,WAAgB,EAChBD,KAAU,EACV8D,GAAU;IAEV,IAAIA,IAAIV,IAAI,KAAK,2BAA2B;QAC1C,OAAO;IACT;IAEA,MAAMhD,cAAcL,eAAeC,OAAOC;IAC1C,MAAM,EAAEQ,MAAM,EAAEH,MAAM,EAAE,GAAGF,WAAW,CAAC,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACK,UAAU,CAACH,QAAQ;QACtB,OAAO;IACT;IACA,MAAM0D,OAAOvD,OAAOwD,UAAU,CAACT,OAAO,CAAC,uBAAuB;IAC9D,MAAMU,eAAe5D,OAAO2D,UAAU;IACtC,MAAM1C,SAASd,OAAOa,cAAc,GAAG6C,MAAM,GAAG7B,QAAQ,CAAC;IACzD,IAAIN,aAAa,CAAC;IAClBT,OAAO6C,KAAK,CAAC,MAAMC,IAAI,CAAC,CAACnC;QACvBF;QACA,OAAOE,KAAKoC,QAAQ,CAACJ;IACvB;IACA,OAAO,IAAIrE,mBACT,CAAC,EAAEJ,KAAKuE,MAAM,CAAC,EAAEpE,OAAOoC,WAAWM,QAAQ,IAAI,CAAC,EAChD3C,IAAIH,KAAK,UAAU+E,MAAM,CACvB,CAAC,gBAAgB,EAAEL,aAAa,iFAAiF,CAAC;AAGxH"}