{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-client-entry-plugin.ts"], "names": ["webpack", "stringify", "path", "sources", "getInvalidator", "getEntries", "EntryTypes", "getEntry<PERSON>ey", "WEBPACK_LAYERS", "APP_CLIENT_INTERNALS", "BARREL_OPTIMIZATION_PREFIX", "COMPILER_NAMES", "DEFAULT_RUNTIME_WEBPACK", "EDGE_RUNTIME_WEBPACK", "SERVER_REFERENCE_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "getActions", "generateActionId", "isClientComponentEntryModule", "isCSSMod", "regexCSS", "traverseModules", "forEachEntryModule", "formatBarrelOptimizedResource", "getModuleReferencesInOrder", "normalizePathSep", "getProxiedPluginState", "PAGE_TYPES", "isWebpackServerOnlyLayer", "getModuleBuildInfo", "getAssumedSourceType", "PLUGIN_NAME", "pluginState", "serverActions", "edgeServerActions", "actionModServerId", "actionModEdgeServerId", "serverModuleIds", "edgeServerModuleIds", "ASYNC_CLIENT_MODULES", "injectedClientEntries", "deduplicateCSSImportsForEntry", "mergedCSSimports", "sortedCSSImports", "Object", "entries", "sort", "a", "b", "a<PERSON><PERSON>", "bPath", "a<PERSON><PERSON><PERSON>", "split", "length", "b<PERSON><PERSON><PERSON>", "aName", "parse", "name", "bName", "indexA", "indexOf", "indexB", "dedupedCSSImports", "trackedCSSImports", "Set", "entryName", "cssImports", "cssImport", "has", "filename", "includes", "add", "push", "FlightClientEntryPlugin", "constructor", "options", "dev", "appDir", "isEdgeServer", "assetPrefix", "<PERSON><PERSON><PERSON>", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "finishMake", "tapPromise", "createClientEntries", "afterCompile", "recordModule", "modId", "mod", "modPath", "matchResource", "resourceResolveData", "mod<PERSON><PERSON><PERSON>", "query", "modResource", "startsWith", "resource", "layer", "serverSideRendering", "ssrNamedModuleId", "relative", "context", "replace", "_chunk", "_chunkGroup", "moduleGraph", "isAsync", "make", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createActionAssets", "addClientEntryAndSSRModulesList", "createdSSRDependenciesForEntry", "addActionEntryList", "actionMapsPerEntry", "createdActions", "entryModule", "internalClientComponentEntryImports", "actionEntryImports", "Map", "clientEntriesToInject", "connection", "entryRequest", "dependency", "request", "clientComponentImports", "actionImports", "collectComponentInfoFromServerEntryDependency", "resolvedModule", "for<PERSON>ach", "dep", "names", "isAbsoluteRequest", "isAbsolute", "keys", "value", "relativeRequest", "bundlePath", "assign", "absolutePagePath", "clientEntryToInject", "injected", "injectClientEntryAndSSRModules", "clientImports", "reduce", "res", "curr", "size", "actionNames", "actionName", "injectActionEntry", "actions", "finishModules", "addedClientActionEntryList", "actionMapsPerClientEntry", "ssrEntryDependencies", "collectClientActionsFromDependencies", "remainingClientImportedActions", "remainingActionEntryImports", "remainingActionNames", "id", "fromClient", "Promise", "all", "invalidator", "outputPath", "some", "shouldInvalidate", "invalidate", "client", "map", "addClientEntryAndSSRModules", "collectedActions", "visitedModule", "visitedEntry", "collectActions", "collectActionsInDep", "modRequest", "entryDependency", "ssrEntryModule", "getResolvedModule", "visited", "CSSImports", "filterClientComponents", "importedIdentifiers", "isCSS", "_identifier", "addClientImport", "webpackRuntime", "sideEffectFree", "factoryMeta", "unused", "getExportsInfo", "isModuleUsed", "dependencyIds", "depModule", "ids", "Array", "from", "loaderOptions", "modules", "test", "localeCompare", "clientImportPath", "server", "clientBrowserLoader", "sep", "x", "JSON", "clientSSRLoader", "page<PERSON><PERSON>", "APP", "type", "CHILD_ENTRY", "parentEntries", "absoluteEntryFilePath", "dispose", "lastActiveTime", "Date", "now", "entryData", "clientComponentEntryDep", "EntryPlugin", "createDependency", "addEntry", "actionsArray", "actionLoader", "__client_imported__", "currentCompilerServerActions", "p", "workers", "<PERSON><PERSON><PERSON><PERSON>", "reactServerComponents", "actionEntryDep", "resolve", "reject", "entry", "get", "includeDependencies", "call", "addModuleTree", "contextInfo", "issuer<PERSON><PERSON>er", "err", "module", "failedEntry", "<PERSON><PERSON><PERSON><PERSON>", "chunkGroup", "mapping", "action", "json", "node", "edge", "undefined", "RawSource", "isFirstImport", "clientEntryType", "rsc", "isCjsModule", "assumedSourceType", "clientImportsSet", "isAutoModuleSourceType", "isCjsDefaultImport"], "mappings": "AAMA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,SAAS,QAAQ,cAAa;AACvC,OAAOC,UAAU,OAAM;AACvB,SAASC,OAAO,QAAQ,qCAAoC;AAC5D,SACEC,cAAc,EACdC,UAAU,EACVC,UAAU,EACVC,WAAW,QACN,8CAA6C;AACpD,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,cAAc,EACdC,uBAAuB,EACvBC,oBAAoB,EACpBC,yBAAyB,EACzBC,gCAAgC,QAC3B,gCAA+B;AACtC,SACEC,UAAU,EACVC,gBAAgB,EAChBC,4BAA4B,EAC5BC,QAAQ,EACRC,QAAQ,QACH,mBAAkB;AACzB,SACEC,eAAe,EACfC,kBAAkB,EAClBC,6BAA6B,EAC7BC,0BAA0B,QACrB,WAAU;AACjB,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,qBAAqB,QAAQ,sBAAqB;AAC3D,SAASC,UAAU,QAAQ,0BAAyB;AACpD,SAASC,wBAAwB,QAAQ,cAAa;AACtD,SAASC,kBAAkB,QAAQ,mCAAkC;AACrE,SAASC,oBAAoB,QAAQ,gCAA+B;AASpE,MAAMC,cAAc;AAqBpB,MAAMC,cAAcN,sBAAsB;IACxC,gDAAgD;IAChDO,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,mBAAmB,CAAC;IAOpBC,uBAAuB,CAAC;IAQxB,gEAAgE;IAChEC,iBAAiB,CAAC;IAClBC,qBAAqB,CAAC;IAEtB,6DAA6D;IAC7D,wEAAwE;IACxE,qFAAqF;IACrF,uCAAuC;IACvCC,sBAAsB,CAAC;IAEvBC,uBAAuB,CAAC;AAC1B;AAEA,SAASC,8BAA8BC,gBAA4B;IACjE,uEAAuE;IACvE,oEAAoE;IACpE,wEAAwE;IACxE,+DAA+D;IAC/D,sEAAsE;IACtE,uEAAuE;IACvE,wEAAwE;IACxE,UAAU;IACV,qEAAqE;IACrE,qEAAqE;IACrE,mEAAmE;IACnE,yEAAyE;IACzE,uFAAuF;IAEvF,2CAA2C;IAC3C,MAAMC,mBAAmBC,OAAOC,OAAO,CAACH,kBAAkBI,IAAI,CAAC,CAACC,GAAGC;QACjE,MAAM,CAACC,MAAM,GAAGF;QAChB,MAAM,CAACG,MAAM,GAAGF;QAEhB,MAAMG,SAASF,MAAMG,KAAK,CAAC,KAAKC,MAAM;QACtC,MAAMC,SAASJ,MAAME,KAAK,CAAC,KAAKC,MAAM;QAEtC,IAAIF,WAAWG,QAAQ;YACrB,OAAOH,SAASG;QAClB;QAEA,MAAMC,QAAQrD,KAAKsD,KAAK,CAACP,OAAOQ,IAAI;QACpC,MAAMC,QAAQxD,KAAKsD,KAAK,CAACN,OAAOO,IAAI;QAEpC,MAAME,SAAS;YAAC;YAAY;SAAS,CAACC,OAAO,CAACL;QAC9C,MAAMM,SAAS;YAAC;YAAY;SAAS,CAACD,OAAO,CAACF;QAE9C,IAAIC,WAAW,CAAC,GAAG,OAAO;QAC1B,IAAIE,WAAW,CAAC,GAAG,OAAO,CAAC;QAC3B,OAAOF,SAASE;IAClB;IAEA,MAAMC,oBAAgC,CAAC;IACvC,MAAMC,oBAAoB,IAAIC;IAC9B,KAAK,MAAM,CAACC,WAAWC,WAAW,IAAIvB,iBAAkB;QACtD,KAAK,MAAMwB,aAAaD,WAAY;YAClC,IAAIH,kBAAkBK,GAAG,CAACD,YAAY;YAEtC,iEAAiE;YACjE,MAAME,WAAWnE,KAAKsD,KAAK,CAACS,WAAWR,IAAI;YAC3C,IAAI;gBAAC;gBAAY;aAAS,CAACa,QAAQ,CAACD,WAAW;gBAC7CN,kBAAkBQ,GAAG,CAACJ;YACxB;YAEA,IAAI,CAACL,iBAAiB,CAACG,UAAU,EAAE;gBACjCH,iBAAiB,CAACG,UAAU,GAAG,EAAE;YACnC;YACAH,iBAAiB,CAACG,UAAU,CAACO,IAAI,CAACL;QACpC;IACF;IAEA,OAAOL;AACT;AAEA,OAAO,MAAMW;IAOXC,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,YAAY,GAAGH,QAAQG,YAAY;QACxC,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACH,GAAG,IAAI,CAAC,IAAI,CAACE,YAAY,GAAG,QAAQ;QAC7D,IAAI,CAACE,aAAa,GAAGL,QAAQK,aAAa;IAC5C;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5BtD,aACA,CAACqD,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjCxF,QAAQyF,YAAY,CAACC,gBAAgB,EACrCJ;YAEFF,YAAYO,mBAAmB,CAACH,GAAG,CACjCxF,QAAQyF,YAAY,CAACC,gBAAgB,EACrC,IAAI1F,QAAQyF,YAAY,CAACG,cAAc,CAACC,QAAQ;QAEpD;QAGFX,SAASC,KAAK,CAACW,UAAU,CAACC,UAAU,CAAChE,aAAa,CAACqD,cACjD,IAAI,CAACY,mBAAmB,CAACd,UAAUE;QAGrCF,SAASC,KAAK,CAACc,YAAY,CAACZ,GAAG,CAACtD,aAAa,CAACqD;YAC5C,MAAMc,eAAe,CAACC,OAAeC;oBAGEA,0BACpBA;gBAHjB,yFAAyF;gBACzF,2DAA2D;gBAC3D,MAAMC,UAAUD,IAAIE,aAAa,MAAIF,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBlG,IAAI;gBAClE,MAAMsG,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;gBACnD,wCAAwC;gBACxC,gFAAgF;gBAChF,MAAMC,cAAcL,UAChBA,QAAQM,UAAU,CAACjG,8BACjBa,8BAA8B6E,IAAIQ,QAAQ,EAAEP,WAC5CA,UAAUG,WACZJ,IAAIQ,QAAQ;gBAEhB,IAAIR,IAAIS,KAAK,KAAKrG,eAAesG,mBAAmB,EAAE;oBACpD;gBACF;gBAEA,yHAAyH;gBACzH,IAAI,OAAOX,UAAU,eAAeO,aAAa;oBAC/C,4EAA4E;oBAC5E,6EAA6E;oBAC7E,sBAAsB;oBACtB,IAAIK,mBAAmB7G,KAAK8G,QAAQ,CAAC9B,SAAS+B,OAAO,EAAEP;oBAEvD,IAAI,CAACK,iBAAiBJ,UAAU,CAAC,MAAM;wBACrC,+BAA+B;wBAC/BI,mBAAmB,CAAC,EAAE,EAAEtF,iBAAiBsF,kBAAkB,CAAC;oBAC9D;oBAEA,IAAI,IAAI,CAACjC,YAAY,EAAE;wBACrB9C,YAAYM,mBAAmB,CAC7ByE,iBAAiBG,OAAO,CAAC,uBAAuB,eACjD,GAAGf;oBACN,OAAO;wBACLnE,YAAYK,eAAe,CAAC0E,iBAAiB,GAAGZ;oBAClD;gBACF;YACF;YAEA9E,gBAAgB+D,aAAa,CAACgB,KAAKe,QAAQC,aAAajB;gBACtD,IAAIC,OAAOA,IAAIQ,QAAQ,IAAI,CAAChF,yBAAyBwE,IAAIS,KAAK,GAAG;oBAC/D,IAAIzB,YAAYiC,WAAW,CAACC,OAAO,CAAClB,MAAM;wBACxC,0FAA0F;wBAC1F,4FAA4F;wBAC5FpE,YAAYO,oBAAoB,CAAC6D,IAAIQ,QAAQ,CAAC,GAAG;oBACnD;gBACF;gBAEA,IAAIT,OAAOD,aAAaC,OAAOC;YACjC;QACF;QAEAlB,SAASC,KAAK,CAACoC,IAAI,CAAClC,GAAG,CAACtD,aAAa,CAACqD;YACpCA,YAAYD,KAAK,CAACqC,aAAa,CAACzB,UAAU,CACxC;gBACEtC,MAAM1B;gBACN0F,OAAOzH,QAAQ0H,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,kBAAkB,CAACzC,aAAawC;QAErD;IACF;IAEA,MAAM5B,oBACJd,QAA0B,EAC1BE,WAAgC,EAChC;QACA,MAAM0C,kCAEF,EAAE;QACN,MAAMC,iCAGF,CAAC;QAEL,MAAMC,qBACJ,EAAE;QACJ,MAAMC,qBAA4D,CAAC;QACnE,MAAMC,iBAAiB,IAAIlE;QAE3B,4EAA4E;QAC5E,0BAA0B;QAC1B1C,mBAAmB8D,aAAa,CAAC,EAAE3B,IAAI,EAAE0E,WAAW,EAAE;YACpD,MAAMC,sCAA8D,CAAC;YACrE,MAAMC,qBAAqB,IAAIC;YAC/B,MAAMC,wBAAwB,EAAE;YAChC,MAAM7F,mBAA+B,CAAC;YAEtC,KAAK,MAAM8F,cAAchH,2BACvB2G,aACA/C,YAAYiC,WAAW,EACtB;gBACD,uFAAuF;gBACvF,MAAMoB,eAAe,AACnBD,WAAWE,UAAU,CACrBC,OAAO;gBAET,MAAM,EAAEC,sBAAsB,EAAEC,aAAa,EAAE3E,UAAU,EAAE,GACzD,IAAI,CAAC4E,6CAA6C,CAAC;oBACjDL;oBACArD;oBACA2D,gBAAgBP,WAAWO,cAAc;gBAC3C;gBAEFF,cAAcG,OAAO,CAAC,CAAC,CAACC,KAAKC,MAAM,GACjCb,mBAAmB7C,GAAG,CAACyD,KAAKC;gBAG9B,MAAMC,oBAAoBjJ,KAAKkJ,UAAU,CAACX;gBAE1C,mDAAmD;gBACnD,IAAI,CAACU,mBAAmB;oBACtBvG,OAAOyG,IAAI,CAACT,wBAAwBI,OAAO,CACzC,CAACM,QAAWlB,mCAAmC,CAACkB,MAAM,GAAG,IAAItF;oBAE/D;gBACF;gBAEA,2HAA2H;gBAC3H,4DAA4D;gBAC5D,kEAAkE;gBAClE,aAAa;gBACb,IAAI;gBAEJ,MAAMuF,kBAAkBJ,oBACpBjJ,KAAK8G,QAAQ,CAAC5B,YAAYT,OAAO,CAACsC,OAAO,EAAGwB,gBAC5CA;gBAEJ,8CAA8C;gBAC9C,MAAMe,aAAa/H,iBACjB8H,gBAAgBrC,OAAO,CAAC,eAAe,IAAIA,OAAO,CAAC,aAAa;gBAGlEtE,OAAO6G,MAAM,CAAC/G,kBAAkBwB;gBAChCqE,sBAAsB/D,IAAI,CAAC;oBACzBU;oBACAE;oBACAnB,WAAWR;oBACXmF;oBACAY;oBACAE,kBAAkBjB;gBACpB;gBAEA,uKAAuK;gBACvK,wGAAwG;gBACxG,8IAA8I;gBAC9I,IACEhF,SAAS,CAAC,GAAG,EAAE1C,iCAAiC,CAAC,IACjDyI,eAAe,iBACf;oBACAjB,sBAAsB/D,IAAI,CAAC;wBACzBU;wBACAE;wBACAnB,WAAWR;wBACXmF,wBAAwB,CAAC;wBACzBY,YAAY,CAAC,GAAG,EAAEzI,iCAAiC,CAAC;wBACpD2I,kBAAkBjB;oBACpB;gBACF;YACF;YAEA,2EAA2E;YAC3E,mBAAmB;YACnB,MAAM3E,oBAAoBrB,8BAA8BC;YACxD,KAAK,MAAMiH,uBAAuBpB,sBAAuB;gBACvD,MAAMqB,WAAW,IAAI,CAACC,8BAA8B,CAAC;oBACnD,GAAGF,mBAAmB;oBACtBG,eAAe;wBACb,GAAGH,oBAAoBf,sBAAsB;wBAC7C,GAAG,AACD9E,CAAAA,iBAAiB,CAAC6F,oBAAoBD,gBAAgB,CAAC,IAAI,EAAE,AAAD,EAC5DK,MAAM,CAAC,CAACC,KAAKC;4BACbD,GAAG,CAACC,KAAK,GAAG,IAAIjG;4BAChB,OAAOgG;wBACT,GAAG,CAAC,EAA4B;oBAClC;gBACF;gBAEA,2EAA2E;gBAC3E,IAAI,CAACjC,8BAA8B,CAAC4B,oBAAoB1F,SAAS,CAAC,EAAE;oBAClE8D,8BAA8B,CAAC4B,oBAAoB1F,SAAS,CAAC,GAAG,EAAE;gBACpE;gBACA8D,8BAA8B,CAAC4B,oBAAoB1F,SAAS,CAAC,CAACO,IAAI,CAChEoF,QAAQ,CAAC,EAAE;gBAGb9B,gCAAgCtD,IAAI,CAACoF;YACvC;YAEA,sBAAsB;YACtB9B,gCAAgCtD,IAAI,CAClC,IAAI,CAACqF,8BAA8B,CAAC;gBAClC3E;gBACAE;gBACAnB,WAAWR;gBACXqG,eAAe;oBAAE,GAAG1B,mCAAmC;gBAAC;gBACxDoB,YAAY/I;YACd;YAGF,IAAI4H,mBAAmB6B,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACjC,kBAAkB,CAACxE,KAAK,EAAE;oBAC7BwE,kBAAkB,CAACxE,KAAK,GAAG,IAAI6E;gBACjC;gBACAL,kBAAkB,CAACxE,KAAK,GAAG,IAAI6E,IAAI;uBAC9BL,kBAAkB,CAACxE,KAAK;uBACxB4E;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAAC5E,MAAM4E,mBAAmB,IAAIzF,OAAOC,OAAO,CACrDoF,oBACC;YACD,KAAK,MAAM,CAACgB,KAAKkB,YAAY,IAAI9B,mBAAoB;gBACnD,KAAK,MAAM+B,cAAcD,YAAa;oBACpCjC,eAAe3D,GAAG,CAACd,OAAO,MAAMwF,MAAM,MAAMmB;gBAC9C;YACF;YACApC,mBAAmBxD,IAAI,CACrB,IAAI,CAAC6F,iBAAiB,CAAC;gBACrBnF;gBACAE;gBACAkF,SAASjC;gBACTpE,WAAWR;gBACX+F,YAAY/F;YACd;QAEJ;QAEA2B,YAAYD,KAAK,CAACoF,aAAa,CAACxE,UAAU,CAAChE,aAAa;YACtD,MAAMyI,6BAA6C,EAAE;YACrD,MAAMC,2BAAkE,CAAC;YAEzE,mEAAmE;YACnE,gBAAgB;YAChB,yEAAyE;YACzE,KAAK,MAAM,CAAChH,MAAMiH,qBAAqB,IAAI9H,OAAOC,OAAO,CACvDkF,gCACC;gBACD,qEAAqE;gBACrE,sBAAsB;gBACtB,MAAMM,qBAAqB,IAAI,CAACsC,oCAAoC,CAAC;oBACnEvF;oBACAK,cAAciF;gBAChB;gBAEA,IAAIrC,mBAAmB6B,IAAI,GAAG,GAAG;oBAC/B,IAAI,CAACO,wBAAwB,CAAChH,KAAK,EAAE;wBACnCgH,wBAAwB,CAAChH,KAAK,GAAG,IAAI6E;oBACvC;oBACAmC,wBAAwB,CAAChH,KAAK,GAAG,IAAI6E,IAAI;2BACpCmC,wBAAwB,CAAChH,KAAK;2BAC9B4E;qBACJ;gBACH;YACF;YAEA,KAAK,MAAM,CAAC5E,MAAM4E,mBAAmB,IAAIzF,OAAOC,OAAO,CACrD4H,0BACC;gBACD,uEAAuE;gBACvE,+CAA+C;gBAC/C,uEAAuE;gBACvE,mBAAmB;gBACnB,IAAIG,iCAAiC;gBACrC,MAAMC,8BAA8B,IAAIvC;gBACxC,KAAK,MAAM,CAACW,KAAKkB,YAAY,IAAI9B,mBAAoB;oBACnD,MAAMyC,uBAAuB,EAAE;oBAC/B,KAAK,MAAMV,cAAcD,YAAa;wBACpC,MAAMY,KAAKtH,OAAO,MAAMwF,MAAM,MAAMmB;wBACpC,IAAI,CAAClC,eAAe9D,GAAG,CAAC2G,KAAK;4BAC3BD,qBAAqBtG,IAAI,CAAC4F;wBAC5B;oBACF;oBACA,IAAIU,qBAAqBzH,MAAM,GAAG,GAAG;wBACnCwH,4BAA4BrF,GAAG,CAACyD,KAAK6B;wBACrCF,iCAAiC;oBACnC;gBACF;gBAEA,IAAIA,gCAAgC;oBAClCJ,2BAA2BhG,IAAI,CAC7B,IAAI,CAAC6F,iBAAiB,CAAC;wBACrBnF;wBACAE;wBACAkF,SAASO;wBACT5G,WAAWR;wBACX+F,YAAY/F;wBACZuH,YAAY;oBACd;gBAEJ;YACF;YAEA,MAAMC,QAAQC,GAAG,CAACV;YAClB;QACF;QAEA,qDAAqD;QACrD,MAAMW,cAAc/K,eAAe8E,SAASkG,UAAU;QACtD,4DAA4D;QAC5D,IACED,eACArD,gCAAgCuD,IAAI,CAClC,CAAC,CAACC,iBAAiB,GAAKA,qBAAqB,OAE/C;YACAH,YAAYI,UAAU,CAAC;gBAAC5K,eAAe6K,MAAM;aAAC;QAChD;QAEA,qGAAqG;QACrG,6EAA6E;QAC7E,MAAMP,QAAQC,GAAG,CACfpD,gCAAgC2D,GAAG,CACjC,CAACC,8BAAgCA,2BAA2B,CAAC,EAAE;QAInE,uCAAuC;QACvC,MAAMT,QAAQC,GAAG,CAAClD;IACpB;IAEA2C,qCAAqC,EACnCvF,WAAW,EACXK,YAAY,EAIb,EAAE;QACD,mCAAmC;QACnC,MAAMkG,mBAAmB,IAAIrD;QAE7B,gFAAgF;QAChF,MAAMsD,gBAAgB,IAAI5H;QAC1B,MAAM6H,eAAe,IAAI7H;QAEzB,MAAM8H,iBAAiB,CAAC,EACtBrD,YAAY,EACZM,cAAc,EAIf;YACC,MAAMgD,sBAAsB,CAAC3F;oBAGHA,0BAKXA,2BAMTA;gBAbJ,IAAI,CAACA,KAAK;gBAEV,MAAMC,UAAkBD,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBlG,IAAI,KAAI;gBACzD,mEAAmE;gBACnE,yEAAyE;gBACzE,0EAA0E;gBAC1E,IAAI8L,aACF3F,UAAWD,CAAAA,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI,EAAC;gBAEhD,yEAAyE;gBACzE,yEAAyE;gBACzE,0EAA0E;gBAC1E,wEAAwE;gBACxE,KAAIL,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBO,UAAU,CAACjG,6BAA6B;oBAC7DsL,aAAa5F,IAAIE,aAAa,GAAG,MAAM0F;gBACzC;gBAEA,IAAI,CAACA,cAAcJ,cAAcxH,GAAG,CAAC4H,aAAa;gBAClDJ,cAAcrH,GAAG,CAACyH;gBAElB,MAAM1B,UAAUtJ,WAAWoF;gBAC3B,IAAIkE,SAAS;oBACXqB,iBAAiBnG,GAAG,CAACwG,YAAY1B;gBACnC;gBAEA9I,2BAA2B4E,KAAKhB,YAAYiC,WAAW,EAAE2B,OAAO,CAC9D,CAACR;oBACCuD,oBACEvD,WAAWO,cAAc;gBAE7B;YAEJ;YAEA,yEAAyE;YACzE,IACEN,gBACA,CAACA,aAAanE,QAAQ,CAAC,oCACvB;gBACA,2DAA2D;gBAC3DyH,oBAAoBhD;YACtB;QACF;QAEA,KAAK,MAAMkD,mBAAmBxG,aAAc;YAC1C,MAAMyG,iBACJ9G,YAAYiC,WAAW,CAAC8E,iBAAiB,CAACF;YAC5C,KAAK,MAAMzD,cAAchH,2BACvB0K,gBACA9G,YAAYiC,WAAW,EACtB;gBACD,MAAMqB,aAAaF,WAAWE,UAAU;gBACxC,MAAMC,UAAU,AAACD,WAA+CC,OAAO;gBAEvE,oEAAoE;gBACpE,oEAAoE;gBACpE,IAAIkD,aAAazH,GAAG,CAACuE,UAAU;gBAC/BkD,aAAatH,GAAG,CAACoE;gBAEjBmD,eAAe;oBACbrD,cAAcE;oBACdI,gBAAgBP,WAAWO,cAAc;gBAC3C;YACF;QACF;QAEA,OAAO4C;IACT;IAEA7C,8CAA8C,EAC5CL,YAAY,EACZrD,WAAW,EACX2D,cAAc,EAKf,EAIC;QACA,gFAAgF;QAChF,MAAMqD,UAAU,IAAIpI;QAEpB,mBAAmB;QACnB,MAAM4E,yBAAiD,CAAC;QACxD,MAAMC,gBAAsC,EAAE;QAC9C,MAAMwD,aAAa,IAAIrI;QAEvB,MAAMsI,yBAAyB,CAC7BlG,KACAmG;gBAMwBnG,0BAKZA,2BAWRA;YApBJ,IAAI,CAACA,KAAK;YAEV,MAAMoG,QAAQrL,SAASiF;YAEvB,MAAMC,UAAkBD,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBlG,IAAI,KAAI;YACzD,mEAAmE;YACnE,yEAAyE;YACzE,0EAA0E;YAC1E,IAAI8L,aACF3F,YAAUD,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK;YAE1C,6EAA6E;YAC7E,IAAIL,IAAI1B,WAAW,CAACjB,IAAI,KAAK,iBAAiB;gBAC5CuI,aAAa,AAAC5F,IAAYqG,WAAW;YACvC;YAEA,yEAAyE;YACzE,yEAAyE;YACzE,0EAA0E;YAC1E,wEAAwE;YACxE,KAAIrG,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBO,UAAU,CAACjG,6BAA6B;gBAC7DsL,aAAa5F,IAAIE,aAAa,GAAG,MAAM0F;YACzC;YAEA,IAAI,CAACA,YAAY;YACjB,IAAII,QAAQhI,GAAG,CAAC4H,aAAa;gBAC3B,IAAIpD,sBAAsB,CAACoD,WAAW,EAAE;oBACtCU,gBACEtG,KACA4F,YACApD,wBACA2D,qBACA;gBAEJ;gBACA;YACF;YACAH,QAAQ7H,GAAG,CAACyH;YAEZ,MAAM1B,UAAUtJ,WAAWoF;YAC3B,IAAIkE,SAAS;gBACXzB,cAAcrE,IAAI,CAAC;oBAACwH;oBAAY1B;iBAAQ;YAC1C;YAEA,MAAMqC,iBAAiB,IAAI,CAAC7H,YAAY,GACpCjE,uBACAD;YAEJ,IAAI4L,OAAO;gBACT,MAAMI,iBACJxG,IAAIyG,WAAW,IAAI,AAACzG,IAAIyG,WAAW,CAASD,cAAc;gBAE5D,IAAIA,gBAAgB;oBAClB,MAAME,SAAS,CAAC1H,YAAYiC,WAAW,CACpC0F,cAAc,CAAC3G,KACf4G,YAAY,CAACL;oBAEhB,IAAIG,QAAQ;gBACd;gBAEAT,WAAW9H,GAAG,CAACyH;YACjB,OAAO,IAAI9K,6BAA6BkF,MAAM;gBAC5C,IAAI,CAACwC,sBAAsB,CAACoD,WAAW,EAAE;oBACvCpD,sBAAsB,CAACoD,WAAW,GAAG,IAAIhI;gBAC3C;gBACA0I,gBACEtG,KACA4F,YACApD,wBACA2D,qBACA;gBAGF;YACF;YAEA/K,2BAA2B4E,KAAKhB,YAAYiC,WAAW,EAAE2B,OAAO,CAC9D,CAACR;oBAMKA;gBALJ,IAAIyE,gBAA0B,EAAE;gBAChC,MAAMC,YAAY1E,WAAWO,cAAc;gBAE3C,mEAAmE;gBACnE,6CAA6C;gBAC7C,KAAIP,yBAAAA,WAAWE,UAAU,qBAArBF,uBAAuB2E,GAAG,EAAE;oBAC9BF,cAAczI,IAAI,IAAIgE,WAAWE,UAAU,CAACyE,GAAG;gBACjD,OAAO;oBACLF,gBAAgB;wBAAC;qBAAI;gBACvB;gBAEAX,uBAAuBY,WAAWD;YACpC;QAEJ;QAEA,2DAA2D;QAC3DX,uBAAuBvD,gBAAgB,EAAE;QAEzC,OAAO;YACLH;YACA1E,YAAYmI,WAAWnC,IAAI,GACvB;gBACE,CAACzB,aAAa,EAAE2E,MAAMC,IAAI,CAAChB;YAC7B,IACA,CAAC;YACLxD;QACF;IACF;IAEAgB,+BAA+B,EAC7B3E,QAAQ,EACRE,WAAW,EACXnB,SAAS,EACT6F,aAAa,EACbN,UAAU,EACVE,gBAAgB,EAQjB,EAIC;QACA,IAAI4B,mBAAmB;QAEvB,MAAMgC,gBAGF;YACFC,SAAS3K,OAAOyG,IAAI,CAACS,eAClBhH,IAAI,CAAC,CAACC,GAAGC,IAAO5B,SAASoM,IAAI,CAACxK,KAAK,IAAID,EAAE0K,aAAa,CAACzK,IACvDyI,GAAG,CAAC,CAACiC,mBAAsB,CAAA;oBAC1B/E,SAAS+E;oBACTP,KAAK;2BAAIrD,aAAa,CAAC4D,iBAAiB;qBAAC;gBAC3C,CAAA;YACFC,QAAQ;QACV;QAEA,uEAAuE;QACvE,0EAA0E;QAC1E,gBAAgB;QAChB,MAAMC,sBAAsB,CAAC,gCAAgC,EAAE3N,UAAU;YACvEsN,SAAS,AAAC,CAAA,IAAI,CAACzI,YAAY,GACvBwI,cAAcC,OAAO,CAAC9B,GAAG,CAAC,CAAC,EAAE9C,OAAO,EAAEwE,GAAG,EAAE,GAAM,CAAA;oBAC/CxE,SAASA,QAAQzB,OAAO,CACtB,mCACA,cAAcA,OAAO,CAAC,OAAOhH,KAAK2N,GAAG;oBAEvCV;gBACF,CAAA,KACAG,cAAcC,OAAO,AAAD,EACtB9B,GAAG,CAAC,CAACqC,IAAMC,KAAK9N,SAAS,CAAC6N;YAC5BH,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,MAAMK,kBAAkB,CAAC,gCAAgC,EAAE/N,UAAU;YACnEsN,SAASD,cAAcC,OAAO,CAAC9B,GAAG,CAAC,CAACqC,IAAMC,KAAK9N,SAAS,CAAC6N;YACzDH,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,iCAAiC;QACjC,2CAA2C;QAC3C,IAAI,IAAI,CAAC/I,GAAG,EAAE;YACZ,MAAM/B,UAAUxC,WAAW6E,SAASkG,UAAU;YAC9C,MAAM6C,UAAU1N,YACdI,eAAe6K,MAAM,EACrB7J,WAAWuM,GAAG,EACd1E;YAGF,IAAI,CAAC3G,OAAO,CAACoL,QAAQ,EAAE;gBACrBpL,OAAO,CAACoL,QAAQ,GAAG;oBACjBE,MAAM7N,WAAW8N,WAAW;oBAC5BC,eAAe,IAAIrK,IAAI;wBAACC;qBAAU;oBAClCqK,uBAAuB5E;oBACvBF;oBACAb,SAASiF;oBACTW,SAAS;oBACTC,gBAAgBC,KAAKC,GAAG;gBAC1B;gBACApD,mBAAmB;YACrB,OAAO;gBACL,MAAMqD,YAAY9L,OAAO,CAACoL,QAAQ;gBAClC,mCAAmC;gBACnC,IAAIU,UAAUhG,OAAO,KAAKiF,qBAAqB;oBAC7Ce,UAAUhG,OAAO,GAAGiF;oBACpBtC,mBAAmB;gBACrB;gBACA,IAAIqD,UAAUR,IAAI,KAAK7N,WAAW8N,WAAW,EAAE;oBAC7CO,UAAUN,aAAa,CAAC9J,GAAG,CAACN;gBAC9B;gBACA0K,UAAUJ,OAAO,GAAG;gBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;YACrC;QACF,OAAO;YACL1M,YAAYQ,qBAAqB,CAACgH,WAAW,GAAGoE;QAClD;QAEA,qDAAqD;QACrD,MAAMgB,0BAA0B5O,QAAQ6O,WAAW,CAACC,gBAAgB,CAClEd,iBACA;YACEvK,MAAM+F;QACR;QAGF,OAAO;YACL8B;YACA,6CAA6C;YAC7C,gGAAgG;YAChG,qEAAqE;YACrE,IAAI,CAACyD,QAAQ,CACX3J,aACA,6BAA6B;YAC7BF,SAAS+B,OAAO,EAChB2H,yBACA;gBACE,+BAA+B;gBAC/BnL,MAAMQ;gBACN,6CAA6C;gBAC7C,iEAAiE;gBACjE4C,OAAOrG,eAAesG,mBAAmB;YAC3C;YAEF8H;SACD;IACH;IAEAvE,kBAAkB,EAChBnF,QAAQ,EACRE,WAAW,EACXkF,OAAO,EACPrG,SAAS,EACTuF,UAAU,EACVwB,UAAU,EAQX,EAAE;QACD,MAAMgE,eAAe5B,MAAMC,IAAI,CAAC/C,QAAQzH,OAAO;QAE/C,MAAMoM,eAAe,CAAC,gCAAgC,EAAEhP,UAAU;YAChEqK,SAASyD,KAAK9N,SAAS,CAAC+O;YACxBE,qBAAqBlE;QACvB,GAAG,CAAC,CAAC;QAEL,MAAMmE,+BAA+B,IAAI,CAACrK,YAAY,GAClD9C,YAAYE,iBAAiB,GAC7BF,YAAYC,aAAa;QAC7B,KAAK,MAAM,CAACmN,GAAGlG,MAAM,IAAI8F,aAAc;YACrC,KAAK,MAAMvL,QAAQyF,MAAO;gBACxB,MAAM6B,KAAK9J,iBAAiBmO,GAAG3L;gBAC/B,IAAI,OAAO0L,4BAA4B,CAACpE,GAAG,KAAK,aAAa;oBAC3DoE,4BAA4B,CAACpE,GAAG,GAAG;wBACjCsE,SAAS,CAAC;wBACVxI,OAAO,CAAC;oBACV;gBACF;gBACAsI,4BAA4B,CAACpE,GAAG,CAACsE,OAAO,CAAC7F,WAAW,GAAG;gBACvD2F,4BAA4B,CAACpE,GAAG,CAAClE,KAAK,CAAC2C,WAAW,GAAGwB,aACjDxK,eAAe8O,aAAa,GAC5B9O,eAAe+O,qBAAqB;YAC1C;QACF;QAEA,0CAA0C;QAC1C,MAAMC,iBAAiBxP,QAAQ6O,WAAW,CAACC,gBAAgB,CAACG,cAAc;YACxExL,MAAM+F;QACR;QAEA,OAAO,IAAI,CAACuF,QAAQ,CAClB3J,aACA,6BAA6B;QAC7BF,SAAS+B,OAAO,EAChBuI,gBACA;YACE/L,MAAMQ;YACN4C,OAAOmE,aACHxK,eAAe8O,aAAa,GAC5B9O,eAAe+O,qBAAqB;QAC1C;IAEJ;IAEAR,SACE3J,WAAgB,EAChB6B,OAAe,EACfyB,UAA8B,EAC9B/D,OAA6B,EACf,mBAAmB,GAAG;QACpC,OAAO,IAAIsG,QAAQ,CAACwE,SAASC;YAC3B,MAAMC,QAAQvK,YAAYvC,OAAO,CAAC+M,GAAG,CAACjL,QAAQlB,IAAI;YAClDkM,MAAME,mBAAmB,CAACrL,IAAI,CAACkE;YAC/BtD,YAAYD,KAAK,CAAC4J,QAAQ,CAACe,IAAI,CAACH,OAAOhL;YACvCS,YAAY2K,aAAa,CACvB;gBACE9I;gBACAyB;gBACAsH,aAAa;oBAAEC,aAAatL,QAAQkC,KAAK;gBAAC;YAC5C,GACA,CAACqJ,KAAwBC;gBACvB,IAAID,KAAK;oBACP9K,YAAYD,KAAK,CAACiL,WAAW,CAACN,IAAI,CAACpH,YAAY/D,SAASuL;oBACxD,OAAOR,OAAOQ;gBAChB;gBAEA9K,YAAYD,KAAK,CAACkL,YAAY,CAACP,IAAI,CAACpH,YAAY/D,SAASwL;gBACzD,OAAOV,QAAQU;YACjB;QAEJ;IACF;IAEA,MAAMtI,mBACJzC,WAAgC,EAChCwC,MAAqC,EACrC;QACA,MAAM3F,gBAAwC,CAAC;QAC/C,MAAMC,oBAA4C,CAAC;QAEnDb,gBAAgB+D,aAAa,CAACgB,KAAKe,QAAQmJ,YAAYnK;YACrD,yEAAyE;YACzE,IACEmK,WAAW7M,IAAI,IACf2C,IAAIuC,OAAO,IACXxC,SACA,kCAAkCqH,IAAI,CAACpH,IAAIuC,OAAO,GAClD;gBACA,MAAMqC,aAAa,4BAA4BwC,IAAI,CAACpH,IAAIuC,OAAO;gBAE/D,MAAM4H,UAAU,IAAI,CAACzL,YAAY,GAC7B9C,YAAYI,qBAAqB,GACjCJ,YAAYG,iBAAiB;gBAEjC,IAAI,CAACoO,OAAO,CAACD,WAAW7M,IAAI,CAAC,EAAE;oBAC7B8M,OAAO,CAACD,WAAW7M,IAAI,CAAC,GAAG,CAAC;gBAC9B;gBACA8M,OAAO,CAACD,WAAW7M,IAAI,CAAC,CAACuH,aAAa,WAAW,SAAS,GAAG7E;YAC/D;QACF;QAEA,IAAK,IAAI4E,MAAM/I,YAAYC,aAAa,CAAE;YACxC,MAAMuO,SAASxO,YAAYC,aAAa,CAAC8I,GAAG;YAC5C,IAAK,IAAItH,QAAQ+M,OAAOnB,OAAO,CAAE;gBAC/B,MAAMlJ,QACJnE,YAAYG,iBAAiB,CAACsB,KAAK,CACjC+M,OAAO3J,KAAK,CAACpD,KAAK,KAAKjD,eAAe8O,aAAa,GAC/C,WACA,SACL;gBACHkB,OAAOnB,OAAO,CAAC5L,KAAK,GAAG0C;YACzB;YACAlE,aAAa,CAAC8I,GAAG,GAAGyF;QACtB;QAEA,IAAK,IAAIzF,MAAM/I,YAAYE,iBAAiB,CAAE;YAC5C,MAAMsO,SAASxO,YAAYE,iBAAiB,CAAC6I,GAAG;YAChD,IAAK,IAAItH,QAAQ+M,OAAOnB,OAAO,CAAE;gBAC/B,MAAMlJ,QACJnE,YAAYI,qBAAqB,CAACqB,KAAK,CACrC+M,OAAO3J,KAAK,CAACpD,KAAK,KAAKjD,eAAe8O,aAAa,GAC/C,WACA,SACL;gBACHkB,OAAOnB,OAAO,CAAC5L,KAAK,GAAG0C;YACzB;YACAjE,iBAAiB,CAAC6I,GAAG,GAAGyF;QAC1B;QAEA,MAAMC,OAAO1C,KAAK9N,SAAS,CACzB;YACEyQ,MAAMzO;YACN0O,MAAMzO;YACN8C,eAAe,IAAI,CAACA,aAAa;QACnC,GACA,MACA,IAAI,CAACJ,GAAG,GAAG,IAAIgM;QAGjBhJ,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC7C,WAAW,CAAC,EAAEjE,0BAA0B,GAAG,CAAC,CAAC,GAC1D,IAAIX,QAAQ0Q,SAAS,CACnB,CAAC,2BAA2B,EAAE9C,KAAK9N,SAAS,CAACwQ,MAAM,CAAC;QAExD7I,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC7C,WAAW,CAAC,EAAEjE,0BAA0B,KAAK,CAAC,CAAC,GAC5D,IAAIX,QAAQ0Q,SAAS,CAACJ;IAC1B;AACF;AAEA,SAAS/D,gBACPtG,GAAyB,EACzB4F,UAAkB,EAClBpD,sBAA8C,EAC9C2D,mBAA6B,EAC7BuE,aAAsB;QAEEjP;IAAxB,MAAMkP,mBAAkBlP,0BAAAA,mBAAmBuE,KAAK4K,GAAG,qBAA3BnP,wBAA6BkP,eAAe;IACpE,MAAME,cAAcF,oBAAoB;IACxC,MAAMG,oBAAoBpP,qBACxBsE,KACA6K,cAAc,aAAa;IAG7B,MAAME,mBAAmBvI,sBAAsB,CAACoD,WAAW;IAE3D,IAAIO,mBAAmB,CAAC,EAAE,KAAK,KAAK;QAClC,kEAAkE;QAClE,qDAAqD;QACrD,sCAAsC;QACtC,IAAI,CAACuE,iBAAiB;eAAIK;SAAiB,CAAC,EAAE,KAAK,KAAK;YACtDvI,sBAAsB,CAACoD,WAAW,GAAG,IAAIhI,IAAI;gBAAC;aAAI;QACpD;IACF,OAAO;QACL,MAAMoN,yBAAyBF,sBAAsB;QACrD,IAAIE,wBAAwB;YAC1BxI,sBAAsB,CAACoD,WAAW,GAAG,IAAIhI,IAAI;gBAAC;aAAI;QACpD,OAAO;YACL,gGAAgG;YAChG,oEAAoE;YACpE,KAAK,MAAMP,QAAQ8I,oBAAqB;gBACtC,mEAAmE;gBACnE,MAAM8E,qBAAqBJ,eAAexN,SAAS;gBAEnD,kEAAkE;gBAClE,4DAA4D;gBAC5D,IAAI4N,oBAAoB;oBACtBzI,sBAAsB,CAACoD,WAAW,CAACzH,GAAG,CAAC;gBACzC;gBAEAqE,sBAAsB,CAACoD,WAAW,CAACzH,GAAG,CAACd;YACzC;QACF;IACF;AACF"}