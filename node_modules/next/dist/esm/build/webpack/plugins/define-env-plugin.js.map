{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["webpack", "needsExperimentalReact", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "getNextPublicEnvironmentVariables", "defineEnv", "process", "env", "startsWith", "value", "getNextConfigEnv", "serializeDefineEnv", "defineEnvStringified", "JSON", "stringify", "getImageConfig", "dev", "deviceSizes", "images", "imageSizes", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "output", "getDefineEnv", "isTurbopack", "clientRouterFilters", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "__NEXT_DEFINE_ENV", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "experimental", "ppr", "deploymentId", "manualClientBasePath", "isNaN", "Number", "staleTimes", "dynamic", "static", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "linkNoTouchStart", "assetPrefix", "undefined", "getDefineEnvPlugin", "options", "DefinePlugin"], "mappings": "AAKA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,sBAAsB,QAAQ,wCAAuC;AAE9E,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AAuCA;;CAEC,GACD,SAASC;IACP,MAAMC,YAAuB,CAAC;IAC9B,IAAK,MAAMP,OAAOQ,QAAQC,GAAG,CAAE;QAC7B,IAAIT,IAAIU,UAAU,CAAC,iBAAiB;YAClC,MAAMC,QAAQH,QAAQC,GAAG,CAACT,IAAI;YAC9B,IAAIW,OAAO;gBACTJ,SAAS,CAAC,CAAC,YAAY,EAAEP,IAAI,CAAC,CAAC,GAAGW;YACpC;QACF;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,SAASK,iBAAiBb,MAA0B;IAClD,sCAAsC;IACtC,MAAMQ,YAAuB,CAAC;IAC9B,MAAME,MAAMV,OAAOU,GAAG;IACtB,IAAK,MAAMT,OAAOS,IAAK;QACrB,MAAME,QAAQF,GAAG,CAACT,IAAI;QACtB,IAAIW,OAAO;YACTb,qBAAqBC,QAAQC;YAC7BO,SAAS,CAAC,CAAC,YAAY,EAAEP,IAAI,CAAC,CAAC,GAAGW;QACpC;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,SAASM,mBAAmBN,SAAoB;IAC9C,MAAMO,uBAA4C,CAAC;IACnD,IAAK,MAAMd,OAAOO,UAAW;QAC3B,MAAMI,QAAQJ,SAAS,CAACP,IAAI;QAC5Bc,oBAAoB,CAACd,IAAI,GAAGe,KAAKC,SAAS,CAACL;IAC7C;IAEA,OAAOG;AACT;AAEA,SAASG,eACPlB,MAA0B,EAC1BmB,GAAY;QASKnB,gBAKSA;IAZ1B,OAAO;QACL,iCAAiC;YAC/BoB,aAAapB,OAAOqB,MAAM,CAACD,WAAW;YACtCE,YAAYtB,OAAOqB,MAAM,CAACC,UAAU;YACpCC,MAAMvB,OAAOqB,MAAM,CAACE,IAAI;YACxBC,QAAQxB,OAAOqB,MAAM,CAACG,MAAM;YAC5BC,qBAAqBzB,OAAOqB,MAAM,CAACI,mBAAmB;YACtDC,WAAW,EAAE1B,2BAAAA,iBAAAA,OAAQqB,MAAM,qBAAdrB,eAAgB0B,WAAW;YACxC,GAAIP,MACA;gBACE,gEAAgE;gBAChEQ,SAAS3B,OAAOqB,MAAM,CAACM,OAAO;gBAC9BC,cAAc,GAAE5B,kBAAAA,OAAOqB,MAAM,qBAAbrB,gBAAe4B,cAAc;gBAC7CC,QAAQ7B,OAAO6B,MAAM;YACvB,IACA,CAAC,CAAC;QACR;IACF;AACF;AAEA,OAAO,SAASC,aAAa,EAC3BC,WAAW,EACXC,mBAAmB,EACnBhC,MAAM,EACNmB,GAAG,EACHc,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EACK;QAmCNxC,iCAETA,kCAGSA,kCAETA,kCA6C6BA;IAtFrC,MAAMQ,YAAuB;QAC3B,+CAA+C;QAC/CiC,mBAAmB;QAEnB,GAAGlC,mCAAmC;QACtC,GAAGM,iBAAiBb,OAAO;QAC3B,GAAI,CAACqC,eACD,CAAC,IACD;YACEK,aACE;;;;aAIC,GACDjC,QAAQC,GAAG,CAACiC,0BAA0B,IAAI;QAC9C,CAAC;QACL,qBAAqBZ;QACrB,yBAAyBA;QACzB,6DAA6D;QAC7D,wBAAwBZ,MAAM,gBAAgB;QAC9C,4BAA4BkB,eACxB,SACAE,eACA,WACA;QACJ,4BAA4B;QAC5B,0BAA0BvC,OAAO4C,YAAY,CAACC,GAAG,KAAK;QACtD,kCAAkC7C,OAAO8C,YAAY,IAAI;QACzD,6CAA6CZ,uBAAuB;QACpE,0CAA0CM,sBAAsB,EAAE;QAClE,8CACExC,OAAO4C,YAAY,CAACG,oBAAoB,IAAI;QAC9C,sDAAsD/B,KAAKC,SAAS,CAClE+B,MAAMC,QAAOjD,kCAAAA,OAAO4C,YAAY,CAACM,UAAU,qBAA9BlD,gCAAgCmD,OAAO,KAChD,GAAG,aAAa;YAChBnD,mCAAAA,OAAO4C,YAAY,CAACM,UAAU,qBAA9BlD,iCAAgCmD,OAAO;QAE7C,qDAAqDnC,KAAKC,SAAS,CACjE+B,MAAMC,QAAOjD,mCAAAA,OAAO4C,YAAY,CAACM,UAAU,qBAA9BlD,iCAAgCoD,MAAM,KAC/C,IAAI,GAAG,YAAY;YACnBpD,mCAAAA,OAAO4C,YAAY,CAACM,UAAU,qBAA9BlD,iCAAgCoD,MAAM;QAE5C,mDACEpD,OAAO4C,YAAY,CAACS,kBAAkB,IAAI;QAC5C,6CACErB,CAAAA,uCAAAA,oBAAqBsB,YAAY,KAAI;QACvC,6CACEtB,CAAAA,uCAAAA,oBAAqBuB,aAAa,KAAI;QACxC,8CACEvD,OAAO4C,YAAY,CAACY,qBAAqB,IAAI;QAC/C,0CACExD,OAAO4C,YAAY,CAACa,kBAAkB,IAAI;QAC5C,mCAAmCzD,OAAO0D,WAAW;QACrD,mBAAmBtB;QACnB,gCAAgC3B,QAAQC,GAAG,CAACiD,gBAAgB,IAAI;QAChE,2FAA2F;QAC3F,GAAIxC,OAAQiB,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+BJ;QACjC,IACA,CAAC,CAAC;QACN,qCAAqCjC,OAAO4D,aAAa;QACzD,sCACE5D,OAAO6D,aAAa,CAACC,aAAa,IAAI;QACxC,+CACE9D,OAAO6D,aAAa,CAACE,qBAAqB,IAAI;QAChD,kCACE/D,OAAOgE,eAAe,KAAK,OAAO,QAAQhE,OAAOgE,eAAe;QAClE,sCACE,6EAA6E;QAC7EhE,OAAOgE,eAAe,KAAK,OAAO,OAAOhE,OAAOgE,eAAe;QACjE,qCAAqC,CAAC7C,OAAOnB,OAAOiE,aAAa;QACjE,mCACE,AAACjE,CAAAA,OAAO4C,YAAY,CAACsB,WAAW,IAAI,CAAC/C,GAAE,KAAM;QAC/C,qCACE,AAACnB,CAAAA,OAAO4C,YAAY,CAACuB,iBAAiB,IAAI,CAAChD,GAAE,KAAM;QACrD,yCACEnB,OAAO4C,YAAY,CAACwB,iBAAiB,IAAI;QAC3C,GAAGlD,eAAelB,QAAQmB,IAAI;QAC9B,sCAAsCnB,OAAOqE,QAAQ;QACrD,uCACErE,OAAO4C,YAAY,CAAC0B,cAAc,IAAI;QACxC,mCAAmCnC;QACnC,oCAAoCnC,OAAO6B,MAAM;QACjD,mCAAmC,CAAC,CAAC7B,OAAOuE,IAAI;QAChD,mCAAmCvE,EAAAA,eAAAA,OAAOuE,IAAI,qBAAXvE,aAAa2B,OAAO,KAAI;QAC3D,mCAAmC3B,OAAOwE,WAAW;QACrD,kDACExE,OAAOyE,0BAA0B;QACnC,0DACEzE,OAAO4C,YAAY,CAAC8B,iCAAiC,IAAI;QAC3D,4CACE1E,OAAO2E,yBAAyB;QAClC,iDACE,AAAC3E,CAAAA,OAAO4C,YAAY,CAACgC,oBAAoB,IACvC5E,OAAO4C,YAAY,CAACgC,oBAAoB,CAACC,MAAM,GAAG,CAAA,KACpD;QACF,6CACE7E,OAAO4C,YAAY,CAACgC,oBAAoB,IAAI;QAC9C,0CACE5E,OAAO4C,YAAY,CAACkC,gBAAgB,IAAI;QAC1C,mCAAmC9E,OAAO+E,WAAW;QACrD,GAAIzC,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiB;QACnB,IACA0C,SAAS;QACb,GAAI1C,0BACA;YACE,yCACExC,uBAAuBE;QAC3B,IACAgF,SAAS;IACf;IACA,OAAOlE,mBAAmBN;AAC5B;AAEA,OAAO,SAASyE,mBAAmBC,OAA+B;IAChE,OAAO,IAAIrF,QAAQsF,YAAY,CAACrD,aAAaoD;AAC/C"}