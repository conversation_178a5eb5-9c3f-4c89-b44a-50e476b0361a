{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/terser-webpack-plugin/src/index.ts"], "names": ["path", "webpack", "ModuleFilenameHelpers", "sources", "pLimit", "Worker", "spans", "getEcmaVersion", "environment", "arrowFunction", "const", "destructuring", "forOf", "module", "bigIntLiteral", "dynamicImport", "buildError", "error", "file", "line", "Error", "message", "col", "stack", "split", "slice", "join", "debugMinify", "process", "env", "NEXT_DEBUG_MINIFY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "options", "terserOptions", "parallel", "swcMinify", "optimize", "compiler", "compilation", "assets", "optimizeOptions", "cache", "SourceMapSource", "RawSource", "compilationSpan", "get", "terserSpan", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "name", "traceAsyncFn", "numberOfAssetsForMinify", "assetsList", "Object", "keys", "assetsForMinify", "Promise", "all", "filter", "matchObject", "bind", "undefined", "test", "res", "getAsset", "console", "log", "info", "minimized", "map", "source", "eTag", "getLazyHashedEtag", "output", "getPromise", "JSON", "stringify", "toString", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputSource", "numberOfWorkers", "Math", "min", "availableNumberOfCores", "initializedWorker", "getWorker", "minify", "result", "require", "input", "inputSourceMap", "sourceMap", "content", "compress", "mangle", "__dirname", "numWorkers", "enableWorkerThreads", "getStdout", "pipe", "stdout", "getStderr", "stderr", "limit", "scheduledTasks", "asset", "push", "minifySpan", "sourceFromInputSource", "sourceAndMap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "javascriptModule", "errors", "code", "storePromise", "newInfo", "updateAsset", "end", "apply", "ecma", "pluginName", "hooks", "thisCompilation", "tap", "getCache", "handleHashForChunk", "hash", "_chunk", "update", "JSModulesHooks", "javascript", "JavascriptModulesPlugin", "getCompilationHooks", "chunkHash", "chunk", "hasRuntime", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "statsPrinter", "stats", "print", "for", "green", "formatFlag"], "mappings": "AAAA,YAAYA,UAAU,OAAM;AAC5B,SACEC,OAAO,EACPC,qBAAqB,EACrBC,OAAO,QACF,qCAAoC;AAC3C,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,MAAM,QAAQ,iCAAgC;AACvD,SAASC,KAAK,QAAQ,yBAAwB;AAE9C,SAASC,eAAeC,WAAgB;IACtC,SAAS;IACT,IACEA,YAAYC,aAAa,IACzBD,YAAYE,KAAK,IACjBF,YAAYG,aAAa,IACzBH,YAAYI,KAAK,IACjBJ,YAAYK,MAAM,EAClB;QACA,OAAO;IACT;IAEA,UAAU;IACV,IAAIL,YAAYM,aAAa,IAAIN,YAAYO,aAAa,EAAE;QAC1D,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASC,WAAWC,KAAU,EAAEC,IAAY;IAC1C,IAAID,MAAME,IAAI,EAAE;QACd,OAAO,IAAIC,MACT,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEH,KAAK,CAAC,EAAED,MAAME,IAAI,CAAC,CAAC,EAC5DF,MAAMK,GAAG,CACV,CAAC,EACAL,MAAMM,KAAK,GAAG,CAAC,EAAE,EAAEN,MAAMM,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAGC,IAAI,CAAC,MAAM,CAAC,GAAG,GACpE,CAAC;IAEN;IAEA,IAAIT,MAAMM,KAAK,EAAE;QACf,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEJ,MAAMM,KAAK,CAAC,CAAC;IAC1E;IAEA,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,CAAC;AAC1D;AAEA,MAAMM,cAAcC,QAAQC,GAAG,CAACC,iBAAiB;AAEjD,OAAO,MAAMC;IAEXC,YAAYC,UAAe,CAAC,CAAC,CAAE;QAC7B,MAAM,EAAEC,gBAAgB,CAAC,CAAC,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGH;QAEpD,IAAI,CAACA,OAAO,GAAG;YACbG;YACAD;YACAD;QACF;IACF;IAEA,MAAMG,SACJC,QAAa,EACbC,WAAgB,EAChBC,MAAW,EACXC,eAAoB,EACpBC,KAAU,EACV,EAAEC,eAAe,EAAEC,SAAS,EAAO,EACnC;QACA,MAAMC,kBAAkBvC,MAAMwC,GAAG,CAACP,gBAAiBjC,MAAMwC,GAAG,CAACR;QAC7D,MAAMS,aAAaF,gBAAgBG,UAAU,CAC3C;QAEFD,WAAWE,YAAY,CAAC,mBAAmBV,YAAYW,IAAI;QAC3DH,WAAWE,YAAY,CAAC,aAAa,IAAI,CAAChB,OAAO,CAACG,SAAS;QAE3D,OAAOW,WAAWI,YAAY,CAAC;YAC7B,IAAIC,0BAA0B;YAC9B,MAAMC,aAAaC,OAAOC,IAAI,CAACf;YAE/B,MAAMgB,kBAAkB,MAAMC,QAAQC,GAAG,CACvCL,WACGM,MAAM,CAAC,CAACT;gBACP,IACE,CAAChD,sBAAsB0D,WAAW,CAACC,IAAI,CACrC,wCAAwC;gBACxCC,WACA;oBAAEC,MAAM;gBAAqB,GAC7Bb,OACF;oBACA,OAAO;gBACT;gBAEA,MAAMc,MAAMzB,YAAY0B,QAAQ,CAACf;gBACjC,IAAI,CAACc,KAAK;oBACRE,QAAQC,GAAG,CAACjB;oBACZ,OAAO;gBACT;gBAEA,MAAM,EAAEkB,IAAI,EAAE,GAAGJ;gBAEjB,qDAAqD;gBACrD,IAAII,KAAKC,SAAS,EAAE;oBAClB,OAAO;gBACT;gBAEA,OAAO;YACT,GACCC,GAAG,CAAC,OAAOpB;gBACV,MAAM,EAAEkB,IAAI,EAAEG,MAAM,EAAE,GAAGhC,YAAY0B,QAAQ,CAACf;gBAE9C,MAAMsB,OAAO9B,MAAM+B,iBAAiB,CAACF;gBACrC,MAAMG,SAAS,MAAMhC,MAAMiC,UAAU,CAACzB,MAAMsB;gBAE5C,IAAI,CAACE,QAAQ;oBACXtB,2BAA2B;gBAC7B;gBAEA,IAAIzB,eAAeA,gBAAgB,KAAK;oBACtCuC,QAAQC,GAAG,CACTS,KAAKC,SAAS,CAAC;wBACb3B;wBACAqB,QAAQA,OAAOA,MAAM,GAAGO,QAAQ;oBAClC,IACA;wBACEC,aAAaC;wBACbC,iBAAiBD;oBACnB;gBAEJ;gBACA,OAAO;oBAAE9B;oBAAMkB;oBAAMc,aAAaX;oBAAQG;oBAAQF;gBAAK;YACzD;YAGJ,MAAMW,kBAAkBC,KAAKC,GAAG,CAC9BjC,yBACAX,gBAAgB6C,sBAAsB;YAGxC,IAAIC;YAEJ,6CAA6C;YAC7C,MAAMC,YAAY;gBAChB,IAAI,IAAI,CAACvD,OAAO,CAACG,SAAS,EAAE;oBAC1B,OAAO;wBACLqD,QAAQ,OAAOxD;4BACb,MAAMyD,SAAS,MAAMC,QAAQ,mBAAmBF,MAAM,CACpDxD,QAAQ2D,KAAK,EACb;gCACE,GAAI3D,QAAQ4D,cAAc,GACtB;oCACEC,WAAW;wCACTC,SAASnB,KAAKC,SAAS,CAAC5C,QAAQ4D,cAAc;oCAChD;gCACF,IACA,CAAC,CAAC;gCACNG,UAAU;gCACVC,QAAQ;4BACV;4BAGF,OAAOP;wBACT;oBACF;gBACF;gBAEA,IAAIH,mBAAmB;oBACrB,OAAOA;gBACT;gBAEAA,oBAAoB,IAAIlF,OAAOL,KAAK0B,IAAI,CAACwE,WAAW,gBAAgB;oBAClEC,YAAYhB;oBACZiB,qBAAqB;gBACvB;gBAEAb,kBAAkBc,SAAS,GAAGC,IAAI,CAAC1E,QAAQ2E,MAAM;gBACjDhB,kBAAkBiB,SAAS,GAAGF,IAAI,CAAC1E,QAAQ6E,MAAM;gBAEjD,OAAOlB;YACT;YAEA,MAAMmB,QAAQtG,OACZ,mEAAmE;YACnE,IAAI,CAAC6B,OAAO,CAACG,SAAS,GAClB4C,WACA5B,0BAA0B,IAC1B+B,kBACAH;YAEN,MAAM2B,iBAAiB,EAAE;YAEzB,KAAK,MAAMC,SAASpD,gBAAiB;gBACnCmD,eAAeE,IAAI,CACjBH,MAAM;oBACJ,MAAM,EAAExD,IAAI,EAAEgC,WAAW,EAAEd,IAAI,EAAEI,IAAI,EAAE,GAAGoC;oBAC1C,IAAI,EAAElC,MAAM,EAAE,GAAGkC;oBAEjB,MAAME,aAAa/D,WAAWC,UAAU,CAAC;oBACzC8D,WAAW7D,YAAY,CAAC,QAAQC;oBAChC4D,WAAW7D,YAAY,CACrB,SACA,OAAOyB,WAAW,cAAc,SAAS;oBAG3C,OAAOoC,WAAW3D,YAAY,CAAC;wBAC7B,IAAI,CAACuB,QAAQ;4BACX,MAAM,EAAEH,QAAQwC,qBAAqB,EAAEzC,KAAKuB,cAAc,EAAE,GAC1DX,YAAY8B,YAAY;4BAE1B,MAAMpB,QAAQqB,OAAOC,QAAQ,CAACH,yBAC1BA,sBAAsBjC,QAAQ,KAC9BiC;4BAEJ,MAAM9E,UAAU;gCACdiB;gCACA0C;gCACAC;gCACA3D,eAAe;oCAAE,GAAG,IAAI,CAACD,OAAO,CAACC,aAAa;gCAAC;4BACjD;4BAEA,IAAI,OAAOD,QAAQC,aAAa,CAACrB,MAAM,KAAK,aAAa;gCACvD,IAAI,OAAOuD,KAAK+C,gBAAgB,KAAK,aAAa;oCAChDlF,QAAQC,aAAa,CAACrB,MAAM,GAAGuD,KAAK+C,gBAAgB;gCACtD,OAAO,IAAI,iBAAiBpD,IAAI,CAACb,OAAO;oCACtCjB,QAAQC,aAAa,CAACrB,MAAM,GAAG;gCACjC,OAAO,IAAI,iBAAiBkD,IAAI,CAACb,OAAO;oCACtCjB,QAAQC,aAAa,CAACrB,MAAM,GAAG;gCACjC;4BACF;4BAEA,IAAI;gCACF6D,SAAS,MAAMc,YAAYC,MAAM,CAACxD;4BACpC,EAAE,OAAOhB,OAAO;gCACdsB,YAAY6E,MAAM,CAACP,IAAI,CAAC7F,WAAWC,OAAOiC;gCAE1C;4BACF;4BAEA,IAAIwB,OAAOJ,GAAG,EAAE;gCACdI,OAAOH,MAAM,GAAG,IAAI5B,gBAClB+B,OAAO2C,IAAI,EACXnE,MACAwB,OAAOJ,GAAG,EACVsB,OACAC,gBACA;4BAEJ,OAAO;gCACLnB,OAAOH,MAAM,GAAG,IAAI3B,UAAU8B,OAAO2C,IAAI;4BAC3C;4BAEA,MAAM3E,MAAM4E,YAAY,CAACpE,MAAMsB,MAAM;gCACnCD,QAAQG,OAAOH,MAAM;4BACvB;wBACF;wBAEA,MAAMgD,UAAU;4BAAElD,WAAW;wBAAK;wBAClC,MAAM,EAAEE,MAAM,EAAE,GAAGG;wBAEnBnC,YAAYiF,WAAW,CAACtE,MAAMqB,QAAQgD;oBACxC;gBACF;YAEJ;YAEA,MAAM9D,QAAQC,GAAG,CAACiD;YAElB,IAAIpB,mBAAmB;gBACrB,MAAMA,kBAAkBkC,GAAG;YAC7B;QACF;IACF;IAEAC,MAAMpF,QAAa,EAAE;YACoBA;QAAvC,MAAM,EAAEK,eAAe,EAAEC,SAAS,EAAE,GAAGN,CAAAA,6BAAAA,oBAAAA,SAAUrC,OAAO,qBAAjBqC,kBAAmBnC,OAAO,KAAIA;QACrE,MAAM,EAAEuE,MAAM,EAAE,GAAGpC,SAASL,OAAO;QAEnC,IAAI,OAAO,IAAI,CAACA,OAAO,CAACC,aAAa,CAACyF,IAAI,KAAK,aAAa;YAC1D,IAAI,CAAC1F,OAAO,CAACC,aAAa,CAACyF,IAAI,GAAGpH,eAAemE,OAAOlE,WAAW,IAAI,CAAC;QAC1E;QAEA,MAAMoH,aAAa,IAAI,CAAC5F,WAAW,CAACkB,IAAI;QACxC,MAAMoC,yBAAyB,IAAI,CAACrD,OAAO,CAACE,QAAQ;QAEpDG,SAASuF,KAAK,CAACC,eAAe,CAACC,GAAG,CAACH,YAAY,CAACrF;YAC9C,MAAMG,QAAQH,YAAYyF,QAAQ,CAAC;YAEnC,MAAMC,qBAAqB,CAACC,MAAWC;gBACrC,oCAAoC;gBACpCD,KAAKE,MAAM,CAAC;YACd;YAEA,MAAMC,iBACJpI,QAAQqI,UAAU,CAACC,uBAAuB,CAACC,mBAAmB,CAC5DjG;YAEJ8F,eAAeI,SAAS,CAACV,GAAG,CAACH,YAAY,CAACc,OAAOR;gBAC/C,IAAI,CAACQ,MAAMC,UAAU,IAAI;gBACzB,OAAOV,mBAAmBC,MAAMQ;YAClC;YAEAnG,YAAYsF,KAAK,CAACe,aAAa,CAACC,UAAU,CACxC;gBACE3F,MAAM0E;gBACNkB,OAAO7I,QAAQ8I,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACxG,SACC,IAAI,CAACH,QAAQ,CACXC,UACAC,aACAC,QACA;oBACE8C;gBACF,GACA5C,OACA;oBAAEC;oBAAiBC;gBAAU;YAInCL,YAAYsF,KAAK,CAACoB,YAAY,CAAClB,GAAG,CAACH,YAAY,CAACsB;gBAC9CA,MAAMrB,KAAK,CAACsB,KAAK,CACdC,GAAG,CAAC,wBACJrB,GAAG,CACF,yBACA,CAAC1D,WAAgB,EAAEgF,KAAK,EAAEC,UAAU,EAAO,GACzC,wCAAwC;oBACxCjF,YAAYgF,MAAMC,WAAW,gBAAgBxF;YAErD;QACF;IACF;AACF"}