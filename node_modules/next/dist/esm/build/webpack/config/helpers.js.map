{"version": 3, "sources": ["../../../../src/build/webpack/config/helpers.ts"], "names": ["curry", "loader", "rule", "config", "module", "rules", "oneOf", "existing", "find", "arrayRule", "push", "unshiftLoader", "unshift", "plugin", "p", "plugins"], "mappings": "AAAA,OAAOA,WAAW,kCAAiC;AAGnD,OAAO,MAAMC,SAASD,MAAM,SAASC,OACnCC,IAAyB,EACzBC,MAA6B;QAiB7BA;IAfA,IAAI,CAACA,OAAOC,MAAM,EAAE;QAClBD,OAAOC,MAAM,GAAG;YAAEC,OAAO,EAAE;QAAC;IAC9B;IAEA,IAAIH,KAAKI,KAAK,EAAE;YACGH;QAAjB,MAAMI,YAAWJ,wBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,sBAAqBK,IAAI,CACxC,CAACC,YACCA,aAAa,OAAOA,cAAc,YAAYA,UAAUH,KAAK;QAEjE,IAAIC,YAAY,OAAOA,aAAa,UAAU;YAC5CA,SAASD,KAAK,CAAEI,IAAI,IAAIR,KAAKI,KAAK;YAClC,OAAOH;QACT;IACF;KAEAA,uBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,qBAAqBO,IAAI,CAACR;IAC1B,OAAOC;AACT,GAAE;AAEF,OAAO,MAAMQ,gBAAgBX,MAAM,SAASW,cAC1CT,IAAyB,EACzBC,MAA6B;QAiB7BA;IAfA,IAAI,CAACA,OAAOC,MAAM,EAAE;QAClBD,OAAOC,MAAM,GAAG;YAAEC,OAAO,EAAE;QAAC;IAC9B;IAEA,IAAIH,KAAKI,KAAK,EAAE;YACGH;QAAjB,MAAMI,YAAWJ,wBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,sBAAqBK,IAAI,CACxC,CAACC,YACCA,aAAa,OAAOA,cAAc,YAAYA,UAAUH,KAAK;QAEjE,IAAIC,YAAY,OAAOA,aAAa,UAAU;gBAC5CA;aAAAA,kBAAAA,SAASD,KAAK,qBAAdC,gBAAgBK,OAAO,IAAIV,KAAKI,KAAK;YACrC,OAAOH;QACT;IACF;KAEAA,uBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,qBAAqBS,OAAO,CAACV;IAC7B,OAAOC;AACT,GAAE;AAEF,OAAO,MAAMU,SAASb,MAAM,SAASa,OACnCC,CAAgC,EAChCX,MAA6B;IAE7B,IAAI,CAACA,OAAOY,OAAO,EAAE;QACnBZ,OAAOY,OAAO,GAAG,EAAE;IACrB;IACAZ,OAAOY,OAAO,CAACL,IAAI,CAACI;IACpB,OAAOX;AACT,GAAE"}