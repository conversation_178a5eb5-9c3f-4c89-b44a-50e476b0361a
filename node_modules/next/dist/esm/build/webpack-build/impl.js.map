{"version": 3, "sources": ["../../../src/build/webpack-build/impl.ts"], "names": ["red", "formatWebpackMessages", "nonNullable", "COMPILER_NAMES", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "APP_CLIENT_INTERNALS", "PHASE_PRODUCTION_BUILD", "runCompiler", "Log", "getBaseWebpackConfig", "loadProjectInfo", "TelemetryPlugin", "NextBuildContext", "resumePluginState", "getPluginState", "createEntrypoints", "loadConfig", "getTraceEvents", "initializeTraceState", "setGlobal", "trace", "WEBPACK_LAYERS", "TraceEntryPointsPlugin", "origDebug", "Telemetry", "debug", "isTelemetryPlugin", "plugin", "isTraceEntryPointsPlugin", "webpackBuildImpl", "compilerName", "result", "warnings", "errors", "stats", "webpackBuildStart", "nextBuildSpan", "dir", "config", "runWebpackSpan", "<PERSON><PERSON><PERSON><PERSON>", "entrypoints", "traceAsyncFn", "buildId", "envFiles", "loadedEnvFiles", "isDev", "rootDir", "pageExtensions", "pagesDir", "appDir", "pages", "mappedPages", "appPaths", "mappedAppPages", "previewMode", "previewProps", "rootPaths", "mappedRootPaths", "hasInstrumentationHook", "commonWebpackOptions", "isServer", "<PERSON><PERSON><PERSON>", "rewrites", "originalRewrites", "originalRedirects", "reactProductionProfiling", "noMangling", "clientRouterFilters", "previewModeId", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "configs", "info", "dev", "Promise", "all", "middlewareMatchers", "compilerType", "client", "server", "edgeServer", "edgePreviewProps", "clientConfig", "serverConfig", "edgeConfig", "optimization", "minimize", "minimizer", "length", "warn", "process", "hrtime", "inputFileSystem", "clientResult", "serverResult", "edgeServerResult", "start", "Date", "now", "pluginState", "key", "injectedClientEntries", "value", "clientEntry", "entry", "import", "layer", "appPagesBrowser", "dependOn", "purge", "filter", "traceFn", "telemetryPlugin", "plugins", "find", "traceEntryPointsPlugin", "webpackBuildEnd", "error", "Boolean", "join", "console", "indexOf", "page_name_regex", "parsed", "exec", "page_name", "groups", "Error", "err", "code", "event", "duration", "buildTraceContext", "telemetryState", "usages", "packagesUsedInServerSideProps", "worker<PERSON>ain", "workerData", "telemetry", "distDir", "buildContext", "Object", "assign", "traceState", "entriesTrace", "chunksTrace", "entryNameMap", "depModArray", "entryEntries", "entryNameFilesMap", "stop", "debugTraceEvents"], "mappings": "AACA,SAASA,GAAG,QAAQ,uBAAsB;AAC1C,OAAOC,2BAA2B,qFAAoF;AACtH,SAASC,WAAW,QAAQ,yBAAwB;AAEpD,SACEC,cAAc,EACdC,oCAAoC,EACpCC,oBAAoB,EACpBC,sBAAsB,QACjB,6BAA4B;AACnC,SAASC,WAAW,QAAQ,cAAa;AACzC,YAAYC,SAAS,gBAAe;AACpC,OAAOC,wBAAwBC,eAAe,QAAQ,oBAAmB;AAEzE,SACEC,eAAe,QAEV,sCAAqC;AAC5C,SACEC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,QACT,mBAAkB;AACzB,SAASC,iBAAiB,QAAQ,aAAY;AAC9C,OAAOC,gBAAgB,sBAAqB;AAC5C,SACEC,cAAc,EACdC,oBAAoB,EACpBC,SAAS,EACTC,KAAK,QAGA,cAAa;AACpB,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,sBAAsB,QAAQ,mDAAkD;AAIzF,OAAOC,eAAe,2BAA0B;AAChD,SAASC,SAAS,QAAQ,0BAAyB;AAEnD,MAAMC,QAAQF,UAAU;AAcxB,SAASG,kBAAkBC,MAAe;IACxC,OAAOA,kBAAkBhB;AAC3B;AAEA,SAASiB,yBACPD,MAAe;IAEf,OAAOA,kBAAkBL;AAC3B;AAEA,OAAO,eAAeO,iBACpBC,YAAkD;QA8M1B,uBAIO;IA3M/B,IAAIC,SAAgC;QAClCC,UAAU,EAAE;QACZC,QAAQ,EAAE;QACVC,OAAO,EAAE;IACX;IACA,IAAIC;IACJ,MAAMC,gBAAgBxB,iBAAiBwB,aAAa;IACpD,MAAMC,MAAMzB,iBAAiByB,GAAG;IAChC,MAAMC,SAAS1B,iBAAiB0B,MAAM;IAEtC,MAAMC,iBAAiBH,cAAcI,UAAU,CAAC;IAChD,MAAMC,cAAc,MAAML,cACvBI,UAAU,CAAC,sBACXE,YAAY,CAAC,IACZ3B,kBAAkB;YAChB4B,SAAS/B,iBAAiB+B,OAAO;YACjCL,QAAQA;YACRM,UAAUhC,iBAAiBiC,cAAc;YACzCC,OAAO;YACPC,SAASV;YACTW,gBAAgBV,OAAOU,cAAc;YACrCC,UAAUrC,iBAAiBqC,QAAQ;YACnCC,QAAQtC,iBAAiBsC,MAAM;YAC/BC,OAAOvC,iBAAiBwC,WAAW;YACnCC,UAAUzC,iBAAiB0C,cAAc;YACzCC,aAAa3C,iBAAiB4C,YAAY;YAC1CC,WAAW7C,iBAAiB8C,eAAe;YAC3CC,wBAAwB/C,iBAAiB+C,sBAAsB;QACjE;IAGJ,MAAMC,uBAAuB;QAC3BC,UAAU;QACVlB,SAAS/B,iBAAiB+B,OAAO;QACjCmB,eAAelD,iBAAiBkD,aAAa;QAC7CxB,QAAQA;QACRY,QAAQtC,iBAAiBsC,MAAM;QAC/BD,UAAUrC,iBAAiBqC,QAAQ;QACnCc,UAAUnD,iBAAiBmD,QAAQ;QACnCC,kBAAkBpD,iBAAiBoD,gBAAgB;QACnDC,mBAAmBrD,iBAAiBqD,iBAAiB;QACrDC,0BAA0BtD,iBAAiBsD,wBAAwB;QACnEC,YAAYvD,iBAAiBuD,UAAU;QACvCC,qBAAqBxD,iBAAiBwD,mBAAmB;QACzDC,eAAezD,iBAAiByD,aAAa;QAC7CC,6BAA6B1D,iBAAiB0D,2BAA2B;QACzEC,qBAAqB3D,iBAAiB2D,mBAAmB;IAC3D;IAEA,MAAMC,UAAU,MAAMjC,eACnBC,UAAU,CAAC,2BACXE,YAAY,CAAC;QACZ,MAAM+B,OAAO,MAAM/D,gBAAgB;YACjC2B;YACAC,QAAQsB,qBAAqBtB,MAAM;YACnCoC,KAAK;QACP;QACA,OAAOC,QAAQC,GAAG,CAAC;YACjBnE,qBAAqB4B,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBiB,oBAAoBpC,YAAYoC,kBAAkB;gBAClDtC;gBACAuC,cAAc3E,eAAe4E,MAAM;gBACnCtC,aAAaA,YAAYsC,MAAM;gBAC/B,GAAGN,IAAI;YACT;YACAhE,qBAAqB4B,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBrB;gBACAsC,oBAAoBpC,YAAYoC,kBAAkB;gBAClDC,cAAc3E,eAAe6E,MAAM;gBACnCvC,aAAaA,YAAYuC,MAAM;gBAC/B,GAAGP,IAAI;YACT;YACAhE,qBAAqB4B,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBrB;gBACAsC,oBAAoBpC,YAAYoC,kBAAkB;gBAClDC,cAAc3E,eAAe8E,UAAU;gBACvCxC,aAAaA,YAAYwC,UAAU;gBACnCC,kBAAkBtE,iBAAiB4C,YAAY;gBAC/C,GAAGiB,IAAI;YACT;SACD;IACH;IAEF,MAAMU,eAAeX,OAAO,CAAC,EAAE;IAC/B,MAAMY,eAAeZ,OAAO,CAAC,EAAE;IAC/B,MAAMa,aAAab,OAAO,CAAC,EAAE;IAE7B,IACEW,aAAaG,YAAY,IACxBH,CAAAA,aAAaG,YAAY,CAACC,QAAQ,KAAK,QACrCJ,aAAaG,YAAY,CAACE,SAAS,IAClCL,aAAaG,YAAY,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,GACpD;QACAjF,IAAIkF,IAAI,CACN,CAAC,iIAAiI,CAAC;IAEvI;IAEAvD,oBAAoBwD,QAAQC,MAAM;IAElCnE,MAAM,CAAC,iBAAiB,CAAC,EAAEK;IAC3B,+EAA+E;IAC/E,MAAMS,eAAeG,YAAY,CAAC;YAsEhCmD;QArEA,qDAAqD;QACrD,8DAA8D;QAC9D,IAAIC,eAA4C;QAEhD,uEAAuE;QACvE,yEAAyE;QACzE,IAAIC,eACF;QACF,IAAIC,mBAEO;QAEX,IAAIH;QAEJ,IAAI,CAAC/D,gBAAgBA,iBAAiB,UAAU;YAC9CL,MAAM;YACN,MAAMwE,QAAQC,KAAKC,GAAG;YACrB,CAACJ,cAAcF,gBAAgB,GAAG,MAAMtF,YAAY6E,cAAc;gBACjE7C;gBACAsD;YACF;YACApE,MAAM,CAAC,yBAAyB,EAAEyE,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC1D;QAEA,IAAI,CAACnE,gBAAgBA,iBAAiB,eAAe;YACnDL,MAAM;YACN,MAAMwE,QAAQC,KAAKC,GAAG;YACrB,CAACH,kBAAkBH,gBAAgB,GAAGR,aACnC,MAAM9E,YAAY8E,YAAY;gBAAE9C;gBAAgBsD;YAAgB,KAChE;gBAAC;aAAK;YACVpE,MAAM,CAAC,8BAA8B,EAAEyE,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC/D;QAEA,wCAAwC;QACxC,IAAI,EAACF,gCAAAA,aAAc9D,MAAM,CAACwD,MAAM,KAAI,EAACO,oCAAAA,iBAAkB/D,MAAM,CAACwD,MAAM,GAAE;YACpE,MAAMW,cAActF;YACpB,IAAK,MAAMuF,OAAOD,YAAYE,qBAAqB,CAAE;gBACnD,MAAMC,QAAQH,YAAYE,qBAAqB,CAACD,IAAI;gBACpD,MAAMG,cAAcrB,aAAasB,KAAK;gBACtC,IAAIJ,QAAQhG,sBAAsB;oBAChCmG,WAAW,CAACpG,qCAAqC,GAAG;wBAClDsG,QAAQ;4BACN,6HAA6H;4BAC7H,oFAAoF;+BACjFF,WAAW,CAACpG,qCAAqC,CAACsG,MAAM;4BAC3DH;yBACD;wBACDI,OAAOtF,eAAeuF,eAAe;oBACvC;gBACF,OAAO;oBACLJ,WAAW,CAACH,IAAI,GAAG;wBACjBQ,UAAU;4BAACzG;yBAAqC;wBAChDsG,QAAQH;wBACRI,OAAOtF,eAAeuF,eAAe;oBACvC;gBACF;YACF;YAEA,IAAI,CAAC9E,gBAAgBA,iBAAiB,UAAU;gBAC9CL,MAAM;gBACN,MAAMwE,QAAQC,KAAKC,GAAG;gBACrB,CAACL,cAAcD,gBAAgB,GAAG,MAAMtF,YAAY4E,cAAc;oBACjE5C;oBACAsD;gBACF;gBACApE,MAAM,CAAC,yBAAyB,EAAEyE,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;YAC1D;QACF;QAEAJ,oCAAAA,yBAAAA,gBAAiBiB,KAAK,qBAAtBjB,4BAAAA;QAEA9D,SAAS;YACPC,UAAU;mBACJ8D,CAAAA,gCAAAA,aAAc9D,QAAQ,KAAI,EAAE;mBAC5B+D,CAAAA,gCAAAA,aAAc/D,QAAQ,KAAI,EAAE;mBAC5BgE,CAAAA,oCAAAA,iBAAkBhE,QAAQ,KAAI,EAAE;aACrC,CAAC+E,MAAM,CAAC7G;YACT+B,QAAQ;mBACF6D,CAAAA,gCAAAA,aAAc7D,MAAM,KAAI,EAAE;mBAC1B8D,CAAAA,gCAAAA,aAAc9D,MAAM,KAAI,EAAE;mBAC1B+D,CAAAA,oCAAAA,iBAAkB/D,MAAM,KAAI,EAAE;aACnC,CAAC8E,MAAM,CAAC7G;YACTgC,OAAO;gBACL4D,gCAAAA,aAAc5D,KAAK;gBACnB6D,gCAAAA,aAAc7D,KAAK;gBACnB8D,oCAAAA,iBAAkB9D,KAAK;aACxB;QACH;IACF;IACAH,SAASK,cACNI,UAAU,CAAC,2BACXwE,OAAO,CAAC,IAAM/G,sBAAsB8B,QAAQ;IAE/C,MAAMkF,mBAAkB,wBAAA,AAAC9B,aAAuC+B,OAAO,qBAA/C,sBAAiDC,IAAI,CAC3EzF;IAGF,MAAM0F,0BAAyB,wBAAA,AAC7BhC,aACA8B,OAAO,qBAFsB,sBAEpBC,IAAI,CAACvF;IAEhB,MAAMyF,kBAAkB1B,QAAQC,MAAM,CAACzD;IAEvC,IAAIJ,OAAOE,MAAM,CAACwD,MAAM,GAAG,GAAG;QAC5B,8DAA8D;QAC9D,0DAA0D;QAC1D,IAAI1D,OAAOE,MAAM,CAACwD,MAAM,GAAG,GAAG;YAC5B1D,OAAOE,MAAM,CAACwD,MAAM,GAAG;QACzB;QACA,IAAI6B,QAAQvF,OAAOE,MAAM,CAAC8E,MAAM,CAACQ,SAASC,IAAI,CAAC;QAE/CC,QAAQH,KAAK,CAACtH,IAAI;QAElB,IACEsH,MAAMI,OAAO,CAAC,wBAAwB,CAAC,KACvCJ,MAAMI,OAAO,CAAC,uCAAuC,CAAC,GACtD;YACA,MAAMC,kBAAkB;YACxB,MAAMC,SAASD,gBAAgBE,IAAI,CAACP;YACpC,MAAMQ,YAAYF,UAAUA,OAAOG,MAAM,IAAIH,OAAOG,MAAM,CAACD,SAAS;YACpE,MAAM,IAAIE,MACR,CAAC,sFAAsF,EAAEF,UAAU,oFAAoF,CAAC;QAE5L;QAEAL,QAAQH,KAAK,CAACA;QACdG,QAAQH,KAAK;QAEb,IACEA,MAAMI,OAAO,CAAC,wBAAwB,CAAC,KACvCJ,MAAMI,OAAO,CAAC,uBAAuB,CAAC,GACtC;YACA,MAAMO,MAAM,IAAID,MACd;YAEFC,IAAIC,IAAI,GAAG;YACX,MAAMD;QACR;QACA,MAAMA,MAAM,IAAID,MAAM;QACtBC,IAAIC,IAAI,GAAG;QACX,MAAMD;IACR,OAAO;QACL,IAAIlG,OAAOC,QAAQ,CAACyD,MAAM,GAAG,GAAG;YAC9BjF,IAAIkF,IAAI,CAAC;YACT+B,QAAQ/B,IAAI,CAAC3D,OAAOC,QAAQ,CAAC+E,MAAM,CAACQ,SAASC,IAAI,CAAC;YAClDC,QAAQ/B,IAAI;QACd,OAAO,IAAI,CAAC5D,cAAc;YACxBtB,IAAI2H,KAAK,CAAC;QACZ;QAEA,OAAO;YACLC,UAAUf,eAAe,CAAC,EAAE;YAC5BgB,iBAAiB,EAAEjB,0CAAAA,uBAAwBiB,iBAAiB;YAC5DjC,aAAatF;YACbwH,gBAAgB;gBACdC,QAAQtB,CAAAA,mCAAAA,gBAAiBsB,MAAM,OAAM,EAAE;gBACvCC,+BACEvB,CAAAA,mCAAAA,gBAAiBuB,6BAA6B,OAAM,EAAE;YAC1D;QACF;IACF;AACF;AAEA,sDAAsD;AACtD,OAAO,eAAeC,WAAWC,UAIhC;IAKC,iCAAiC;IACjC,MAAMC,YAAY,IAAInH,UAAU;QAC9BoH,SAASF,WAAWG,YAAY,CAACvG,MAAM,CAAEsG,OAAO;IAClD;IACAzH,UAAU,aAAawH;IACvB,0EAA0E;IAC1EG,OAAOC,MAAM,CAACnI,kBAAkB8H,WAAWG,YAAY;IAEvD,0CAA0C;IAC1C3H,qBAAqBwH,WAAWM,UAAU;IAE1C,sBAAsB;IACtBnI,kBAAkBD,iBAAiBwF,WAAW;IAE9C,iDAAiD;IACjDxF,iBAAiB0B,MAAM,GAAG,MAAMtB,WAC9BV,wBACAM,iBAAiByB,GAAG;IAEtBzB,iBAAiBwB,aAAa,GAAGhB,MAC/B,CAAC,YAAY,EAAEsH,WAAW5G,YAAY,CAAC,CAAC;IAG1C,MAAMC,SAAS,MAAMF,iBAAiB6G,WAAW5G,YAAY;IAC7D,MAAM,EAAEmH,YAAY,EAAEC,WAAW,EAAE,GAAGnH,OAAOsG,iBAAiB,IAAI,CAAC;IACnE,IAAIY,cAAc;QAChB,MAAM,EAAEE,YAAY,EAAEC,WAAW,EAAE,GAAGH;QACtC,IAAIG,aAAa;YACfrH,OAAOsG,iBAAiB,CAAEY,YAAY,CAAEG,WAAW,GAAGA;QACxD;QACA,IAAID,cAAc;YAChB,MAAME,eAAeF;YACrBpH,OAAOsG,iBAAiB,CAAEY,YAAY,CAAEE,YAAY,GAAGE;QACzD;IACF;IACA,IAAIH,+BAAAA,YAAaI,iBAAiB,EAAE;QAClC,MAAMA,oBAAoBJ,YAAYI,iBAAiB;QACvDvH,OAAOsG,iBAAiB,CAAEa,WAAW,CAAEI,iBAAiB,GAAGA;IAC7D;IACA1I,iBAAiBwB,aAAa,CAACmH,IAAI;IACnC,OAAO;QAAE,GAAGxH,MAAM;QAAEyH,kBAAkBvI;IAAiB;AACzD"}