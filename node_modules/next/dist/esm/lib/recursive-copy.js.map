{"version": 3, "sources": ["../../src/lib/recursive-copy.ts"], "names": ["path", "promises", "constants", "<PERSON><PERSON>", "isError", "COPYFILE_EXCL", "recursiveCopy", "source", "dest", "concurrency", "overwrite", "filter", "cwdPath", "process", "cwd", "from", "resolve", "to", "sema", "_copy", "item", "lstats", "target", "replace", "acquire", "lstat", "isFile", "isDirectory", "isSymbolicLink", "stats", "stat", "mkdir", "recursive", "err", "code", "release", "files", "readdir", "withFileTypes", "Promise", "all", "map", "file", "join", "name", "copyFile", "undefined"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAEvB,SAASC,QAAQ,EAAEC,SAAS,QAAQ,KAAI;AACxC,SAASC,IAAI,QAAQ,gCAA+B;AACpD,OAAOC,aAAa,aAAY;AAEhC,MAAMC,gBAAgBH,UAAUG,aAAa;AAE7C,OAAO,eAAeC,cACpBC,MAAc,EACdC,IAAY,EACZ,EACEC,cAAc,EAAE,EAChBC,YAAY,KAAK,EACjBC,SAAS,IAAM,IAAI,EAKpB,GAAG,CAAC,CAAC;IAEN,MAAMC,UAAUC,QAAQC,GAAG;IAC3B,MAAMC,OAAOf,KAAKgB,OAAO,CAACJ,SAASL;IACnC,MAAMU,KAAKjB,KAAKgB,OAAO,CAACJ,SAASJ;IAEjC,MAAMU,OAAO,IAAIf,KAAKM;IAEtB,+BAA+B;IAC/B,eAAeU,MAAMC,IAAY,EAAEC,MAAuB;QACxD,MAAMC,SAASF,KAAKG,OAAO,CAACR,MAAME;QAElC,MAAMC,KAAKM,OAAO;QAElB,IAAI,CAACH,QAAQ;YACX,0BAA0B;YAC1BA,SAAS,MAAMpB,SAASwB,KAAK,CAACV;QAChC;QAEA,+CAA+C;QAC/C,kDAAkD;QAClD,IAAIW,SAASL,OAAOK,MAAM;QAC1B,IAAIC,cAAcN,OAAOM,WAAW;QACpC,IAAIN,OAAOO,cAAc,IAAI;YAC3B,MAAMC,QAAQ,MAAM5B,SAAS6B,IAAI,CAACV;YAClCM,SAASG,MAAMH,MAAM;YACrBC,cAAcE,MAAMF,WAAW;QACjC;QAEA,IAAIA,aAAa;YACf,IAAI;gBACF,MAAM1B,SAAS8B,KAAK,CAACT,QAAQ;oBAAEU,WAAW;gBAAK;YACjD,EAAE,OAAOC,KAAK;gBACZ,8CAA8C;gBAC9C,IAAI7B,QAAQ6B,QAAQA,IAAIC,IAAI,KAAK,UAAU;oBACzC,MAAMD;gBACR;YACF;YACAf,KAAKiB,OAAO;YACZ,MAAMC,QAAQ,MAAMnC,SAASoC,OAAO,CAACjB,MAAM;gBAAEkB,eAAe;YAAK;YACjE,MAAMC,QAAQC,GAAG,CACfJ,MAAMK,GAAG,CAAC,CAACC,OAASvB,MAAMnB,KAAK2C,IAAI,CAACvB,MAAMsB,KAAKE,IAAI,GAAGF;QAE1D,OAAO,IACLhB,UACA,oCAAoC;QACpC,8DAA8D;QAC9Df,OAAOS,KAAKG,OAAO,CAACR,MAAM,IAAIQ,OAAO,CAAC,OAAO,OAC7C;YACA,MAAMtB,SAAS4C,QAAQ,CACrBzB,MACAE,QACAZ,YAAYoC,YAAYzC;YAE1Ba,KAAKiB,OAAO;QACd,OAAO;YACLjB,KAAKiB,OAAO;QACd;IACF;IAEA,MAAMhB,MAAMJ;AACd"}