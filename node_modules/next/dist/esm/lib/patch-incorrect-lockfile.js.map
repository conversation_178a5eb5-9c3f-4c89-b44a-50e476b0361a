{"version": 3, "sources": ["../../src/lib/patch-incorrect-lockfile.ts"], "names": ["promises", "Log", "findUp", "nextPkgJson", "isCI", "getRegistry", "registry", "fetchPkgInfo", "pkg", "res", "fetch", "ok", "Error", "status", "data", "json", "versionData", "versions", "version", "os", "cpu", "engines", "tarball", "dist", "integrity", "patchIncorrectLockfile", "dir", "process", "env", "NEXT_IGNORE_INCORRECT_LOCKFILE", "lockfilePath", "cwd", "content", "readFile", "endingNewline", "endsWith", "lockfileParsed", "JSON", "parse", "lockfileVersion", "parseInt", "expectedSwcPkgs", "Object", "keys", "patchDependency", "pkgData", "dependencies", "resolved", "optional", "patchPackage", "packages", "supportedVersions", "includes", "shouldPatchDependencies", "shouldPatchPackages", "missingSwcPkgs", "pkgPrefix", "substring", "length", "push", "warn", "pkgsData", "Promise", "all", "map", "i", "writeFile", "stringify", "err", "error", "console"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,KAAI;AAC7B,YAAYC,SAAS,sBAAqB;AAC1C,OAAOC,YAAY,6BAA4B;AAC/C,2BAA2B;AAC3B,OAAOC,iBAAiB,oBAAmB;AAE3C,SAASC,IAAI,QAAQ,uBAAsB;AAC3C,SAASC,WAAW,QAAQ,yBAAwB;AAEpD,IAAIC;AAEJ,eAAeC,aAAaC,GAAW;IACrC,IAAI,CAACF,UAAUA,WAAWD;IAC1B,MAAMI,MAAM,MAAMC,MAAM,CAAC,EAAEJ,SAAS,EAAEE,IAAI,CAAC;IAE3C,IAAI,CAACC,IAAIE,EAAE,EAAE;QACX,MAAM,IAAIC,MACR,CAAC,kCAAkC,EAAEJ,IAAI,aAAa,EAAEC,IAAII,MAAM,CAAC,CAAC;IAExE;IACA,MAAMC,OAAO,MAAML,IAAIM,IAAI;IAC3B,MAAMC,cAAcF,KAAKG,QAAQ,CAACd,YAAYe,OAAO,CAAC;IAEtD,OAAO;QACLC,IAAIH,YAAYG,EAAE;QAClBC,KAAKJ,YAAYI,GAAG;QACpBC,SAASL,YAAYK,OAAO;QAC5BC,SAASN,YAAYO,IAAI,CAACD,OAAO;QACjCE,WAAWR,YAAYO,IAAI,CAACC,SAAS;IACvC;AACF;AAEA;;;;;CAKC,GACD,OAAO,eAAeC,uBAAuBC,GAAW;IACtD,IAAIC,QAAQC,GAAG,CAACC,8BAA8B,EAAE;QAC9C;IACF;IACA,MAAMC,eAAe,MAAM5B,OAAO,qBAAqB;QAAE6B,KAAKL;IAAI;IAElE,IAAI,CAACI,cAAc;QACjB,oDAAoD;QACpD;IACF;IACA,MAAME,UAAU,MAAMhC,SAASiC,QAAQ,CAACH,cAAc;IACtD,+BAA+B;IAC/B,MAAMI,gBAAgBF,QAAQG,QAAQ,CAAC,UACnC,SACAH,QAAQG,QAAQ,CAAC,QACjB,OACA;IAEJ,MAAMC,iBAAiBC,KAAKC,KAAK,CAACN;IAClC,MAAMO,kBAAkBC,SAASJ,kCAAAA,eAAgBG,eAAe,EAAE;IAClE,MAAME,kBAAkBC,OAAOC,IAAI,CAACxC,WAAW,CAAC,uBAAuB,IAAI,CAAC;IAE5E,MAAMyC,kBAAkB,CACtBpC,KACAqC;QAEAT,eAAeU,YAAY,CAACtC,IAAI,GAAG;YACjCU,SAASf,YAAYe,OAAO;YAC5B6B,UAAUF,QAAQvB,OAAO;YACzBE,WAAWqB,QAAQrB,SAAS;YAC5BwB,UAAU;QACZ;IACF;IAEA,MAAMC,eAAe,CACnBzC,KACAqC;QAEAT,eAAec,QAAQ,CAAC1C,IAAI,GAAG;YAC7BU,SAASf,YAAYe,OAAO;YAC5B6B,UAAUF,QAAQvB,OAAO;YACzBE,WAAWqB,QAAQrB,SAAS;YAC5BJ,KAAKyB,QAAQzB,GAAG;YAChB4B,UAAU;YACV7B,IAAI0B,QAAQ1B,EAAE;YACdE,SAASwB,QAAQxB,OAAO;QAC1B;IACF;IAEA,IAAI;QACF,MAAM8B,oBAAoB;YAAC;YAAG;YAAG;SAAE;QAEnC,IAAI,CAACA,kBAAkBC,QAAQ,CAACb,kBAAkB;YAChD,8BAA8B;YAC9B;QACF;QACA,4BAA4B;QAC5B,oCAAoC;QACpC,wBAAwB;QACxB,MAAMc,0BACJd,oBAAoB,KAAKA,oBAAoB;QAC/C,MAAMe,sBAAsBf,oBAAoB,KAAKA,oBAAoB;QAEzE,IACE,AAACc,2BAA2B,CAACjB,eAAeU,YAAY,IACvDQ,uBAAuB,CAAClB,eAAec,QAAQ,EAChD;YACA,2BAA2B;YAC3B;QACF;QACA,MAAMK,iBAAiB,EAAE;QACzB,IAAIC;QAEJ,IAAIF,qBAAqB;YACvBE,YAAY;YACZ,KAAK,MAAMhD,OAAOkC,OAAOC,IAAI,CAACP,eAAec,QAAQ,EAAG;gBACtD,IAAI1C,IAAI2B,QAAQ,CAAC,sBAAsB;oBACrCqB,YAAYhD,IAAIiD,SAAS,CAAC,GAAGjD,IAAIkD,MAAM,GAAG;gBAC5C;YACF;YAEA,IAAI,CAACF,WAAW;gBACd,4CAA4C;gBAC5C;YACF;QACF;QAEA,KAAK,MAAMhD,OAAOiC,gBAAiB;YACjC,IACE,AAACY,2BAA2B,CAACjB,eAAeU,YAAY,CAACtC,IAAI,IAC5D8C,uBAAuB,CAAClB,eAAec,QAAQ,CAAC,CAAC,EAAEM,UAAU,EAAEhD,IAAI,CAAC,CAAC,EACtE;gBACA+C,eAAeI,IAAI,CAACnD;YACtB;QACF;QACA,IAAI+C,eAAeG,MAAM,KAAK,GAAG;YAC/B;QACF;QACAzD,IAAI2D,IAAI,CACN,CAAC,wCAAwC,CAAC,EAC1CxD,OAAO,4CAA4C;QAGrD,IAAIA,MAAM;YACR,8DAA8D;YAC9D;QACF;QACA,MAAMyD,WAAW,MAAMC,QAAQC,GAAG,CAChCR,eAAeS,GAAG,CAAC,CAACxD,MAAQD,aAAaC;QAG3C,IAAK,IAAIyD,IAAI,GAAGA,IAAIJ,SAASH,MAAM,EAAEO,IAAK;YACxC,MAAMzD,MAAM+C,cAAc,CAACU,EAAE;YAC7B,MAAMpB,UAAUgB,QAAQ,CAACI,EAAE;YAE3B,IAAIZ,yBAAyB;gBAC3BT,gBAAgBpC,KAAKqC;YACvB;YACA,IAAIS,qBAAqB;gBACvBL,aAAa,CAAC,EAAEO,UAAU,EAAEhD,IAAI,CAAC,EAAEqC;YACrC;QACF;QAEA,MAAM7C,SAASkE,SAAS,CACtBpC,cACAO,KAAK8B,SAAS,CAAC/B,gBAAgB,MAAM,KAAKF;QAE5CjC,IAAI2D,IAAI,CACN;IAEJ,EAAE,OAAOQ,KAAK;QACZnE,IAAIoE,KAAK,CACP,CAAC,yFAAyF,CAAC;QAE7FC,QAAQD,KAAK,CAACD;IAChB;AACF"}