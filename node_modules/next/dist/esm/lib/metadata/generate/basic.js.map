{"version": 3, "sources": ["../../../../src/lib/metadata/generate/basic.tsx"], "names": ["React", "Meta", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MultiMeta", "ViewportMetaKeys", "resolveViewportLayout", "viewport", "resolved", "viewportKey_", "viewportKey", "value", "ViewportMeta", "name", "content", "themeColor", "map", "color", "media", "colorScheme", "BasicMeta", "metadata", "meta", "charSet", "title", "absolute", "description", "applicationName", "authors", "author", "url", "link", "rel", "href", "toString", "manifest", "crossOrigin", "generator", "keywords", "join", "referrer", "creator", "publisher", "robots", "basic", "googleBot", "abstract", "archives", "archive", "assets", "asset", "bookmarks", "bookmark", "category", "classification", "other", "Object", "entries", "Array", "isArray", "contentItem", "ItunesMeta", "itunes", "appId", "appArgument", "formatDetectionKeys", "FormatDetectionMeta", "formatDetection", "key", "AppleWebAppMeta", "appleWebApp", "capable", "startupImage", "statusBarStyle", "image", "VerificationMeta", "verification", "namePrefix", "contents", "google", "yahoo", "yandex", "me"], "mappings": ";AAOA,OAAOA,WAAW,QAAO;AACzB,SAASC,IAAI,EAAEC,UAAU,EAAEC,SAAS,QAAQ,SAAQ;AACpD,SAASC,gBAAgB,QAAQ,eAAc;AAE/C,0DAA0D;AAC1D,SAASC,sBAAsBC,QAAkB;IAC/C,IAAIC,WAA0B;IAE9B,IAAID,YAAY,OAAOA,aAAa,UAAU;QAC5CC,WAAW;QACX,IAAK,MAAMC,gBAAgBJ,iBAAkB;YAC3C,MAAMK,cAAcD;YACpB,IAAIC,eAAeH,UAAU;gBAC3B,IAAII,QAAQJ,QAAQ,CAACG,YAAY;gBACjC,IAAI,OAAOC,UAAU,WAAWA,QAAQA,QAAQ,QAAQ;gBACxD,IAAIH,UAAUA,YAAY;gBAC1BA,YAAY,CAAC,EAAEH,gBAAgB,CAACK,YAAY,CAAC,CAAC,EAAEC,MAAM,CAAC;YACzD;QACF;IACF;IACA,OAAOH;AACT;AAEA,OAAO,SAASI,aAAa,EAAEL,QAAQ,EAAkC;IACvE,OAAOJ,WAAW;QAChBD,KAAK;YAAEW,MAAM;YAAYC,SAASR,sBAAsBC;QAAU;WAC9DA,SAASQ,UAAU,GACnBR,SAASQ,UAAU,CAACC,GAAG,CAAC,CAACD,aACvBb,KAAK;gBACHW,MAAM;gBACNC,SAASC,WAAWE,KAAK;gBACzBC,OAAOH,WAAWG,KAAK;YACzB,MAEF,EAAE;QACNhB,KAAK;YAAEW,MAAM;YAAgBC,SAASP,SAASY,WAAW;QAAC;KAC5D;AACH;AAEA,OAAO,SAASC,UAAU,EAAEC,QAAQ,EAAkC;QAwBhCA,oBAIFA,kBACGA;IA5BrC,OAAOlB,WAAW;sBAChB,KAACmB;YAAKC,SAAQ;;QACdF,SAASG,KAAK,KAAK,QAAQH,SAASG,KAAK,CAACC,QAAQ,iBAChD,KAACD;sBAAOH,SAASG,KAAK,CAACC,QAAQ;aAC7B;QACJvB,KAAK;YAAEW,MAAM;YAAeC,SAASO,SAASK,WAAW;QAAC;QAC1DxB,KAAK;YAAEW,MAAM;YAAoBC,SAASO,SAASM,eAAe;QAAC;WAC/DN,SAASO,OAAO,GAChBP,SAASO,OAAO,CAACZ,GAAG,CAAC,CAACa,SAAW;gBAC/BA,OAAOC,GAAG,iBACR,KAACC;oBAAKC,KAAI;oBAASC,MAAMJ,OAAOC,GAAG,CAACI,QAAQ;qBAC1C;gBACJhC,KAAK;oBAAEW,MAAM;oBAAUC,SAASe,OAAOhB,IAAI;gBAAC;aAC7C,IACD,EAAE;QACNQ,SAASc,QAAQ,iBACf,KAACJ;YACCC,KAAI;YACJC,MAAMZ,SAASc,QAAQ,CAACD,QAAQ;YAChCE,aAAY;aAEZ;QACJlC,KAAK;YAAEW,MAAM;YAAaC,SAASO,SAASgB,SAAS;QAAC;QACtDnC,KAAK;YAAEW,MAAM;YAAYC,OAAO,GAAEO,qBAAAA,SAASiB,QAAQ,qBAAjBjB,mBAAmBkB,IAAI,CAAC;QAAK;QAC/DrC,KAAK;YAAEW,MAAM;YAAYC,SAASO,SAASmB,QAAQ;QAAC;QACpDtC,KAAK;YAAEW,MAAM;YAAWC,SAASO,SAASoB,OAAO;QAAC;QAClDvC,KAAK;YAAEW,MAAM;YAAaC,SAASO,SAASqB,SAAS;QAAC;QACtDxC,KAAK;YAAEW,MAAM;YAAUC,OAAO,GAAEO,mBAAAA,SAASsB,MAAM,qBAAftB,iBAAiBuB,KAAK;QAAC;QACvD1C,KAAK;YAAEW,MAAM;YAAaC,OAAO,GAAEO,oBAAAA,SAASsB,MAAM,qBAAftB,kBAAiBwB,SAAS;QAAC;QAC9D3C,KAAK;YAAEW,MAAM;YAAYC,SAASO,SAASyB,QAAQ;QAAC;WAChDzB,SAAS0B,QAAQ,GACjB1B,SAAS0B,QAAQ,CAAC/B,GAAG,CAAC,CAACgC,wBACrB,KAACjB;gBAAKC,KAAI;gBAAWC,MAAMe;kBAE7B,EAAE;WACF3B,SAAS4B,MAAM,GACf5B,SAAS4B,MAAM,CAACjC,GAAG,CAAC,CAACkC,sBAAU,KAACnB;gBAAKC,KAAI;gBAASC,MAAMiB;kBACxD,EAAE;WACF7B,SAAS8B,SAAS,GAClB9B,SAAS8B,SAAS,CAACnC,GAAG,CAAC,CAACoC,yBACtB,KAACrB;gBAAKC,KAAI;gBAAYC,MAAMmB;kBAE9B,EAAE;QACNlD,KAAK;YAAEW,MAAM;YAAYC,SAASO,SAASgC,QAAQ;QAAC;QACpDnD,KAAK;YAAEW,MAAM;YAAkBC,SAASO,SAASiC,cAAc;QAAC;WAC5DjC,SAASkC,KAAK,GACdC,OAAOC,OAAO,CAACpC,SAASkC,KAAK,EAAEvC,GAAG,CAAC,CAAC,CAACH,MAAMC,QAAQ;YACjD,IAAI4C,MAAMC,OAAO,CAAC7C,UAAU;gBAC1B,OAAOA,QAAQE,GAAG,CAAC,CAAC4C,cAClB1D,KAAK;wBAAEW;wBAAMC,SAAS8C;oBAAY;YAEtC,OAAO;gBACL,OAAO1D,KAAK;oBAAEW;oBAAMC;gBAAQ;YAC9B;QACF,KACA,EAAE;KACP;AACH;AAEA,OAAO,SAAS+C,WAAW,EAAEC,MAAM,EAA0C;IAC3E,IAAI,CAACA,QAAQ,OAAO;IACpB,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAE,GAAGF;IAC/B,IAAIhD,UAAU,CAAC,OAAO,EAAEiD,MAAM,CAAC;IAC/B,IAAIC,aAAa;QACflD,WAAW,CAAC,eAAe,EAAEkD,YAAY,CAAC;IAC5C;IACA,qBAAO,KAAC1C;QAAKT,MAAK;QAAmBC,SAASA;;AAChD;AAEA,MAAMmD,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AACD,OAAO,SAASC,oBAAoB,EAClCC,eAAe,EAGhB;IACC,IAAI,CAACA,iBAAiB,OAAO;IAC7B,IAAIrD,UAAU;IACd,KAAK,MAAMsD,OAAOH,oBAAqB;QACrC,IAAIG,OAAOD,iBAAiB;YAC1B,IAAIrD,SAASA,WAAW;YACxBA,WAAW,CAAC,EAAEsD,IAAI,GAAG,CAAC;QACxB;IACF;IACA,qBAAO,KAAC9C;QAAKT,MAAK;QAAmBC,SAASA;;AAChD;AAEA,OAAO,SAASuD,gBAAgB,EAC9BC,WAAW,EAGZ;IACC,IAAI,CAACA,aAAa,OAAO;IAEzB,MAAM,EAAEC,OAAO,EAAE/C,KAAK,EAAEgD,YAAY,EAAEC,cAAc,EAAE,GAAGH;IAEzD,OAAOnE,WAAW;QAChBoE,UACIrE,KAAK;YAAEW,MAAM;YAAgCC,SAAS;QAAM,KAC5D;QACJZ,KAAK;YAAEW,MAAM;YAA8BC,SAASU;QAAM;QAC1DgD,eACIA,aAAaxD,GAAG,CAAC,CAAC0D,sBAChB,KAAC3C;gBACCE,MAAMyC,MAAM5C,GAAG;gBACfZ,OAAOwD,MAAMxD,KAAK;gBAClBc,KAAI;kBAGR;QACJyC,iBACIvE,KAAK;YACHW,MAAM;YACNC,SAAS2D;QACX,KACA;KACL;AACH;AAEA,OAAO,SAASE,iBAAiB,EAC/BC,YAAY,EAGb;IACC,IAAI,CAACA,cAAc,OAAO;IAE1B,OAAOzE,WAAW;QAChBC,UAAU;YACRyE,YAAY;YACZC,UAAUF,aAAaG,MAAM;QAC/B;QACA3E,UAAU;YAAEyE,YAAY;YAASC,UAAUF,aAAaI,KAAK;QAAC;QAC9D5E,UAAU;YACRyE,YAAY;YACZC,UAAUF,aAAaK,MAAM;QAC/B;QACA7E,UAAU;YAAEyE,YAAY;YAAMC,UAAUF,aAAaM,EAAE;QAAC;WACpDN,aAAarB,KAAK,GAClBC,OAAOC,OAAO,CAACmB,aAAarB,KAAK,EAAEvC,GAAG,CAAC,CAAC,CAACoD,KAAKzD,MAAM,GAClDP,UAAU;gBAAEyE,YAAYT;gBAAKU,UAAUnE;YAAM,MAE/C,EAAE;KACP;AACH"}