{"version": 3, "sources": ["../../src/lib/is-serializable-props.ts"], "names": ["isPlainObject", "getObjectClassLabel", "regexpPlainIdentifier", "SerializableError", "Error", "constructor", "page", "method", "path", "message", "isSerializableProps", "input", "visit", "visited", "value", "has", "get", "set", "isSerializable", "refs", "type", "Object", "entries", "every", "key", "nestedV<PERSON>ue", "nextPath", "test", "JSON", "stringify", "newRefs", "Map", "Array", "isArray", "index", "prototype", "toString", "call"], "mappings": "AAAA,SACEA,aAAa,EACbC,mBAAmB,QACd,gCAA+B;AAEtC,MAAMC,wBAAwB;AAE9B,OAAO,MAAMC,0BAA0BC;IACrCC,YAAYC,IAAY,EAAEC,MAAc,EAAEC,IAAY,EAAEC,OAAe,CAAE;QACvE,KAAK,CACHD,OACI,CAAC,oBAAoB,EAAEA,KAAK,mBAAmB,EAAED,OAAO,OAAO,EAAED,KAAK,YAAY,EAAEG,QAAQ,CAAC,GAC7F,CAAC,wCAAwC,EAAEF,OAAO,OAAO,EAAED,KAAK,YAAY,EAAEG,QAAQ,CAAC;IAE/F;AACF;AAEA,OAAO,SAASC,oBACdJ,IAAY,EACZC,MAAc,EACdI,KAAU;IAEV,IAAI,CAACX,cAAcW,QAAQ;QACzB,MAAM,IAAIR,kBACRG,MACAC,QACA,IACA,CAAC,8CAA8C,EAAEA,OAAO,sCAAsC,EAAEN,oBAC9FU,OACA,IAAI,CAAC;IAEX;IAEA,SAASC,MAAMC,OAAyB,EAAEC,KAAU,EAAEN,IAAY;QAChE,IAAIK,QAAQE,GAAG,CAACD,QAAQ;YACtB,MAAM,IAAIX,kBACRG,MACAC,QACAC,MACA,CAAC,+DAA+D,EAC9DK,QAAQG,GAAG,CAACF,UAAU,SACvB,IAAI,CAAC;QAEV;QAEAD,QAAQI,GAAG,CAACH,OAAON;IACrB;IAEA,SAASU,eACPC,IAAsB,EACtBL,KAAU,EACVN,IAAY;QAEZ,MAAMY,OAAO,OAAON;QACpB,IACE,iDAAiD;QACjDA,UAAU,QACV,iEAAiE;QACjE,cAAc;QACd,EAAE;QACF,yEAAyE;QACzE,kCAAkC;QAClCM,SAAS,aACTA,SAAS,YACTA,SAAS,UACT;YACA,OAAO;QACT;QAEA,IAAIA,SAAS,aAAa;YACxB,MAAM,IAAIjB,kBACRG,MACAC,QACAC,MACA;QAEJ;QAEA,IAAIR,cAAcc,QAAQ;YACxBF,MAAMO,MAAML,OAAON;YAEnB,IACEa,OAAOC,OAAO,CAACR,OAAOS,KAAK,CAAC,CAAC,CAACC,KAAKC,YAAY;gBAC7C,MAAMC,WAAWxB,sBAAsByB,IAAI,CAACH,OACxC,CAAC,EAAEhB,KAAK,CAAC,EAAEgB,IAAI,CAAC,GAChB,CAAC,EAAEhB,KAAK,CAAC,EAAEoB,KAAKC,SAAS,CAACL,KAAK,CAAC,CAAC;gBAErC,MAAMM,UAAU,IAAIC,IAAIZ;gBACxB,OACED,eAAeY,SAASN,KAAKE,aAC7BR,eAAeY,SAASL,aAAaC;YAEzC,IACA;gBACA,OAAO;YACT;YAEA,MAAM,IAAIvB,kBACRG,MACAC,QACAC,MACA,CAAC,+CAA+C,CAAC;QAErD;QAEA,IAAIwB,MAAMC,OAAO,CAACnB,QAAQ;YACxBF,MAAMO,MAAML,OAAON;YAEnB,IACEM,MAAMS,KAAK,CAAC,CAACE,aAAaS;gBACxB,MAAMJ,UAAU,IAAIC,IAAIZ;gBACxB,OAAOD,eAAeY,SAASL,aAAa,CAAC,EAAEjB,KAAK,CAAC,EAAE0B,MAAM,CAAC,CAAC;YACjE,IACA;gBACA,OAAO;YACT;YAEA,MAAM,IAAI/B,kBACRG,MACAC,QACAC,MACA,CAAC,8CAA8C,CAAC;QAEpD;QAEA,0CAA0C;QAC1C,0DAA0D;QAC1D,MAAM,IAAIL,kBACRG,MACAC,QACAC,MACA,MACEY,OACA,MACCA,CAAAA,SAAS,WACN,CAAC,GAAG,EAAEC,OAAOc,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACvB,OAAO,EAAE,CAAC,GAC/C,EAAC,IACL;IAEN;IAEA,OAAOI,eAAe,IAAIa,OAAOpB,OAAO;AAC1C"}