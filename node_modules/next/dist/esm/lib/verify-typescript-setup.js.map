{"version": 3, "sources": ["../../src/lib/verify-typescript-setup.ts"], "names": ["bold", "cyan", "red", "yellow", "path", "hasNecessaryDependencies", "semver", "CompileError", "log", "getTypeScriptIntent", "writeAppTypeDeclarations", "writeConfigurationDefaults", "installDependencies", "isCI", "missingDepsError", "requiredPackages", "file", "pkg", "exportsRestrict", "verifyTypeScriptSetup", "dir", "distDir", "cacheDir", "intentDirs", "tsconfigPath", "typeCheckPreflight", "disableStaticImages", "hasAppDir", "hasPagesDir", "resolvedTsConfigPath", "join", "deps", "intent", "version", "missing", "length", "console", "catch", "err", "error", "command", "tsPath", "resolved", "get", "ts", "Promise", "resolve", "require", "lt", "warn", "firstTimeSetup", "baseDir", "imageImportsEnabled", "result", "runTypeCheck", "message", "process", "exit", "env", "JEST_WORKER_ID", "Error"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,eAAc;AACtD,OAAOC,UAAU,OAAM;AAEvB,SAASC,wBAAwB,QAAQ,+BAA8B;AAEvE,OAAOC,YAAY,4BAA2B;AAC9C,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,YAAYC,SAAS,sBAAqB;AAE1C,SAASC,mBAAmB,QAAQ,mCAAkC;AAEtE,SAASC,wBAAwB,QAAQ,wCAAuC;AAChF,SAASC,0BAA0B,QAAQ,0CAAyC;AACpF,SAASC,mBAAmB,QAAQ,yBAAwB;AAC5D,SAASC,IAAI,QAAQ,uBAAsB;AAC3C,SAASC,gBAAgB,QAAQ,sCAAqC;AAEtE,MAAMC,mBAAmB;IACvB;QACEC,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;IACA;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;IACA;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;CACD;AAED,OAAO,eAAeC,sBAAsB,EAC1CC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,SAAS,EACTC,WAAW,EAWZ;IACC,MAAMC,uBAAuBzB,KAAK0B,IAAI,CAACV,KAAKI;IAE5C,IAAI;YAaEO;QAZJ,wCAAwC;QACxC,MAAMC,SAAS,MAAMvB,oBAAoBW,KAAKG,YAAYC;QAC1D,IAAI,CAACQ,QAAQ;YACX,OAAO;gBAAEC,SAAS;YAAK;QACzB;QAEA,4DAA4D;QAC5D,IAAIF,OAA8B,MAAM1B,yBACtCe,KACAL;QAGF,IAAIgB,EAAAA,gBAAAA,KAAKG,OAAO,qBAAZH,cAAcI,MAAM,IAAG,GAAG;YAC5B,IAAItB,MAAM;gBACR,4DAA4D;gBAC5D,2DAA2D;gBAC3DC,iBAAiBM,KAAKW,KAAKG,OAAO;YACpC;YACAE,QAAQ5B,GAAG,CACTR,KACEG,OACE,CAAC,gGAAgG,CAAC,KAGpG,OACA,4BACA,SACAH,KACE,gEACEC,KAAK,mBACL,sFAEJ;YAEJ,MAAMW,oBAAoBQ,KAAKW,KAAKG,OAAO,EAAE,MAAMG,KAAK,CAAC,CAACC;gBACxD,IAAIA,OAAO,OAAOA,QAAQ,YAAY,aAAaA,KAAK;oBACtDF,QAAQG,KAAK,CACX,CAAC,+FAA+F,CAAC,GAC/F,AAACD,IAAYE,OAAO,GACpB;gBAEN;gBACA,MAAMF;YACR;YACAP,OAAO,MAAM1B,yBAAyBe,KAAKL;QAC7C;QAEA,8CAA8C;QAC9C,MAAM0B,SAASV,KAAKW,QAAQ,CAACC,GAAG,CAAC;QACjC,MAAMC,KAAM,MAAMC,QAAQC,OAAO,CAC/BC,QAAQN;QAGV,IAAInC,OAAO0C,EAAE,CAACJ,GAAGX,OAAO,EAAE,UAAU;YAClCzB,IAAIyC,IAAI,CACN,CAAC,yHAAyH,EAAEL,GAAGX,OAAO,CAAC,CAAC;QAE5I;QAEA,+DAA+D;QAC/D,MAAMtB,2BACJiC,IACAf,sBACAG,OAAOkB,cAAc,EACrBvB,WACAN,SACAO;QAEF,qEAAqE;QACrE,kBAAkB;QAClB,MAAMlB,yBAAyB;YAC7ByC,SAAS/B;YACTgC,qBAAqB,CAAC1B;YACtBE;YACAD;QACF;QAEA,IAAI0B;QACJ,IAAI5B,oBAAoB;YACtB,MAAM,EAAE6B,YAAY,EAAE,GAAGP,QAAQ;YAEjC,yEAAyE;YACzEM,SAAS,MAAMC,aACbV,IACAxB,KACAC,SACAQ,sBACAP,UACAK;QAEJ;QACA,OAAO;YAAE0B;YAAQpB,SAASW,GAAGX,OAAO;QAAC;IACvC,EAAE,OAAOK,KAAK;QACZ,+DAA+D;QAC/D,IAAIA,eAAe/B,cAAc;YAC/B6B,QAAQG,KAAK,CAACrC,IAAI;YAClBkC,QAAQG,KAAK,CAACD,IAAIiB,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf;QAEA;;;;KAIC,GAED,mEAAmE;QACnE,IAAID,QAAQE,GAAG,CAACC,cAAc,EAAE;YAC9B,IAAIrB,eAAesB,OAAO;gBACxBxB,QAAQG,KAAK,CAACD,IAAIiB,OAAO;YAC3B,OAAO;gBACLnB,QAAQG,KAAK,CAACD;YAChB;YACAkB,QAAQC,IAAI,CAAC;QACf;QACA,kFAAkF;QAClF,MAAMnB;IACR;AACF"}