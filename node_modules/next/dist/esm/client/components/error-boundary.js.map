{"version": 3, "sources": ["../../../src/client/components/error-boundary.tsx"], "names": ["React", "usePathname", "isNextRouterError", "staticGenerationAsyncStorage", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "HandleISRError", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "getDerivedStateFromError", "getDerivedStateFromProps", "props", "state", "pathname", "previousPathname", "render", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "children", "constructor", "setState", "GlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "p", "Error<PERSON>ou<PERSON><PERSON>"], "mappings": "AAAA;;AAEA,OAAOA,WAAW,QAAO;AACzB,SAASC,WAAW,QAAQ,eAAc;AAC1C,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,4BAA4B,QAAQ,6CAA4C;AAEzF,MAAMC,SAAS;IACbC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IACAC,MAAM;QACJC,UAAU;QACVC,YAAY;QACZC,YAAY;QACZC,QAAQ;IACV;AACF;AAwBA,8DAA8D;AAC9D,yDAAyD;AACzD,oCAAoC;AACpC,SAASC,eAAe,KAAyB;IAAzB,IAAA,EAAEb,KAAK,EAAkB,GAAzB;IACtB,MAAMc,QAAQhB,6BAA6BiB,QAAQ;IACnD,IAAID,CAAAA,yBAAAA,MAAOE,YAAY,MAAIF,yBAAAA,MAAOG,kBAAkB,GAAE;QACpDC,QAAQlB,KAAK,CAACA;QACd,MAAMA;IACR;IAEA,OAAO;AACT;AAEA,OAAO,MAAMmB,6BAA6BxB,MAAMyB,SAAS;IASvD,OAAOC,yBAAyBrB,KAAY,EAAE;QAC5C,IAAIH,kBAAkBG,QAAQ;YAC5B,+DAA+D;YAC/D,4GAA4G;YAC5G,MAAMA;QACR;QAEA,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAOsB,yBACLC,KAAgC,EAChCC,KAAgC,EACE;QAClC;;;;;KAKC,GACD,IAAID,MAAME,QAAQ,KAAKD,MAAME,gBAAgB,IAAIF,MAAMxB,KAAK,EAAE;YAC5D,OAAO;gBACLA,OAAO;gBACP0B,kBAAkBH,MAAME,QAAQ;YAClC;QACF;QACA,OAAO;YACLzB,OAAOwB,MAAMxB,KAAK;YAClB0B,kBAAkBH,MAAME,QAAQ;QAClC;IACF;IAMA,0IAA0I;IAC1IE,SAA0B;QACxB,IAAI,IAAI,CAACH,KAAK,CAACxB,KAAK,EAAE;YACpB,qBACE;;kCACE,KAACa;wBAAeb,OAAO,IAAI,CAACwB,KAAK,CAACxB,KAAK;;oBACtC,IAAI,CAACuB,KAAK,CAACK,WAAW;oBACtB,IAAI,CAACL,KAAK,CAACM,YAAY;kCACxB,KAACC,IAAI,CAACP,KAAK,CAACQ,cAAc;wBACxB/B,OAAO,IAAI,CAACwB,KAAK,CAACxB,KAAK;wBACvBgC,OAAO,IAAI,CAACA,KAAK;;;;QAIzB;QAEA,OAAO,IAAI,CAACT,KAAK,CAACU,QAAQ;IAC5B;IA1DAC,YAAYX,KAAgC,CAAE;QAC5C,KAAK,CAACA;aAoCRS,QAAQ;YACN,IAAI,CAACG,QAAQ,CAAC;gBAAEnC,OAAO;YAAK;QAC9B;QArCE,IAAI,CAACwB,KAAK,GAAG;YAAExB,OAAO;YAAM0B,kBAAkB,IAAI,CAACH,KAAK,CAACE,QAAQ;QAAC;IACpE;AAwDF;AAEA,OAAO,SAASW,YAAY,KAAyB;IAAzB,IAAA,EAAEpC,KAAK,EAAkB,GAAzB;IAC1B,MAAMqC,SAA6BrC,yBAAAA,MAAOqC,MAAM;IAChD,qBACE,MAACC;QAAKC,IAAG;;0BACP,KAACC;0BACD,MAACC;;kCACC,KAAC5B;wBAAeb,OAAOA;;kCACvB,KAAC0C;wBAAIC,OAAO5C,OAAOC,KAAK;kCACtB,cAAA,MAAC0C;;8CACC,KAACE;oCAAGD,OAAO5C,OAAOS,IAAI;8CACnB,AAAC,0BACA6B,CAAAA,SAAS,WAAW,QAAO,IAC5B,2CACCA,CAAAA,SAAS,gBAAgB,iBAAgB,IAC1C;;gCAEFA,uBAAS,KAACQ;oCAAEF,OAAO5C,OAAOS,IAAI;8CAAG,AAAC,aAAU6B;qCAAgB;;;;;;;;AAMzE;AAEA,gFAAgF;AAChF,2CAA2C;AAC3C,eAAeD,YAAW;AAE1B;;;CAGC,GAED;;;CAGC,GACD,OAAO,SAASU,cAAc,KAKuB;IALvB,IAAA,EAC5Bf,cAAc,EACdH,WAAW,EACXC,YAAY,EACZI,QAAQ,EAC2C,GALvB;IAM5B,MAAMR,WAAW7B;IACjB,IAAImC,gBAAgB;QAClB,qBACE,KAACZ;YACCM,UAAUA;YACVM,gBAAgBA;YAChBH,aAAaA;YACbC,cAAcA;sBAEbI;;IAGP;IAEA,qBAAO;kBAAGA;;AACZ"}