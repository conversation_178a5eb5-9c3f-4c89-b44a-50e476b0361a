{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/LeftRightDialogHeader.tsx"], "names": ["React", "CloseIcon", "LeftRightDialogHeader", "children", "className", "previous", "next", "close", "buttonLeft", "useRef", "buttonRight", "buttonClose", "nav", "set<PERSON><PERSON>", "useState", "onNav", "useCallback", "el", "useEffect", "root", "getRootNode", "d", "self", "document", "handler", "e", "key", "preventDefault", "stopPropagation", "current", "focus", "ShadowRoot", "a", "activeElement", "HTMLElement", "blur", "addEventListener", "removeEventListener", "div", "data-nextjs-dialog-left-right", "ref", "button", "type", "disabled", "undefined", "aria-disabled", "onClick", "svg", "viewBox", "fill", "xmlns", "title", "path", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "data-nextjs-errors-dialog-left-right-close-button", "aria-label", "span", "aria-hidden"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,SAAS,QAAQ,wBAAuB;AAUjD,MAAMC,wBACJ,SAASA,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACN,GAN8B;IAO7B,MAAMC,aAAaR,MAAMS,MAAM,CAA2B;IAC1D,MAAMC,cAAcV,MAAMS,MAAM,CAA2B;IAC3D,MAAME,cAAcX,MAAMS,MAAM,CAA2B;IAE3D,MAAM,CAACG,KAAKC,OAAO,GAAGb,MAAMc,QAAQ,CAAqB;IACzD,MAAMC,QAAQf,MAAMgB,WAAW,CAAC,CAACC;QAC/BJ,OAAOI;IACT,GAAG,EAAE;IAELjB,MAAMkB,SAAS,CAAC;QACd,IAAIN,OAAO,MAAM;YACf;QACF;QAEA,MAAMO,OAAOP,IAAIQ,WAAW;QAC5B,MAAMC,IAAIC,KAAKC,QAAQ;QAEvB,SAASC,QAAQC,CAAgB;YAC/B,IAAIA,EAAEC,GAAG,KAAK,aAAa;gBACzBD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjB,IAAIpB,WAAWqB,OAAO,EAAE;oBACtBrB,WAAWqB,OAAO,CAACC,KAAK;gBAC1B;gBACAzB,YAAYA;YACd,OAAO,IAAIoB,EAAEC,GAAG,KAAK,cAAc;gBACjCD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjB,IAAIlB,YAAYmB,OAAO,EAAE;oBACvBnB,YAAYmB,OAAO,CAACC,KAAK;gBAC3B;gBACAxB,QAAQA;YACV,OAAO,IAAImB,EAAEC,GAAG,KAAK,UAAU;gBAC7BD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjB,IAAIT,gBAAgBY,YAAY;oBAC9B,MAAMC,IAAIb,KAAKc,aAAa;oBAC5B,IAAID,KAAKA,MAAMrB,YAAYkB,OAAO,IAAIG,aAAaE,aAAa;wBAC9DF,EAAEG,IAAI;wBACN;oBACF;gBACF;gBAEA5B,yBAAAA;YACF;QACF;QAEAY,KAAKiB,gBAAgB,CAAC,WAAWZ;QACjC,IAAIL,SAASE,GAAG;YACdA,EAAEe,gBAAgB,CAAC,WAAWZ;QAChC;QACA,OAAO;YACLL,KAAKkB,mBAAmB,CAAC,WAAWb;YACpC,IAAIL,SAASE,GAAG;gBACdA,EAAEgB,mBAAmB,CAAC,WAAWb;YACnC;QACF;IACF,GAAG;QAACjB;QAAOK;QAAKN;QAAMD;KAAS;IAE/B,2EAA2E;IAC3E,2CAA2C;IAC3CL,MAAMkB,SAAS,CAAC;QACd,IAAIN,OAAO,MAAM;YACf;QACF;QAEA,MAAMO,OAAOP,IAAIQ,WAAW;QAC5B,8CAA8C;QAC9C,IAAID,gBAAgBY,YAAY;YAC9B,MAAMC,IAAIb,KAAKc,aAAa;YAE5B,IAAI5B,YAAY,MAAM;gBACpB,IAAIG,WAAWqB,OAAO,IAAIG,MAAMxB,WAAWqB,OAAO,EAAE;oBAClDrB,WAAWqB,OAAO,CAACM,IAAI;gBACzB;YACF,OAAO,IAAI7B,QAAQ,MAAM;gBACvB,IAAII,YAAYmB,OAAO,IAAIG,MAAMtB,YAAYmB,OAAO,EAAE;oBACpDnB,YAAYmB,OAAO,CAACM,IAAI;gBAC1B;YACF;QACF;IACF,GAAG;QAACvB;QAAKN;QAAMD;KAAS;IAExB,qBACE,MAACiC;QAAIC,+BAA6B;QAACnC,WAAWA;;0BAC5C,MAACQ;gBAAI4B,KAAKzB;;kCACR,KAAC0B;wBACCD,KAAKhC;wBACLkC,MAAK;wBACLC,UAAUtC,YAAY,OAAO,OAAOuC;wBACpCC,iBAAexC,YAAY,OAAO,OAAOuC;wBACzCE,SAASzC,mBAAAA,WAAYuC;kCAErB,cAAA,MAACG;4BACCC,SAAQ;4BACRC,MAAK;4BACLC,OAAM;;8CAEN,KAACC;8CAAM;;8CACP,KAACC;oCACC/B,GAAE;oCACFgC,QAAO;oCACPC,aAAY;oCACZC,eAAc;oCACdC,gBAAe;;;;;kCAIrB,KAACf;wBACCD,KAAK9B;wBACLgC,MAAK;wBACLC,UAAUrC,QAAQ,OAAO,OAAOsC;wBAChCC,iBAAevC,QAAQ,OAAO,OAAOsC;wBACrCE,SAASxC,eAAAA,OAAQsC;kCAEjB,cAAA,MAACG;4BACCC,SAAQ;4BACRC,MAAK;4BACLC,OAAM;;8CAEN,KAACC;8CAAM;;8CACP,KAACC;oCACC/B,GAAE;oCACFgC,QAAO;oCACPC,aAAY;oCACZC,eAAc;oCACdC,gBAAe;;;;;oBAIpBrD;;;YAEFI,sBACC,KAACkC;gBACCgB,mDAAiD;gBACjDjB,KAAK7B;gBACL+B,MAAK;gBACLI,SAASvC;gBACTmD,cAAW;0BAEX,cAAA,KAACC;oBAAKC,eAAY;8BAChB,cAAA,KAAC3D;;iBAGH;;;AAGV;AAEF,SAASC,qBAAqB,GAAE"}