{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/ComponentStackFrameRow.tsx"], "names": ["React", "useOpenInEditor", "HotlinkedText", "EditorLink", "children", "componentStackFrame", "file", "column", "lineNumber", "open", "div", "tabIndex", "role", "onClick", "title", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "line", "x1", "y1", "x2", "y2", "formatLineNumber", "LocationLine", "SourceLocation", "canOpenInEditor", "span", "ComponentStackFrameRow", "component", "data-nextjs-component-stack-frame", "h3", "text"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AAEzB,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,aAAa,QAAQ,mCAAkC;AAEhE,SAASC,WAAW,KAMnB;IANmB,IAAA,EAClBC,QAAQ,EACRC,qBAAqB,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAE,EAIlD,GANmB;IAOlB,MAAMC,OAAOR,gBAAgB;QAC3BK;QACAC;QACAC;IACF;IAEA,qBACE,MAACE;QACCC,UAAU;QACVC,MAAM;QACNC,SAASJ;QACTK,OAAO;;YAENV;0BACD,MAACW;gBACCC,OAAM;gBACNC,SAAQ;gBACRC,MAAK;gBACLC,QAAO;gBACPC,aAAY;gBACZC,eAAc;gBACdC,gBAAe;;kCAEf,KAACC;wBAAKC,GAAE;;kCACR,KAACC;wBAASC,QAAO;;kCACjB,KAACC;wBAAKC,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;;;;;;AAIzC;AAEA,SAASC,iBAAiBxB,UAAkB,EAAED,MAA0B;IACtE,IAAI,CAACA,QAAQ;QACX,OAAOC;IACT;IAEA,OAAO,AAAGA,aAAW,MAAGD;AAC1B;AAEA,SAAS0B,aAAa,KAIrB;IAJqB,IAAA,EACpB5B,mBAAmB,EAGpB,GAJqB;IAKpB,MAAM,EAAEC,IAAI,EAAEE,UAAU,EAAED,MAAM,EAAE,GAAGF;IACrC,qBACE;;YACGC;YAAK;YAAEE,aAAa,AAAC,MAAGwB,iBAAiBxB,YAAYD,UAAQ,MAAK;;;AAGzE;AAEA,SAAS2B,eAAe,KAIvB;IAJuB,IAAA,EACtB7B,mBAAmB,EAGpB,GAJuB;IAKtB,MAAM,EAAEC,IAAI,EAAE6B,eAAe,EAAE,GAAG9B;IAElC,IAAIC,QAAQ6B,iBAAiB;QAC3B,qBACE,KAAChC;YAAWE,qBAAqBA;sBAC/B,cAAA,KAAC+B;0BACC,cAAA,KAACH;oBAAa5B,qBAAqBA;;;;IAI3C;IAEA,qBACE,KAACK;kBACC,cAAA,KAACuB;YAAa5B,qBAAqBA;;;AAGzC;AAEA,OAAO,SAASgC,uBAAuB,KAItC;IAJsC,IAAA,EACrChC,mBAAmB,EAGpB,GAJsC;IAKrC,MAAM,EAAEiC,SAAS,EAAE,GAAGjC;IAEtB,qBACE,MAACK;QAAI6B,mCAAiC;;0BACpC,KAACC;0BACC,cAAA,KAACtC;oBAAcuC,MAAMH;;;0BAEvB,KAACJ;gBAAe7B,qBAAqBA;;;;AAG3C"}