{"version": 3, "sources": ["../../../src/server/async-storage/draft-mode-provider.ts"], "names": ["COOKIE_NAME_PRERENDER_BYPASS", "checkIsOnDemandRevalidate", "DraftModeProvider", "constructor", "previewProps", "req", "cookies", "mutableCookies", "isOnDemandRevalidate", "cookieValue", "get", "value", "isEnabled", "Boolean", "previewModeId", "_previewModeId", "_mutableCookies", "enable", "Error", "set", "name", "httpOnly", "sameSite", "process", "env", "NODE_ENV", "secure", "path", "disable", "expires", "Date"], "mappings": "AAMA,SACEA,4BAA4B,EAC5BC,yBAAyB,QACpB,eAAc;AAGrB,OAAO,MAAMC;IAaXC,YACEC,YAA2C,EAC3CC,GAA6D,EAC7DC,OAA+B,EAC/BC,cAA+B,CAC/B;YAOoBD;QANpB,mEAAmE;QACnE,4DAA4D;QAC5D,MAAME,uBACJJ,gBACAH,0BAA0BI,KAAKD,cAAcI,oBAAoB;QAEnE,MAAMC,eAAcH,eAAAA,QAAQI,GAAG,CAACV,kDAAZM,aAA2CK,KAAK;QAEpE,IAAI,CAACC,SAAS,GAAGC,QACf,CAACL,wBACCC,eACAL,gBACAK,gBAAgBL,aAAaU,aAAa;QAG9C,IAAI,CAACC,cAAc,GAAGX,gCAAAA,aAAcU,aAAa;QACjD,IAAI,CAACE,eAAe,GAAGT;IACzB;IAEAU,SAAS;QACP,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;YACxB,MAAM,IAAIG,MACR;QAEJ;QAEA,IAAI,CAACF,eAAe,CAACG,GAAG,CAAC;YACvBC,MAAMpB;YACNW,OAAO,IAAI,CAACI,cAAc;YAC1BM,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCE,MAAM;QACR;IACF;IAEAC,UAAU;QACR,2DAA2D;QAC3D,oDAAoD;QACpD,wEAAwE;QACxE,IAAI,CAACZ,eAAe,CAACG,GAAG,CAAC;YACvBC,MAAMpB;YACNW,OAAO;YACPU,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCE,MAAM;YACNE,SAAS,IAAIC,KAAK;QACpB;IACF;AACF"}