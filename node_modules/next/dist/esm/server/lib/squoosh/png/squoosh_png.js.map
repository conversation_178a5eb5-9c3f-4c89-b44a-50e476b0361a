{"version": 3, "sources": ["../../../../../src/server/lib/squoosh/png/squoosh_png.js"], "names": ["wasm", "cachedTextDecoder", "TextDecoder", "ignoreBOM", "fatal", "decode", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "getStringFromWasm0", "ptr", "len", "subarray", "cachegetUint8ClampedMemory0", "getUint8ClampedMemory0", "Uint8ClampedArray", "getClampedArrayU8FromWasm0", "heap", "Array", "fill", "undefined", "push", "heap_next", "length", "addHeapObject", "obj", "idx", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "getArrayU8FromWasm0", "encode", "data", "width", "height", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "getObject", "dropObject", "takeObject", "ret", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "wbg", "__wbg_newwithownedu8clampedarrayandsh_787b2db8ea6bfd62", "arg0", "arg1", "arg2", "arg3", "v0", "ImageData", "__wbindgen_throw", "Error", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module", "cleanup"], "mappings": "AAAA,IAAIA;AAEJ,IAAIC,oBAAoB,IAAIC,YAAY,SAAS;IAC/CC,WAAW;IACXC,OAAO;AACT;AAEAH,kBAAkBI,MAAM;AAExB,IAAIC,uBAAuB;AAC3B,SAASC;IACP,IACED,yBAAyB,QACzBA,qBAAqBE,MAAM,KAAKR,KAAKS,MAAM,CAACD,MAAM,EAClD;QACAF,uBAAuB,IAAII,WAAWV,KAAKS,MAAM,CAACD,MAAM;IAC1D;IACA,OAAOF;AACT;AAEA,SAASK,mBAAmBC,GAAG,EAAEC,GAAG;IAClC,OAAOZ,kBAAkBI,MAAM,CAACE,kBAAkBO,QAAQ,CAACF,KAAKA,MAAMC;AACxE;AAEA,IAAIE,8BAA8B;AAClC,SAASC;IACP,IACED,gCAAgC,QAChCA,4BAA4BP,MAAM,KAAKR,KAAKS,MAAM,CAACD,MAAM,EACzD;QACAO,8BAA8B,IAAIE,kBAAkBjB,KAAKS,MAAM,CAACD,MAAM;IACxE;IACA,OAAOO;AACT;AAEA,SAASG,2BAA2BN,GAAG,EAAEC,GAAG;IAC1C,OAAOG,yBAAyBF,QAAQ,CAACF,MAAM,GAAGA,MAAM,IAAIC;AAC9D;AAEA,MAAMM,OAAO,IAAIC,MAAM,IAAIC,IAAI,CAACC;AAEhCH,KAAKI,IAAI,CAACD,WAAW,MAAM,MAAM;AAEjC,IAAIE,YAAYL,KAAKM,MAAM;AAE3B,SAASC,cAAcC,GAAG;IACxB,IAAIH,cAAcL,KAAKM,MAAM,EAAEN,KAAKI,IAAI,CAACJ,KAAKM,MAAM,GAAG;IACvD,MAAMG,MAAMJ;IACZA,YAAYL,IAAI,CAACS,IAAI;IAErBT,IAAI,CAACS,IAAI,GAAGD;IACZ,OAAOC;AACT;AAEA,IAAIC,kBAAkB;AAEtB,SAASC,kBAAkBC,GAAG,EAAEC,MAAM;IACpC,MAAMpB,MAAMoB,OAAOD,IAAIN,MAAM,GAAG;IAChClB,kBAAkB0B,GAAG,CAACF,KAAKnB,MAAM;IACjCiB,kBAAkBE,IAAIN,MAAM;IAC5B,OAAOb;AACT;AAEA,IAAIsB,uBAAuB;AAC3B,SAASC;IACP,IACED,yBAAyB,QACzBA,qBAAqB1B,MAAM,KAAKR,KAAKS,MAAM,CAACD,MAAM,EAClD;QACA0B,uBAAuB,IAAIE,WAAWpC,KAAKS,MAAM,CAACD,MAAM;IAC1D;IACA,OAAO0B;AACT;AAEA,SAASG,oBAAoBzB,GAAG,EAAEC,GAAG;IACnC,OAAON,kBAAkBO,QAAQ,CAACF,MAAM,GAAGA,MAAM,IAAIC;AACvD;AACA;;;;;CAKC,GACD,OAAO,SAASyB,OAAOC,IAAI,EAAEC,KAAK,EAAEC,MAAM;IACxC,IAAI;QACF,MAAMC,SAAS1C,KAAK2C,+BAA+B,CAAC,CAAC;QACrD,IAAIC,OAAOd,kBAAkBS,MAAMvC,KAAK6C,iBAAiB;QACzD,IAAIC,OAAOjB;QACX7B,KAAKsC,MAAM,CAACI,QAAQE,MAAME,MAAMN,OAAOC;QACvC,IAAIM,KAAKZ,iBAAiB,CAACO,SAAS,IAAI,EAAE;QAC1C,IAAIM,KAAKb,iBAAiB,CAACO,SAAS,IAAI,EAAE;QAC1C,IAAIO,KAAKZ,oBAAoBU,IAAIC,IAAIE,KAAK;QAC1ClD,KAAKmD,eAAe,CAACJ,IAAIC,KAAK;QAC9B,OAAOC;IACT,SAAU;QACRjD,KAAK2C,+BAA+B,CAAC;IACvC;AACF;AAEA,SAASS,UAAUxB,GAAG;IACpB,OAAOT,IAAI,CAACS,IAAI;AAClB;AAEA,SAASyB,WAAWzB,GAAG;IACrB,IAAIA,MAAM,IAAI;IACdT,IAAI,CAACS,IAAI,GAAGJ;IACZA,YAAYI;AACd;AAEA,SAAS0B,WAAW1B,GAAG;IACrB,MAAM2B,MAAMH,UAAUxB;IACtByB,WAAWzB;IACX,OAAO2B;AACT;AACA;;;CAGC,GACD,OAAO,SAASlD,OAAOkC,IAAI;IACzB,IAAIK,OAAOd,kBAAkBS,MAAMvC,KAAK6C,iBAAiB;IACzD,IAAIC,OAAOjB;IACX,IAAI0B,MAAMvD,KAAKK,MAAM,CAACuC,MAAME;IAC5B,OAAOQ,WAAWC;AACpB;AAEA,eAAeC,KAAKC,MAAM,EAAEC,OAAO;IACjC,IAAI,OAAOC,aAAa,cAAcF,kBAAkBE,UAAU;QAChE,IAAI,OAAOC,YAAYC,oBAAoB,KAAK,YAAY;YAC1D,OAAO,MAAMD,YAAYC,oBAAoB,CAACJ,QAAQC;QACxD;QAEA,MAAMI,QAAQ,MAAML,OAAOM,WAAW;QACtC,OAAO,MAAMH,YAAYI,WAAW,CAACF,OAAOJ;IAC9C,OAAO;QACL,MAAMO,WAAW,MAAML,YAAYI,WAAW,CAACP,QAAQC;QAEvD,IAAIO,oBAAoBL,YAAYM,QAAQ,EAAE;YAC5C,OAAO;gBAAED;gBAAUR;YAAO;QAC5B,OAAO;YACL,OAAOQ;QACT;IACF;AACF;AAEA,eAAeE,KAAKC,KAAK;IACvB,MAAMV,UAAU,CAAC;IACjBA,QAAQW,GAAG,GAAG,CAAC;IACfX,QAAQW,GAAG,CAACC,sDAAsD,GAChE,SAAUC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI;QAC9B,IAAIC,KAAKzD,2BAA2BqD,MAAMC,MAAMtB,KAAK;QACrDlD,KAAKmD,eAAe,CAACoB,MAAMC,OAAO;QAClC,IAAIjB,MAAM,IAAIqB,UAAUD,IAAIF,SAAS,GAAGC,SAAS;QACjD,OAAOhD,cAAc6B;IACvB;IACFG,QAAQW,GAAG,CAACQ,gBAAgB,GAAG,SAAUN,IAAI,EAAEC,IAAI;QACjD,MAAM,IAAIM,MAAMnE,mBAAmB4D,MAAMC;IAC3C;IAEA,IACE,OAAOJ,UAAU,YAChB,OAAOW,YAAY,cAAcX,iBAAiBW,WAClD,OAAOC,QAAQ,cAAcZ,iBAAiBY,KAC/C;QACAZ,QAAQa,MAAMb;IAChB;IAEA,MAAM,EAAEH,QAAQ,EAAER,MAAM,EAAE,GAAG,MAAMD,KAAK,MAAMY,OAAOV;IAErD1D,OAAOiE,SAASiB,OAAO;IACvBf,KAAKgB,sBAAsB,GAAG1B;IAE9B,OAAOzD;AACT;AAEA,eAAemE,KAAI;AAEnB,+DAA+D;AAC/D,OAAO,SAASiB;IACdpF,OAAO;IACPe,8BAA8B;IAC9BT,uBAAuB;IACvB4B,uBAAuB;AACzB"}