{"version": 3, "sources": ["../../../src/server/lib/app-info-log.ts"], "names": ["loadEnvConfig", "Log", "bold", "purple", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_BUILD", "loadConfig", "getEnabledExperimentalFeatures", "logStartInfo", "networkUrl", "appUrl", "envInfo", "expFeatureInfo", "maxExperimentalFeatures", "Infinity", "bootstrap", "prefixes", "ready", "process", "env", "__NEXT_VERSION", "TURBOPACK", "length", "join", "exp", "slice", "info", "getStartServerInfo", "dir", "dev", "onLoadUserConfig", "userConfig", "userNextConfigExperimental", "experimental", "sort", "a", "b", "loadedEnvFiles", "console", "map", "f", "path"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAW;AACzC,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,IAAI,EAAEC,MAAM,QAAQ,uBAAsB;AACnD,SACEC,wBAAwB,EACxBC,sBAAsB,QACjB,6BAA4B;AACnC,OAAOC,cAAcC,8BAA8B,QAAQ,YAAW;AAEtE,OAAO,SAASC,aAAa,EAC3BC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,cAAc,EACdC,0BAA0BC,QAAQ,EAOnC;IACCb,IAAIc,SAAS,CACX,CAAC,EAAEb,KACDC,OAAO,CAAC,EAAEF,IAAIe,QAAQ,CAACC,KAAK,CAAC,SAAS,EAAEC,QAAQC,GAAG,CAACC,cAAc,CAAC,CAAC,GACpE,EAAEF,QAAQC,GAAG,CAACE,SAAS,GAAG,aAAa,GAAG,CAAC;IAE/C,IAAIX,QAAQ;QACVT,IAAIc,SAAS,CAAC,CAAC,gBAAgB,EAAEL,OAAO,CAAC;IAC3C;IACA,IAAID,YAAY;QACdR,IAAIc,SAAS,CAAC,CAAC,gBAAgB,EAAEN,WAAW,CAAC;IAC/C;IACA,IAAIE,2BAAAA,QAASW,MAAM,EAAErB,IAAIc,SAAS,CAAC,CAAC,gBAAgB,EAAEJ,QAAQY,IAAI,CAAC,MAAM,CAAC;IAE1E,IAAIX,kCAAAA,eAAgBU,MAAM,EAAE;QAC1BrB,IAAIc,SAAS,CAAC,CAAC,iCAAiC,CAAC;QACjD,sCAAsC;QACtC,KAAK,MAAMS,OAAOZ,eAAea,KAAK,CAAC,GAAGZ,yBAA0B;YAClEZ,IAAIc,SAAS,CAAC,CAAC,IAAI,EAAES,IAAI,CAAC;QAC5B;QACA,+DAA+D,GAC/D,IAAIZ,eAAeU,MAAM,GAAGT,yBAAyB;YACnDZ,IAAIc,SAAS,CAAC,CAAC,OAAO,CAAC;QACzB;IACF;IAEA,oCAAoC;IACpCd,IAAIyB,IAAI,CAAC;AACX;AAEA,OAAO,eAAeC,mBACpBC,GAAW,EACXC,GAAY;IAKZ,IAAIjB,iBAA2B,EAAE;IACjC,MAAMN,WACJuB,MAAMzB,2BAA2BC,wBACjCuB,KACA;QACEE,kBAAiBC,UAAU;YACzB,MAAMC,6BAA6BzB,+BACjCwB,WAAWE,YAAY;YAEzBrB,iBAAiBoB,2BAA2BE,IAAI,CAC9C,CAACC,GAAGC,IAAMD,EAAEb,MAAM,GAAGc,EAAEd,MAAM;QAEjC;IACF;IAGF,iDAAiD;IACjD,qDAAqD;IACrD,+BAA+B;IAC/B,IAAIX,UAAoB,EAAE;IAC1B,MAAM,EAAE0B,cAAc,EAAE,GAAGrC,cAAc4B,KAAK,MAAMU,SAAS;IAC7D,IAAID,eAAef,MAAM,GAAG,GAAG;QAC7BX,UAAU0B,eAAeE,GAAG,CAAC,CAACC,IAAMA,EAAEC,IAAI;IAC5C;IAEA,OAAO;QACL9B;QACAC;IACF;AACF"}