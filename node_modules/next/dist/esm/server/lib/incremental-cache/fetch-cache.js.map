{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/fetch-cache.ts"], "names": ["L<PERSON><PERSON><PERSON>", "CACHE_ONE_YEAR", "NEXT_CACHE_SOFT_TAGS_HEADER", "rateLimitedUntil", "memoryCache", "CACHE_TAGS_HEADER", "CACHE_HEADERS_HEADER", "CACHE_STATE_HEADER", "CACHE_REVALIDATE_HEADER", "CACHE_FETCH_URL_HEADER", "CACHE_CONTROL_VALUE_HEADER", "DEBUG", "Boolean", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "fetchRetryWithTimeout", "url", "init", "retryIndex", "controller", "AbortController", "timeout", "setTimeout", "abort", "fetch", "signal", "catch", "err", "console", "log", "finally", "clearTimeout", "<PERSON><PERSON><PERSON><PERSON>", "hasMatchingTags", "arr1", "arr2", "length", "set1", "Set", "set2", "size", "tag", "has", "isAvailable", "ctx", "_requestHeaders", "SUSPENSE_CACHE_URL", "constructor", "headers", "newHeaders", "JSON", "parse", "k", "scHost", "sc<PERSON><PERSON><PERSON><PERSON>", "SUSPENSE_CACHE_BASEPATH", "SUSPENSE_CACHE_AUTH_TOKEN", "scProto", "SUSPENSE_CACHE_PROTO", "cacheEndpoint", "maxMemoryCacheSize", "max", "value", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "resetRequestCache", "reset", "revalidateTag", "args", "tags", "Date", "now", "res", "map", "encodeURIComponent", "join", "method", "next", "internal", "status", "retryAfter", "get", "parseInt", "ok", "warn", "key", "softTags", "kindHint", "fetchIdx", "fetchUrl", "hasFetchKindAndMatchingTags", "start", "fetchParams", "fetchType", "error", "text", "cached", "json", "includes", "push", "cacheState", "age", "lastModified", "Object", "keys", "set", "newValue", "undefined", "existingCache", "existingValue", "every", "field", "fetchCache", "revalidate", "toString"], "mappings": "AAGA,OAAOA,cAAc,+BAA8B;AACnD,SACEC,cAAc,EACdC,2BAA2B,QACtB,yBAAwB;AAE/B,IAAIC,mBAAmB;AACvB,IAAIC;AASJ,MAAMC,oBAAoB;AAC1B,MAAMC,uBAAuB;AAC7B,MAAMC,qBAAqB;AAC3B,MAAMC,0BAA0B;AAChC,MAAMC,yBAAyB;AAC/B,MAAMC,6BAA6B;AAEnC,MAAMC,QAAQC,QAAQC,QAAQC,GAAG,CAACC,wBAAwB;AAE1D,eAAeC,sBACbC,GAAgC,EAChCC,IAAiC,EACjCC,aAAa,CAAC;IAEd,MAAMC,aAAa,IAAIC;IACvB,MAAMC,UAAUC,WAAW;QACzBH,WAAWI,KAAK;IAClB,GAAG;IAEH,OAAOC,MAAMR,KAAK;QAChB,GAAIC,QAAQ,CAAC,CAAC;QACdQ,QAAQN,WAAWM,MAAM;IAC3B,GACGC,KAAK,CAAC,CAACC;QACN,IAAIT,eAAe,GAAG;YACpB,MAAMS;QACR,OAAO;YACL,IAAIjB,OAAO;gBACTkB,QAAQC,GAAG,CAAC,CAAC,iBAAiB,EAAEb,IAAI,OAAO,EAAEE,WAAW,CAAC;YAC3D;YACA,OAAOH,sBAAsBC,KAAKC,MAAMC,aAAa;QACvD;IACF,GACCY,OAAO,CAAC;QACPC,aAAaV;IACf;AACJ;AAEA,eAAe,MAAMW;IAIXC,gBAAgBC,IAAc,EAAEC,IAAc,EAAE;QACtD,IAAID,KAAKE,MAAM,KAAKD,KAAKC,MAAM,EAAE,OAAO;QAExC,MAAMC,OAAO,IAAIC,IAAIJ;QACrB,MAAMK,OAAO,IAAID,IAAIH;QAErB,IAAIE,KAAKG,IAAI,KAAKD,KAAKC,IAAI,EAAE,OAAO;QAEpC,KAAK,IAAIC,OAAOJ,KAAM;YACpB,IAAI,CAACE,KAAKG,GAAG,CAACD,MAAM,OAAO;QAC7B;QAEA,OAAO;IACT;IAEA,OAAOE,YAAYC,GAElB,EAAE;QACD,OAAO,CAAC,CACNA,CAAAA,IAAIC,eAAe,CAAC,mBAAmB,IAAIjC,QAAQC,GAAG,CAACiC,kBAAkB,AAAD;IAE5E;IAEAC,YAAYH,GAAwB,CAAE;QACpC,IAAI,CAACI,OAAO,GAAG,CAAC;QAChB,IAAI,CAACA,OAAO,CAAC,eAAe,GAAG;QAE/B,IAAI3C,wBAAwBuC,IAAIC,eAAe,EAAE;YAC/C,MAAMI,aAAaC,KAAKC,KAAK,CAC3BP,IAAIC,eAAe,CAACxC,qBAAqB;YAE3C,IAAK,MAAM+C,KAAKH,WAAY;gBAC1B,IAAI,CAACD,OAAO,CAACI,EAAE,GAAGH,UAAU,CAACG,EAAE;YACjC;YACA,OAAOR,IAAIC,eAAe,CAACxC,qBAAqB;QAClD;QACA,MAAMgD,SACJT,IAAIC,eAAe,CAAC,mBAAmB,IAAIjC,QAAQC,GAAG,CAACiC,kBAAkB;QAE3E,MAAMQ,aACJV,IAAIC,eAAe,CAAC,uBAAuB,IAC3CjC,QAAQC,GAAG,CAAC0C,uBAAuB;QAErC,IAAI3C,QAAQC,GAAG,CAAC2C,yBAAyB,EAAE;YACzC,IAAI,CAACR,OAAO,CACV,gBACD,GAAG,CAAC,OAAO,EAAEpC,QAAQC,GAAG,CAAC2C,yBAAyB,CAAC,CAAC;QACvD;QAEA,IAAIH,QAAQ;YACV,MAAMI,UAAU7C,QAAQC,GAAG,CAAC6C,oBAAoB,IAAI;YACpD,IAAI,CAACC,aAAa,GAAG,CAAC,EAAEF,QAAQ,GAAG,EAAEJ,OAAO,EAAEC,cAAc,GAAG,CAAC;YAChE,IAAI5C,OAAO;gBACTkB,QAAQC,GAAG,CAAC,wBAAwB,IAAI,CAAC8B,aAAa;YACxD;QACF,OAAO,IAAIjD,OAAO;YAChBkB,QAAQC,GAAG,CAAC;QACd;QAEA,IAAIe,IAAIgB,kBAAkB,EAAE;YAC1B,IAAI,CAACzD,aAAa;gBAChB,IAAIO,OAAO;oBACTkB,QAAQC,GAAG,CAAC;gBACd;gBAEA1B,cAAc,IAAIJ,SAAS;oBACzB8D,KAAKjB,IAAIgB,kBAAkB;oBAC3BxB,QAAO,EAAE0B,KAAK,EAAE;4BAeXZ;wBAdH,IAAI,CAACY,OAAO;4BACV,OAAO;wBACT,OAAO,IAAIA,MAAMC,IAAI,KAAK,YAAY;4BACpC,OAAOb,KAAKc,SAAS,CAACF,MAAMG,KAAK,EAAE7B,MAAM;wBAC3C,OAAO,IAAI0B,MAAMC,IAAI,KAAK,SAAS;4BACjC,MAAM,IAAIG,MAAM;wBAClB,OAAO,IAAIJ,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOb,KAAKc,SAAS,CAACF,MAAMK,IAAI,IAAI,IAAI/B,MAAM;wBAChD,OAAO,IAAI0B,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOD,MAAMM,IAAI,CAAChC,MAAM;wBAC1B;wBACA,wCAAwC;wBACxC,OACE0B,MAAMO,IAAI,CAACjC,MAAM,GAChBc,CAAAA,EAAAA,kBAAAA,KAAKc,SAAS,CAACF,MAAMC,IAAI,KAAK,UAAUD,MAAMQ,QAAQ,sBAAtDpB,gBACGd,MAAM,KAAI,CAAA;oBAElB;gBACF;YACF;QACF,OAAO;YACL,IAAI1B,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;QACF;IACF;IAEO0C,oBAA0B;QAC/BpE,+BAAAA,YAAaqE,KAAK;IACpB;IAEA,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAC3C,IAAIjE,OAAO;YACTkB,QAAQC,GAAG,CAAC,iBAAiB8C;QAC/B;QAEA,IAAI,CAACA,KAAKvC,MAAM,EAAE;QAElB,IAAIwC,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC,iBAAiB3B;YAC/B;YACA;QACF;QAEA,IAAI;YACF,MAAM4E,MAAM,MAAM/D,sBAChB,CAAC,EAAE,IAAI,CAAC4C,aAAa,CAAC,mCAAmC,EAAEgB,KACxDI,GAAG,CAAC,CAACtC,MAAQuC,mBAAmBvC,MAChCwC,IAAI,CAAC,KAAK,CAAC,EACd;gBACEC,QAAQ;gBACRlC,SAAS,IAAI,CAACA,OAAO;gBACrB,sCAAsC;gBACtCmC,MAAM;oBAAEC,UAAU;gBAAK;YACzB;YAGF,IAAIN,IAAIO,MAAM,KAAK,KAAK;gBACtB,MAAMC,aAAaR,IAAI9B,OAAO,CAACuC,GAAG,CAAC,kBAAkB;gBACrDrF,mBAAmB0E,KAAKC,GAAG,KAAKW,SAASF;YAC3C;YAEA,IAAI,CAACR,IAAIW,EAAE,EAAE;gBACX,MAAM,IAAIvB,MAAM,CAAC,2BAA2B,EAAEY,IAAIO,MAAM,CAAC,CAAC,CAAC;YAC7D;QACF,EAAE,OAAO1D,KAAK;YACZC,QAAQ8D,IAAI,CAAC,CAAC,yBAAyB,EAAEf,KAAK,CAAC,EAAEhD;QACnD;IACF;IAEA,MAAa4D,IAAI,GAAGb,IAAqC,EAAE;YAqBvDP;QApBF,MAAM,CAACwB,KAAK/C,MAAM,CAAC,CAAC,CAAC,GAAG8B;QACxB,MAAM,EAAEC,IAAI,EAAEiB,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,GAAGnD;QAEzD,IAAIiD,aAAa,SAAS;YACxB,OAAO;QACT;QAEA,IAAIjB,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;YACA,OAAO;QACT;QAEA,qDAAqD;QACrD,qDAAqD;QACrD,yBAAyB;QACzB,IAAIsC,OAAOhE,+BAAAA,YAAaoF,GAAG,CAACI;QAE5B,MAAMK,8BACJ7B,CAAAA,yBAAAA,cAAAA,KAAML,KAAK,qBAAXK,YAAaJ,IAAI,MAAK,WACtB,IAAI,CAAC9B,eAAe,CAAC0C,QAAQ,EAAE,EAAER,KAAKL,KAAK,CAACa,IAAI,IAAI,EAAE;QAExD,8DAA8D;QAC9D,gDAAgD;QAChD,IAAI,IAAI,CAAChB,aAAa,IAAK,CAAA,CAACQ,QAAQ,CAAC6B,2BAA0B,GAAI;YACjE,IAAI;gBACF,MAAMC,QAAQrB,KAAKC,GAAG;gBACtB,MAAMqB,cAAoC;oBACxCd,UAAU;oBACVe,WAAW;oBACXJ,UAAUA;oBACVD;gBACF;gBACA,MAAMhB,MAAM,MAAMtD,MAChB,CAAC,EAAE,IAAI,CAACmC,aAAa,CAAC,mBAAmB,EAAEgC,IAAI,CAAC,EAChD;oBACET,QAAQ;oBACRlC,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACxC,uBAAuB,EAAEuF;wBAC1B,CAAC3F,kBAAkB,EAAEuE,CAAAA,wBAAAA,KAAMM,IAAI,CAAC,SAAQ;wBACxC,CAAChF,4BAA4B,EAAE2F,CAAAA,4BAAAA,SAAUX,IAAI,CAAC,SAAQ;oBACxD;oBACAE,MAAMe;gBACR;gBAGF,IAAIpB,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAI9B,OAAO,CAACuC,GAAG,CAAC,kBAAkB;oBACrDrF,mBAAmB0E,KAAKC,GAAG,KAAKW,SAASF;gBAC3C;gBAEA,IAAIR,IAAIO,MAAM,KAAK,KAAK;oBACtB,IAAI3E,OAAO;wBACTkB,QAAQC,GAAG,CACT,CAAC,yBAAyB,EAAE8D,IAAI,YAAY,EAC1Cf,KAAKC,GAAG,KAAKoB,MACd,EAAE,CAAC;oBAER;oBACA,OAAO;gBACT;gBAEA,IAAI,CAACnB,IAAIW,EAAE,EAAE;oBACX7D,QAAQwE,KAAK,CAAC,MAAMtB,IAAIuB,IAAI;oBAC5B,MAAM,IAAInC,MAAM,CAAC,4BAA4B,EAAEY,IAAIO,MAAM,CAAC,CAAC;gBAC7D;gBAEA,MAAMiB,SAAgC,MAAMxB,IAAIyB,IAAI;gBAEpD,IAAI,CAACD,UAAUA,OAAOvC,IAAI,KAAK,SAAS;oBACtCrD,SAASkB,QAAQC,GAAG,CAAC;wBAAEyE;oBAAO;oBAC9B,MAAM,IAAIpC,MAAM;gBAClB;gBAEA,oEAAoE;gBACpE,IAAIoC,OAAOvC,IAAI,KAAK,SAAS;oBAC3BuC,OAAO3B,IAAI,KAAK,EAAE;oBAClB,KAAK,MAAMlC,OAAOkC,QAAQ,EAAE,CAAE;wBAC5B,IAAI,CAAC2B,OAAO3B,IAAI,CAAC6B,QAAQ,CAAC/D,MAAM;4BAC9B6D,OAAO3B,IAAI,CAAC8B,IAAI,CAAChE;wBACnB;oBACF;gBACF;gBAEA,MAAMiE,aAAa5B,IAAI9B,OAAO,CAACuC,GAAG,CAACjF;gBACnC,MAAMqG,MAAM7B,IAAI9B,OAAO,CAACuC,GAAG,CAAC;gBAE5BpB,OAAO;oBACLL,OAAOwC;oBACP,qDAAqD;oBACrD,uCAAuC;oBACvCM,cACEF,eAAe,UACX9B,KAAKC,GAAG,KAAK7E,iBACb4E,KAAKC,GAAG,KAAKW,SAASmB,OAAO,KAAK,MAAM;gBAChD;gBAEA,IAAIjG,OAAO;oBACTkB,QAAQC,GAAG,CACT,CAAC,0BAA0B,EAAE8D,IAAI,YAAY,EAC3Cf,KAAKC,GAAG,KAAKoB,MACd,UAAU,EACTY,OAAOC,IAAI,CAACR,QAAQlE,MAAM,CAC3B,eAAe,EAAEsE,WAAW,OAAO,EAAE/B,wBAAAA,KAAMM,IAAI,CAC9C,KACA,WAAW,EAAEW,4BAAAA,SAAUX,IAAI,CAAC,KAAK,CAAC;gBAExC;gBAEA,IAAId,MAAM;oBACRhE,+BAAAA,YAAa4G,GAAG,CAACpB,KAAKxB;gBACxB;YACF,EAAE,OAAOxC,KAAK;gBACZ,sCAAsC;gBACtC,IAAIjB,OAAO;oBACTkB,QAAQwE,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAEzE;gBAClD;YACF;QACF;QAEA,OAAOwC,QAAQ;IACjB;IAEA,MAAa4C,IAAI,GAAGrC,IAAqC,EAAE;QACzD,MAAM,CAACiB,KAAKxB,MAAMvB,IAAI,GAAG8B;QAEzB,MAAMsC,WAAW7C,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,UAAUI,KAAKA,IAAI,GAAG8C;QACtD,MAAMC,gBAAgB/G,+BAAAA,YAAaoF,GAAG,CAACI;QACvC,MAAMwB,gBAAgBD,iCAAAA,cAAepD,KAAK;QAC1C,IACEqD,CAAAA,iCAAAA,cAAepD,IAAI,MAAK,WACxB8C,OAAOC,IAAI,CAACK,cAAchD,IAAI,EAAEiD,KAAK,CACnC,CAACC,QACCnE,KAAKc,SAAS,CACZ,AAACmD,cAAchD,IAAI,AAAoC,CAACkD,MAAM,MAEhEnE,KAAKc,SAAS,CAAC,AAACgD,QAA4C,CAACK,MAAM,IAEvE;YACA,IAAI3G,OAAO;gBACTkB,QAAQC,GAAG,CAAC,CAAC,uBAAuB,EAAE8D,IAAI,gBAAgB,CAAC;YAC7D;YACA;QACF;QAEA,MAAM,EAAE2B,UAAU,EAAExB,QAAQ,EAAEC,QAAQ,EAAEpB,IAAI,EAAE,GAAG/B;QACjD,IAAI,CAAC0E,YAAY;QAEjB,IAAI1C,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;YACA;QACF;QAEA1B,+BAAAA,YAAa4G,GAAG,CAACpB,KAAK;YACpB7B,OAAOK;YACPyC,cAAchC,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAAClB,aAAa,EAAE;YACtB,IAAI;gBACF,MAAMsC,QAAQrB,KAAKC,GAAG;gBACtB,IAAIV,SAAS,QAAQ,gBAAgBA,MAAM;oBACzC,IAAI,CAACnB,OAAO,CAACzC,wBAAwB,GAAG4D,KAAKoD,UAAU,CAACC,QAAQ;gBAClE;gBACA,IACE,CAAC,IAAI,CAACxE,OAAO,CAACzC,wBAAwB,IACtC4D,SAAS,QACT,UAAUA,MACV;oBACA,IAAI,CAACnB,OAAO,CAACvC,2BAA2B,GACtC0D,KAAKA,IAAI,CAACnB,OAAO,CAAC,gBAAgB;gBACtC;gBACA,MAAMoB,OAAOlB,KAAKc,SAAS,CAAC;oBAC1B,GAAGG,IAAI;oBACP,yCAAyC;oBACzC,sBAAsB;oBACtBQ,MAAMsC;gBACR;gBAEA,IAAIvG,OAAO;oBACTkB,QAAQC,GAAG,CAAC,aAAa8D;gBAC3B;gBACA,MAAMO,cAAoC;oBACxCd,UAAU;oBACVe,WAAW;oBACXJ;oBACAD;gBACF;gBACA,MAAMhB,MAAM,MAAMtD,MAChB,CAAC,EAAE,IAAI,CAACmC,aAAa,CAAC,mBAAmB,EAAEgC,IAAI,CAAC,EAChD;oBACET,QAAQ;oBACRlC,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACxC,uBAAuB,EAAEuF,YAAY;wBACtC,CAAC3F,kBAAkB,EAAEuE,CAAAA,wBAAAA,KAAMM,IAAI,CAAC,SAAQ;oBAC1C;oBACAb,MAAMA;oBACNe,MAAMe;gBACR;gBAGF,IAAIpB,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAI9B,OAAO,CAACuC,GAAG,CAAC,kBAAkB;oBACrDrF,mBAAmB0E,KAAKC,GAAG,KAAKW,SAASF;gBAC3C;gBAEA,IAAI,CAACR,IAAIW,EAAE,EAAE;oBACX/E,SAASkB,QAAQC,GAAG,CAAC,MAAMiD,IAAIuB,IAAI;oBACnC,MAAM,IAAInC,MAAM,CAAC,iBAAiB,EAAEY,IAAIO,MAAM,CAAC,CAAC;gBAClD;gBAEA,IAAI3E,OAAO;oBACTkB,QAAQC,GAAG,CACT,CAAC,oCAAoC,EAAE8D,IAAI,YAAY,EACrDf,KAAKC,GAAG,KAAKoB,MACd,UAAU,EAAE7B,KAAKhC,MAAM,CAAC,CAAC;gBAE9B;YACF,EAAE,OAAOT,KAAK;gBACZ,+BAA+B;gBAC/B,IAAIjB,OAAO;oBACTkB,QAAQwE,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAEzE;gBAChD;YACF;QACF;QACA;IACF;AACF"}