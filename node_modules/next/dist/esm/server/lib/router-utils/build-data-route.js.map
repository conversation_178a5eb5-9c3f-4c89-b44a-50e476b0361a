{"version": 3, "sources": ["../../../../src/server/lib/router-utils/build-data-route.ts"], "names": ["path", "normalizePagePath", "isDynamicRoute", "getNamedRouteRegex", "normalizeRouteRegex", "escapeStringRegexp", "buildDataRoute", "page", "buildId", "pagePath", "dataRoute", "posix", "join", "dataRouteRegex", "namedDataRouteRegex", "routeKeys", "routeRegex", "replace", "re", "source", "namedRegex", "RegExp"], "mappings": "AAAA,OAAOA,UAAU,sCAAqC;AACtD,SAASC,iBAAiB,QAAQ,oDAAmD;AACrF,SAASC,cAAc,QAAQ,8CAA6C;AAC5E,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,mBAAmB,QAAQ,kCAAiC;AACrE,SAASC,kBAAkB,QAAQ,oCAAmC;AAEtE,OAAO,SAASC,eAAeC,IAAY,EAAEC,OAAe;IAC1D,MAAMC,WAAWR,kBAAkBM;IACnC,MAAMG,YAAYV,KAAKW,KAAK,CAACC,IAAI,CAAC,eAAeJ,SAAS,CAAC,EAAEC,SAAS,KAAK,CAAC;IAE5E,IAAII;IACJ,IAAIC;IACJ,IAAIC;IAEJ,IAAIb,eAAeK,OAAO;QACxB,MAAMS,aAAab,mBACjBO,UAAUO,OAAO,CAAC,WAAW,KAC7B;QAGFJ,iBAAiBT,oBACfY,WAAWE,EAAE,CAACC,MAAM,CAACF,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC;QAE7DH,sBAAsBE,WAAWI,UAAU,CAAEH,OAAO,CAClD,kBACA,CAAC,QAAQ,CAAC;QAEZF,YAAYC,WAAWD,SAAS;IAClC,OAAO;QACLF,iBAAiBT,oBACf,IAAIiB,OACF,CAAC,CAAC,EAAErB,KAAKW,KAAK,CAACC,IAAI,CACjB,eACAP,mBAAmBG,UACnB,CAAC,EAAEC,SAAS,KAAK,CAAC,EAClB,CAAC,CAAC,EACJU,MAAM;IAEZ;IAEA,OAAO;QACLZ;QACAQ;QACAF;QACAC;IACF;AACF"}