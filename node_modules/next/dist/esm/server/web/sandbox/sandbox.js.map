{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "names": ["getModuleContext", "requestStore", "requestToBodyStream", "NEXT_RSC_UNION_QUERY", "ErrorSource", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "process", "env", "NODE_ENV", "getServerError", "require", "params", "then", "result", "waitUntil", "catch", "error", "getRuntimeContext", "runtime", "evaluateInContext", "moduleName", "name", "onWarning", "onError", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "run", "runWithTaggedErrors", "subreq", "request", "headers", "subrequests", "split", "MAX_RECURSION_DEPTH", "depth", "reduce", "acc", "curr", "Promise", "resolve", "response", "Response", "edgeFunction", "_ENTRIES", "default", "cloned", "includes", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "toString", "Headers", "key", "value", "Object", "entries", "set", "headerName", "Error", "finalize"], "mappings": "AAGA,SAASA,gBAAgB,EAAEC,YAAY,QAAQ,YAAW;AAC1D,SAASC,mBAAmB,QAAQ,qBAAoB;AACxD,SAASC,oBAAoB,QAAQ,gDAA+C;AAEpF,OAAO,MAAMC,cAAcC,OAAO,gBAAe;AAEjD,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAcD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;QAEV,OAAO,CAACC,SACNN,GAAGM,QACAC,IAAI,CAAC,CAACC;oBAEMA;uBAFM;oBACjB,GAAGA,MAAM;oBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;wBACnC,mGAAmG;wBACnG,MAAMP,eAAeO,OAAO;oBAC9B;gBACF;eACCD,KAAK,CAAC,CAACC;gBACN,+CAA+C;gBAC/C,MAAMP,eAAeO,OAAO;YAC9B;IACN;IAEA,OAAOX;AACT;AAEA,OAAO,eAAeY,kBAAkBN,MASvC;IACC,MAAM,EAAEO,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMtB,iBAAiB;QAC5DuB,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,SAASZ,OAAOY,OAAO,IAAK,CAAA,KAAO,CAAA;QACnCC,UAAUb,OAAOa,QAAQ,KAAK;QAC9BC,mBAAmBd,OAAOc,iBAAiB;QAC3CC,SAASf,OAAOe,OAAO;IACzB;IAEA,IAAIf,OAAOgB,gBAAgB,EAAE;QAC3BT,QAAQU,OAAO,CAACC,UAAU,CAACC,kBAAkB,GAAGnB,OAAOgB,gBAAgB;IACzE;IAEA,KAAK,MAAMI,aAAapB,OAAOqB,KAAK,CAAE;QACpCb,kBAAkBY;IACpB;IACA,OAAOb;AACT;AAEA,OAAO,MAAMe,MAAM7B,iBAAiB,eAAe8B,oBAAoBvB,MAAM;QA6BvEA;IA5BJ,MAAMO,UAAU,MAAMD,kBAAkBN;IACxC,MAAMwB,SAASxB,OAAOyB,OAAO,CAACC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC;IAChE,MAAMC,cAAc,OAAOH,WAAW,WAAWA,OAAOI,KAAK,CAAC,OAAO,EAAE;IAEvE,MAAMC,sBAAsB;IAC5B,MAAMC,QAAQH,YAAYI,MAAM,CAC9B,CAACC,KAAKC,OAAUA,SAASjC,OAAOU,IAAI,GAAGsB,MAAM,IAAIA,KACjD;IAGF,IAAIF,SAASD,qBAAqB;QAChC,OAAO;YACL1B,WAAW+B,QAAQC,OAAO;YAC1BC,UAAU,IAAI7B,QAAQU,OAAO,CAACoB,QAAQ,CAAC,MAAM;gBAC3CX,SAAS;oBACP,qBAAqB;gBACvB;YACF;QACF;IACF;IAEA,MAAMY,eAE4B,AAChC,CAAA,MAAM/B,QAAQU,OAAO,CAACsB,QAAQ,CAAC,CAAC,WAAW,EAAEvC,OAAOU,IAAI,CAAC,CAAC,CAAC,AAAD,EAC1D8B,OAAO;IAET,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACC,QAAQ,CAAC1C,OAAOyB,OAAO,CAACkB,MAAM,KAC1D3C,uBAAAA,OAAOyB,OAAO,CAACmB,IAAI,qBAAnB5C,qBAAqB6C,eAAe,KACpCC;IAEJ,MAAMC,cAAcxC,QAAQyC,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAIlD,OAAOyB,OAAO,CAAC0B,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAAChE;IAEhCW,OAAOyB,OAAO,CAAC0B,GAAG,GAAGF,YAAYK,QAAQ;IAEzC,MAAM5B,UAAU,IAAI6B;IACpB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAC3D,OAAOyB,OAAO,CAACC,OAAO,EAAG;QACjEA,QAAQkC,GAAG,CAACJ,KAAKC,CAAAA,yBAAAA,MAAOH,QAAQ,OAAM;IACxC;IAEA,IAAI;QACF,IAAIpD,SAAuC4C;QAC3C,MAAM3D,aAAamC,GAAG,CAAC;YAAEI;QAAQ,GAAG;YAClCxB,SAAS,MAAMoC,aAAa;gBAC1Bb,SAAS;oBACP,GAAGzB,OAAOyB,OAAO;oBACjBmB,MACEH,UAAUrD,oBAAoBmB,QAAQU,OAAO,EAAE8B,aAAaN;gBAChE;YACF;YACA,KAAK,MAAMoB,cAAcrE,kBAAmB;gBAC1CU,OAAOkC,QAAQ,CAACV,OAAO,CAAC2B,MAAM,CAACQ;YACjC;QACF;QACA,IAAI,CAAC3D,QAAQ,MAAM,IAAI4D,MAAM;QAC7B,OAAO5D;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAOyB,OAAO,CAACmB,IAAI,qBAAnB5C,sBAAqB+D,QAAQ;IACrC;AACF,GAAE"}