{"version": 3, "sources": ["../../../../../src/server/web/spec-extension/adapters/next-request.ts"], "names": ["getRequestMeta", "fromNodeOutgoingHttpHeaders", "NextRequest", "ResponseAbortedName", "ResponseAborted", "Error", "name", "createAbortController", "response", "controller", "AbortController", "once", "writableFinished", "abort", "signalFromNodeResponse", "errored", "destroyed", "AbortSignal", "signal", "NextRequestAdapter", "fromBaseNextRequest", "request", "fromWebNextRequest", "fromNodeNextRequest", "body", "method", "url", "startsWith", "URL", "base", "headers", "duplex", "aborted"], "mappings": "AAKA,SAASA,cAAc,QAAQ,wBAAuB;AACtD,SAASC,2BAA2B,QAAQ,cAAa;AACzD,SAASC,WAAW,QAAQ,aAAY;AAExC,OAAO,MAAMC,sBAAsB,kBAAiB;AACpD,OAAO,MAAMC,wBAAwBC;;;aACnBC,OAAOH;;AACzB;AAEA;;;;;;CAMC,GACD,OAAO,SAASI,sBAAsBC,QAAkB;IACtD,MAAMC,aAAa,IAAIC;IAEvB,6EAA6E;IAC7E,4EAA4E;IAC5E,mDAAmD;IACnDF,SAASG,IAAI,CAAC,SAAS;QACrB,IAAIH,SAASI,gBAAgB,EAAE;QAE/BH,WAAWI,KAAK,CAAC,IAAIT;IACvB;IAEA,OAAOK;AACT;AAEA;;;;;;;CAOC,GACD,OAAO,SAASK,uBAAuBN,QAAkB;IACvD,MAAM,EAAEO,OAAO,EAAEC,SAAS,EAAE,GAAGR;IAC/B,IAAIO,WAAWC,WAAW;QACxB,OAAOC,YAAYJ,KAAK,CAACE,WAAW,IAAIX;IAC1C;IAEA,MAAM,EAAEc,MAAM,EAAE,GAAGX,sBAAsBC;IACzC,OAAOU;AACT;AAEA,OAAO,MAAMC;IACX,OAAcC,oBACZC,OAAwB,EACxBH,MAAmB,EACN;QACb,oCAAoC;QACpC,IAAI,aAAaG,WAAW,AAACA,QAA2BA,OAAO,EAAE;YAC/D,OAAOF,mBAAmBG,kBAAkB,CAACD;QAC/C;QAEA,OAAOF,mBAAmBI,mBAAmB,CAC3CF,SACAH;IAEJ;IAEA,OAAcK,oBACZF,OAAwB,EACxBH,MAAmB,EACN;QACb,6CAA6C;QAC7C,IAAIM,OAAwB;QAC5B,IAAIH,QAAQI,MAAM,KAAK,SAASJ,QAAQI,MAAM,KAAK,UAAUJ,QAAQG,IAAI,EAAE;YACzE,qFAAqF;YACrFA,OAAOH,QAAQG,IAAI;QACrB;QAEA,IAAIE;QACJ,IAAIL,QAAQK,GAAG,CAACC,UAAU,CAAC,SAAS;YAClCD,MAAM,IAAIE,IAAIP,QAAQK,GAAG;QAC3B,OAAO;YACL,+CAA+C;YAC/C,MAAMG,OAAO7B,eAAeqB,SAAS;YACrC,IAAI,CAACQ,QAAQ,CAACA,KAAKF,UAAU,CAAC,SAAS;gBACrC,wEAAwE;gBACxE,uEAAuE;gBACvE,4DAA4D;gBAC5DD,MAAM,IAAIE,IAAIP,QAAQK,GAAG,EAAE;YAC7B,OAAO;gBACLA,MAAM,IAAIE,IAAIP,QAAQK,GAAG,EAAEG;YAC7B;QACF;QAEA,OAAO,IAAI3B,YAAYwB,KAAK;YAC1BD,QAAQJ,QAAQI,MAAM;YACtBK,SAAS7B,4BAA4BoB,QAAQS,OAAO;YACpD,mEAAmE;YACnEC,QAAQ;YACRb;YACA,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIA,OAAOc,OAAO,GACd,CAAC,IACD;gBACER;YACF,CAAC;QACP;IACF;IAEA,OAAcF,mBAAmBD,OAAuB,EAAe;QACrE,6CAA6C;QAC7C,IAAIG,OAA8B;QAClC,IAAIH,QAAQI,MAAM,KAAK,SAASJ,QAAQI,MAAM,KAAK,QAAQ;YACzDD,OAAOH,QAAQG,IAAI;QACrB;QAEA,OAAO,IAAItB,YAAYmB,QAAQK,GAAG,EAAE;YAClCD,QAAQJ,QAAQI,MAAM;YACtBK,SAAS7B,4BAA4BoB,QAAQS,OAAO;YACpD,mEAAmE;YACnEC,QAAQ;YACRb,QAAQG,QAAQA,OAAO,CAACH,MAAM;YAC9B,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIG,QAAQA,OAAO,CAACH,MAAM,CAACc,OAAO,GAC9B,CAAC,IACD;gBACER;YACF,CAAC;QACP;IACF;AACF"}