{"version": 3, "sources": ["../../../src/server/web/next-url.ts"], "names": ["detectDomainLocale", "formatNextPathnameInfo", "getHostname", "getNextPathnameInfo", "REGEX_LOCALHOST_HOSTNAME", "parseURL", "url", "base", "URL", "String", "replace", "Internal", "Symbol", "NextURL", "constructor", "input", "baseOrOpts", "opts", "options", "basePath", "analyze", "info", "pathname", "nextConfig", "parseData", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "i18nProvider", "hostname", "headers", "domainLocale", "i18n", "domains", "defaultLocale", "buildId", "locale", "trailingSlash", "formatPathname", "forceLocale", "undefined", "formatSearch", "search", "locales", "includes", "TypeError", "searchParams", "host", "value", "port", "protocol", "href", "hash", "origin", "password", "username", "startsWith", "toString", "toJSON", "for", "clone"], "mappings": "AAIA,SAASA,kBAAkB,QAAQ,6CAA4C;AAC/E,SAASC,sBAAsB,QAAQ,0DAAyD;AAChG,SAASC,WAAW,QAAQ,gCAA+B;AAC3D,SAASC,mBAAmB,QAAQ,uDAAsD;AAc1F,MAAMC,2BACJ;AAEF,SAASC,SAASC,GAAiB,EAAEC,IAAmB;IACtD,OAAO,IAAIC,IACTC,OAAOH,KAAKI,OAAO,CAACN,0BAA0B,cAC9CG,QAAQE,OAAOF,MAAMG,OAAO,CAACN,0BAA0B;AAE3D;AAEA,MAAMO,WAAWC,OAAO;AAExB,OAAO,MAAMC;IAeXC,YACEC,KAAmB,EACnBC,UAAmC,EACnCC,IAAc,CACd;QACA,IAAIV;QACJ,IAAIW;QAEJ,IACE,AAAC,OAAOF,eAAe,YAAY,cAAcA,cACjD,OAAOA,eAAe,UACtB;YACAT,OAAOS;YACPE,UAAUD,QAAQ,CAAC;QACrB,OAAO;YACLC,UAAUD,QAAQD,cAAc,CAAC;QACnC;QAEA,IAAI,CAACL,SAAS,GAAG;YACfL,KAAKD,SAASU,OAAOR,QAAQW,QAAQX,IAAI;YACzCW,SAASA;YACTC,UAAU;QACZ;QAEA,IAAI,CAACC,OAAO;IACd;IAEQA,UAAU;YAcV,wCAAA,mCAKJ,6BACA,yCAAA;QAnBF,MAAMC,OAAOlB,oBAAoB,IAAI,CAACQ,SAAS,CAACL,GAAG,CAACgB,QAAQ,EAAE;YAC5DC,YAAY,IAAI,CAACZ,SAAS,CAACO,OAAO,CAACK,UAAU;YAC7CC,WAAW,CAACC,QAAQC,GAAG,CAACC,kCAAkC;YAC1DC,cAAc,IAAI,CAACjB,SAAS,CAACO,OAAO,CAACU,YAAY;QACnD;QAEA,MAAMC,WAAW3B,YACf,IAAI,CAACS,SAAS,CAACL,GAAG,EAClB,IAAI,CAACK,SAAS,CAACO,OAAO,CAACY,OAAO;QAEhC,IAAI,CAACnB,SAAS,CAACoB,YAAY,GAAG,IAAI,CAACpB,SAAS,CAACO,OAAO,CAACU,YAAY,GAC7D,IAAI,CAACjB,SAAS,CAACO,OAAO,CAACU,YAAY,CAAC5B,kBAAkB,CAAC6B,YACvD7B,oBACE,oCAAA,IAAI,CAACW,SAAS,CAACO,OAAO,CAACK,UAAU,sBAAjC,yCAAA,kCAAmCS,IAAI,qBAAvC,uCAAyCC,OAAO,EAChDJ;QAGN,MAAMK,gBACJ,EAAA,8BAAA,IAAI,CAACvB,SAAS,CAACoB,YAAY,qBAA3B,4BAA6BG,aAAa,OAC1C,qCAAA,IAAI,CAACvB,SAAS,CAACO,OAAO,CAACK,UAAU,sBAAjC,0CAAA,mCAAmCS,IAAI,qBAAvC,wCAAyCE,aAAa;QAExD,IAAI,CAACvB,SAAS,CAACL,GAAG,CAACgB,QAAQ,GAAGD,KAAKC,QAAQ;QAC3C,IAAI,CAACX,SAAS,CAACuB,aAAa,GAAGA;QAC/B,IAAI,CAACvB,SAAS,CAACQ,QAAQ,GAAGE,KAAKF,QAAQ,IAAI;QAC3C,IAAI,CAACR,SAAS,CAACwB,OAAO,GAAGd,KAAKc,OAAO;QACrC,IAAI,CAACxB,SAAS,CAACyB,MAAM,GAAGf,KAAKe,MAAM,IAAIF;QACvC,IAAI,CAACvB,SAAS,CAAC0B,aAAa,GAAGhB,KAAKgB,aAAa;IACnD;IAEQC,iBAAiB;QACvB,OAAOrC,uBAAuB;YAC5BkB,UAAU,IAAI,CAACR,SAAS,CAACQ,QAAQ;YACjCgB,SAAS,IAAI,CAACxB,SAAS,CAACwB,OAAO;YAC/BD,eAAe,CAAC,IAAI,CAACvB,SAAS,CAACO,OAAO,CAACqB,WAAW,GAC9C,IAAI,CAAC5B,SAAS,CAACuB,aAAa,GAC5BM;YACJJ,QAAQ,IAAI,CAACzB,SAAS,CAACyB,MAAM;YAC7Bd,UAAU,IAAI,CAACX,SAAS,CAACL,GAAG,CAACgB,QAAQ;YACrCe,eAAe,IAAI,CAAC1B,SAAS,CAAC0B,aAAa;QAC7C;IACF;IAEQI,eAAe;QACrB,OAAO,IAAI,CAAC9B,SAAS,CAACL,GAAG,CAACoC,MAAM;IAClC;IAEA,IAAWP,UAAU;QACnB,OAAO,IAAI,CAACxB,SAAS,CAACwB,OAAO;IAC/B;IAEA,IAAWA,QAAQA,OAA2B,EAAE;QAC9C,IAAI,CAACxB,SAAS,CAACwB,OAAO,GAAGA;IAC3B;IAEA,IAAWC,SAAS;QAClB,OAAO,IAAI,CAACzB,SAAS,CAACyB,MAAM,IAAI;IAClC;IAEA,IAAWA,OAAOA,MAAc,EAAE;YAG7B,wCAAA;QAFH,IACE,CAAC,IAAI,CAACzB,SAAS,CAACyB,MAAM,IACtB,GAAC,oCAAA,IAAI,CAACzB,SAAS,CAACO,OAAO,CAACK,UAAU,sBAAjC,yCAAA,kCAAmCS,IAAI,qBAAvC,uCAAyCW,OAAO,CAACC,QAAQ,CAACR,UAC3D;YACA,MAAM,IAAIS,UACR,CAAC,8CAA8C,EAAET,OAAO,CAAC,CAAC;QAE9D;QAEA,IAAI,CAACzB,SAAS,CAACyB,MAAM,GAAGA;IAC1B;IAEA,IAAIF,gBAAgB;QAClB,OAAO,IAAI,CAACvB,SAAS,CAACuB,aAAa;IACrC;IAEA,IAAIH,eAAe;QACjB,OAAO,IAAI,CAACpB,SAAS,CAACoB,YAAY;IACpC;IAEA,IAAIe,eAAe;QACjB,OAAO,IAAI,CAACnC,SAAS,CAACL,GAAG,CAACwC,YAAY;IACxC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACpC,SAAS,CAACL,GAAG,CAACyC,IAAI;IAChC;IAEA,IAAIA,KAAKC,KAAa,EAAE;QACtB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACyC,IAAI,GAAGC;IAC5B;IAEA,IAAInB,WAAW;QACb,OAAO,IAAI,CAAClB,SAAS,CAACL,GAAG,CAACuB,QAAQ;IACpC;IAEA,IAAIA,SAASmB,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACuB,QAAQ,GAAGmB;IAChC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACtC,SAAS,CAACL,GAAG,CAAC2C,IAAI;IAChC;IAEA,IAAIA,KAAKD,KAAa,EAAE;QACtB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAAC2C,IAAI,GAAGD;IAC5B;IAEA,IAAIE,WAAW;QACb,OAAO,IAAI,CAACvC,SAAS,CAACL,GAAG,CAAC4C,QAAQ;IACpC;IAEA,IAAIA,SAASF,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAAC4C,QAAQ,GAAGF;IAChC;IAEA,IAAIG,OAAO;QACT,MAAM7B,WAAW,IAAI,CAACgB,cAAc;QACpC,MAAMI,SAAS,IAAI,CAACD,YAAY;QAChC,OAAO,CAAC,EAAE,IAAI,CAACS,QAAQ,CAAC,EAAE,EAAE,IAAI,CAACH,IAAI,CAAC,EAAEzB,SAAS,EAAEoB,OAAO,EAAE,IAAI,CAACU,IAAI,CAAC,CAAC;IACzE;IAEA,IAAID,KAAK7C,GAAW,EAAE;QACpB,IAAI,CAACK,SAAS,CAACL,GAAG,GAAGD,SAASC;QAC9B,IAAI,CAACc,OAAO;IACd;IAEA,IAAIiC,SAAS;QACX,OAAO,IAAI,CAAC1C,SAAS,CAACL,GAAG,CAAC+C,MAAM;IAClC;IAEA,IAAI/B,WAAW;QACb,OAAO,IAAI,CAACX,SAAS,CAACL,GAAG,CAACgB,QAAQ;IACpC;IAEA,IAAIA,SAAS0B,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACgB,QAAQ,GAAG0B;IAChC;IAEA,IAAII,OAAO;QACT,OAAO,IAAI,CAACzC,SAAS,CAACL,GAAG,CAAC8C,IAAI;IAChC;IAEA,IAAIA,KAAKJ,KAAa,EAAE;QACtB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAAC8C,IAAI,GAAGJ;IAC5B;IAEA,IAAIN,SAAS;QACX,OAAO,IAAI,CAAC/B,SAAS,CAACL,GAAG,CAACoC,MAAM;IAClC;IAEA,IAAIA,OAAOM,KAAa,EAAE;QACxB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACoC,MAAM,GAAGM;IAC9B;IAEA,IAAIM,WAAW;QACb,OAAO,IAAI,CAAC3C,SAAS,CAACL,GAAG,CAACgD,QAAQ;IACpC;IAEA,IAAIA,SAASN,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACgD,QAAQ,GAAGN;IAChC;IAEA,IAAIO,WAAW;QACb,OAAO,IAAI,CAAC5C,SAAS,CAACL,GAAG,CAACiD,QAAQ;IACpC;IAEA,IAAIA,SAASP,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACiD,QAAQ,GAAGP;IAChC;IAEA,IAAI7B,WAAW;QACb,OAAO,IAAI,CAACR,SAAS,CAACQ,QAAQ;IAChC;IAEA,IAAIA,SAAS6B,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACQ,QAAQ,GAAG6B,MAAMQ,UAAU,CAAC,OAAOR,QAAQ,CAAC,CAAC,EAAEA,MAAM,CAAC;IACvE;IAEAS,WAAW;QACT,OAAO,IAAI,CAACN,IAAI;IAClB;IAEAO,SAAS;QACP,OAAO,IAAI,CAACP,IAAI;IAClB;IAEA,CAACvC,OAAO+C,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLR,MAAM,IAAI,CAACA,IAAI;YACfE,QAAQ,IAAI,CAACA,MAAM;YACnBH,UAAU,IAAI,CAACA,QAAQ;YACvBK,UAAU,IAAI,CAACA,QAAQ;YACvBD,UAAU,IAAI,CAACA,QAAQ;YACvBP,MAAM,IAAI,CAACA,IAAI;YACflB,UAAU,IAAI,CAACA,QAAQ;YACvBoB,MAAM,IAAI,CAACA,IAAI;YACf3B,UAAU,IAAI,CAACA,QAAQ;YACvBoB,QAAQ,IAAI,CAACA,MAAM;YACnBI,cAAc,IAAI,CAACA,YAAY;YAC/BM,MAAM,IAAI,CAACA,IAAI;QACjB;IACF;IAEAQ,QAAQ;QACN,OAAO,IAAI/C,QAAQJ,OAAO,IAAI,GAAG,IAAI,CAACE,SAAS,CAACO,OAAO;IACzD;AACF"}