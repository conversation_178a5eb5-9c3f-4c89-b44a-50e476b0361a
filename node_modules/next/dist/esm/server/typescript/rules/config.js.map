{"version": 3, "sources": ["../../../../src/server/typescript/rules/config.ts"], "names": ["getSource", "isPositionInsideNode", "getTs", "removeStringQuotes", "NEXT_TS_ERRORS", "ALLOWED_EXPORTS", "LEGACY_CONFIG_EXPORT", "API_DOCS", "dynamic", "description", "options", "link", "fetchCache", "preferredRegion", "<PERSON><PERSON><PERSON><PERSON>", "value", "parsed", "JSON", "parse", "Array", "isArray", "some", "v", "err", "getHint", "join", "revalidate", "type", "false", "Number", "replace", "dynamicParams", "true", "runtime", "metadata", "maxDuration", "visitEntryConfig", "fileName", "position", "callback", "source", "ts", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "node", "isVariableStatement", "modifiers", "m", "kind", "SyntaxKind", "ExportKeyword", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "text", "name", "getText", "createAutoCompletionOptionName", "sort", "sortText", "ScriptElementKind", "constElement", "kindModifiers", "ScriptElementKindModifier", "exportedModifier", "labelDetails", "data", "exportName", "moduleSpecifier", "createAutoCompletionOptionValue", "apiName", "isString", "startsWith", "insertText", "string", "unknown", "none", "getAPIDescription", "api", "Object", "entries", "map", "key", "config", "addCompletionsAtPosition", "prior", "entryConfig", "push", "keys", "index", "getQuickInfoAtPosition", "overridden", "initializer", "docsLink", "isStringLiteral", "enumElement", "textSpan", "start", "getStart", "length", "getWidth", "displayParts", "documentation", "getCompletionEntryDetails", "entryName", "content", "getSemanticDiagnosticsForExportVariableStatement", "diagnostics", "isIdentifier", "includes", "file", "category", "DiagnosticCategory", "Error", "code", "INVALID_ENTRY_EXPORT", "messageText", "displayedValue", "errorMessage", "isInvalid", "isNoSubstitutionTemplateLiteral", "val", "<PERSON><PERSON><PERSON><PERSON>", "filter", "test", "isNumericLiteral", "isPrefixUnaryExpression", "isMinusToken", "operator", "operand", "TrueKeyword", "FalseKeyword", "isArrayLiteralExpression", "stringify", "elements", "e", "isBigIntLiteral", "isObjectLiteralExpression", "isRegularExpressionLiteral", "INVALID_OPTION_VALUE", "prop", "properties", "isPropertyAssignment", "INVALID_CONFIG_OPTION"], "mappings": "AAAA,4EAA4E;AAE5E,SACEA,SAAS,EACTC,oBAAoB,EACpBC,KAAK,EACLC,kBAAkB,QACb,WAAU;AACjB,SACEC,cAAc,EACdC,eAAe,EACfC,oBAAoB,QACf,cAAa;AAGpB,MAAMC,WAUF;IACFC,SAAS;QACPC,aACE;QACFC,SAAS;YACP,UACE;YACF,mBACE;YACF,WACE;YACF,kBACE;QACJ;QACAC,MAAM;IACR;IACAC,YAAY;QACVH,aACE;QACFC,SAAS;YACP,oBACE;YACF,mBACE;YACF,sBACE;YACF,UACE;YACF,mBACE;YACF,gBACE;YACF,iBACE;QACJ;QACAC,MAAM;IACR;IACAE,iBAAiB;QACfJ,aACE;QACFC,SAAS;YACP,UACE;YACF,YAAY;YACZ,UAAU;QACZ;QACAC,MAAM;QACNG,SAAS,CAACC;YACR,IAAI;gBACF,MAAMC,SAASC,KAAKC,KAAK,CAACH;gBAC1B,OACE,OAAOC,WAAW,YACjBG,MAAMC,OAAO,CAACJ,WAAW,CAACA,OAAOK,IAAI,CAAC,CAACC,IAAM,OAAOA,MAAM;YAE/D,EAAE,OAAOC,KAAK;gBACZ,OAAO;YACT;QACF;QACAC,SAAS,CAACT;YACR,IAAIA,UAAU,QAAQ,OAAO,CAAC,gCAAgC,CAAC;YAC/D,IAAIA,UAAU,UAAU,OAAO,CAAC,0BAA0B,CAAC;YAC3D,IAAIA,UAAU,QAAQ,OAAO,CAAC,oCAAoC,CAAC;YACnE,IAAII,MAAMC,OAAO,CAACL,QAAQ,OAAO,CAAC,mBAAmB,EAAEA,MAAMU,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,OAAOV,UAAU,UAAU,OAAO,CAAC,kBAAkB,EAAEA,MAAM,CAAC,CAAC;QACrE;IACF;IACAW,YAAY;QACVjB,aACE;QACFkB,MAAM;QACNjB,SAAS;YACPkB,OACE;YACF,GAAG;YACH,IAAI;QACN;QACAjB,MAAM;QACNG,SAAS,CAACC;YACR,OAAOA,UAAU,WAAWc,OAAOd,MAAMe,OAAO,CAAC,MAAM,QAAQ;QACjE;QACAN,SAAS,CAACT;YACR,OAAO,CAAC,uCAAuC,EAAEA,MAAM,WAAW,CAAC;QACrE;IACF;IACAgB,eAAe;QACbtB,aACE;QACFC,SAAS;YACPsB,MAAM;YACNJ,OACE;QACJ;QACAjB,MAAM;QACNG,SAAS,CAACC;YACR,OAAOA,UAAU,UAAUA,UAAU;QACvC;IACF;IACAkB,SAAS;QACPxB,aACE;QACFC,SAAS;YACP,YAAY;YACZ,UAAU;YACV,uBAAuB,CAAC,2EAA2E,CAAC;QACtG;QACAC,MAAM;IACR;IACAuB,UAAU;QACRzB,aAAa;QACbE,MAAM;IACR;IACAwB,aAAa;QACX1B,aACE;QACFE,MAAM;IACR;AACF;AAEA,SAASyB,iBACPC,QAAgB,EAChBC,QAAgB,EAChBC,QAA4E;IAE5E,MAAMC,SAASxC,UAAUqC;IACzB,IAAIG,QAAQ;QACV,MAAMC,KAAKvC;QACXuC,GAAGC,YAAY,CAACF,QAAQ,SAASG,MAAMC,IAAI;YACzC,uBAAuB;YACvB,IAAI3C,qBAAqBqC,UAAUM,OAAO;oBAItCA;gBAHF,kBAAkB;gBAClB,IACEH,GAAGI,mBAAmB,CAACD,WACvBA,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgBvB,IAAI,CAAC,CAAC0B,IAAMA,EAAEC,IAAI,KAAKP,GAAGQ,UAAU,CAACC,aAAa,IAClE;oBACA,IAAIT,GAAGU,yBAAyB,CAACP,KAAKQ,eAAe,GAAG;wBACtD,KAAK,MAAMC,eAAeT,KAAKQ,eAAe,CAACE,YAAY,CAAE;4BAC3D,IAAIrD,qBAAqBqC,UAAUe,cAAc;gCAC/C,2BAA2B;gCAC3B,MAAME,OAAOF,YAAYG,IAAI,CAACC,OAAO;gCACrClB,SAASgB,MAAMF;4BACjB;wBACF;oBACF;gBACF;YACF;QACF;IACF;AACF;AAEA,SAASK,+BAA+BC,IAAY,EAAEH,IAAY;IAChE,MAAMf,KAAKvC;IACX,OAAO;QACLsD;QACAI,UAAU,MAAMD;QAChBX,MAAMP,GAAGoB,iBAAiB,CAACC,YAAY;QACvCC,eAAetB,GAAGuB,yBAAyB,CAACC,gBAAgB;QAC5DC,cAAc;YACZzD,aAAa,CAAC,QAAQ,EAAE+C,KAAK,OAAO,CAAC;QACvC;QACAW,MAAM;YACJC,YAAYZ;YACZa,iBAAiB;QACnB;IACF;AACF;AAEA,SAASC,gCACPX,IAAY,EACZH,IAAY,EACZe,OAAe;IAEf,MAAM9B,KAAKvC;IACX,MAAMsE,WAAWhB,KAAKiB,UAAU,CAAC;IACjC,OAAO;QACLjB;QACAkB,YAAYvE,mBAAmBqD;QAC/BI,UAAU,KAAKD;QACfX,MAAMwB,WAAW/B,GAAGoB,iBAAiB,CAACc,MAAM,GAAGlC,GAAGoB,iBAAiB,CAACe,OAAO;QAC3Eb,eAAetB,GAAGuB,yBAAyB,CAACa,IAAI;QAChDX,cAAc;YACZzD,aAAa,CAAC,QAAQ,EAAE8D,QAAQ,OAAO,CAAC;QAC1C;QACAJ,MAAM;YACJC,YAAYG;YACZF,iBAAiB;QACnB;IACF;AACF;AAEA,SAASS,kBAAkBC,GAAW;IACpC,OACExE,QAAQ,CAACwE,IAAI,CAACtE,WAAW,GACzB,SACAuE,OAAOC,OAAO,CAAC1E,QAAQ,CAACwE,IAAI,CAACrE,OAAO,IAAI,CAAC,GACtCwE,GAAG,CAAC,CAAC,CAACC,KAAKpE,MAAM,GAAK,CAAC,IAAI,EAAEoE,IAAI,IAAI,EAAEpE,MAAM,CAAC,EAC9CU,IAAI,CAAC;AAEZ;AACA,MAAM2D,SAAS;IACb,8CAA8C;IAC9CC,0BACEhD,QAAgB,EAChBC,QAAgB,EAChBgD,KAAqD;QAErDlD,iBAAiBC,UAAUC,UAAU,CAACiD,aAAalC;YACjD,IAAI,CAAC9C,QAAQ,CAACgF,YAAY,EAAE;gBAC1B,IAAItF,qBAAqBqC,UAAUe,YAAYG,IAAI,GAAG;oBACpD8B,MAAML,OAAO,CAACO,IAAI,IACbR,OAAOS,IAAI,CAAClF,UAAU2E,GAAG,CAAC,CAAC1B,MAAMkC;wBAClC,OAAOhC,+BAA+BgC,OAAOlC;oBAC/C;gBAEJ;gBACA;YACF;YAEA8B,MAAML,OAAO,CAACO,IAAI,IACbR,OAAOS,IAAI,CAAClF,QAAQ,CAACgF,YAAY,CAAC7E,OAAO,IAAI,CAAC,GAAGwE,GAAG,CACrD,CAAC1B,MAAMkC;gBACL,OAAOpB,gCAAgCoB,OAAOlC,MAAM+B;YACtD;QAGN;IACF;IAEA,mDAAmD;IACnDI,wBAAuBtD,QAAgB,EAAEC,QAAgB;QACvD,MAAMG,KAAKvC;QAEX,IAAI0F;QACJxD,iBAAiBC,UAAUC,UAAU,CAACiD,aAAalC;YACjD,IAAI,CAAC9C,QAAQ,CAACgF,YAAY,EAAE;YAE5B,MAAM/B,OAAOH,YAAYG,IAAI;YAC7B,MAAMzC,QAAQsC,YAAYwC,WAAW;YAErC,MAAMC,WAAW;gBACf9C,MAAM;gBACNO,MACE,CAAC,yBAAyB,EAAEgC,YAAY,UAAU,CAAC,GACnDhF,QAAQ,CAACgF,YAAY,CAAC5E,IAAI;YAC9B;YAEA,IAAII,SAASd,qBAAqBqC,UAAUvB,QAAQ;oBAO9CR,+BAAAA,uBACEA;gBAPN,iCAAiC;gBACjC,MAAMiE,WAAW/B,GAAGsD,eAAe,CAAChF;gBACpC,MAAMwC,OAAOpD,mBAAmBY,MAAM0C,OAAO;gBAC7C,MAAM0B,MAAMX,WAAW,CAAC,CAAC,EAAEjB,KAAK,CAAC,CAAC,GAAGA;gBAErC,MAAMzC,UAAUP,QAAQ,CAACgF,YAAY,CAACzE,OAAO,IACzCP,gCAAAA,CAAAA,wBAAAA,QAAQ,CAACgF,YAAY,EAACzE,OAAO,qBAA7BP,mCAAAA,uBAAgC4E,OAChC,CAAC,GAAC5E,gCAAAA,QAAQ,CAACgF,YAAY,CAAC7E,OAAO,qBAA7BH,6BAA+B,CAAC4E,IAAI;gBAE1C,IAAIrE,SAAS;wBAaHP,gCACAA,+BAAAA;oBAbRqF,aAAa;wBACX5C,MAAMP,GAAGoB,iBAAiB,CAACmC,WAAW;wBACtCjC,eAAetB,GAAGuB,yBAAyB,CAACa,IAAI;wBAChDoB,UAAU;4BACRC,OAAOnF,MAAMoF,QAAQ;4BACrBC,QAAQrF,MAAMsF,QAAQ;wBACxB;wBACAC,cAAc,EAAE;wBAChBC,eAAe;4BACb;gCACEvD,MAAM;gCACNO,MACEhD,EAAAA,iCAAAA,QAAQ,CAACgF,YAAY,CAAC7E,OAAO,qBAA7BH,8BAA+B,CAAC4E,IAAI,OACpC5E,gCAAAA,CAAAA,yBAAAA,QAAQ,CAACgF,YAAY,EAAC/D,OAAO,qBAA7BjB,mCAAAA,wBAAgC4E,SAChC;4BACJ;4BACAW;yBACD;oBACH;gBACF,OAAO;oBACL,qCAAqC;oBACrCF,aAAa;wBACX5C,MAAMP,GAAGoB,iBAAiB,CAACmC,WAAW;wBACtCjC,eAAetB,GAAGuB,yBAAyB,CAACa,IAAI;wBAChDoB,UAAU;4BACRC,OAAOnF,MAAMoF,QAAQ;4BACrBC,QAAQrF,MAAMsF,QAAQ;wBACxB;wBACAC,cAAc,EAAE;wBAChBC,eAAe;4BAACT;yBAAS;oBAC3B;gBACF;YACF,OAAO;gBACL,gCAAgC;gBAChCF,aAAa;oBACX5C,MAAMP,GAAGoB,iBAAiB,CAACmC,WAAW;oBACtCjC,eAAetB,GAAGuB,yBAAyB,CAACa,IAAI;oBAChDoB,UAAU;wBACRC,OAAO1C,KAAK2C,QAAQ;wBACpBC,QAAQ5C,KAAK6C,QAAQ;oBACvB;oBACAC,cAAc,EAAE;oBAChBC,eAAe;wBACb;4BACEvD,MAAM;4BACNO,MAAMuB,kBAAkBS;wBAC1B;wBACAO;qBACD;gBACH;YACF;QACF;QACA,OAAOF;IACT;IAEA,iDAAiD;IACjDY,2BACEC,SAAiB,EACjBtC,IAAkC;QAElC,MAAM1B,KAAKvC;QACX,IACEiE,QACAA,KAAKE,eAAe,IACpBF,KAAKE,eAAe,CAACI,UAAU,CAAC,oBAChC;YACA,IAAIiC,UAAU;YACd,IAAIvC,KAAKE,eAAe,KAAK,qCAAqC;gBAChEqC,UAAU5B,kBAAkB2B;YAC9B,OAAO;gBACL,MAAM/F,UAAUH,QAAQ,CAAC4D,KAAKC,UAAU,CAAC,CAAC1D,OAAO;gBACjD,IAAI,CAACA,SAAS;gBACdgG,UAAUhG,OAAO,CAAC+F,UAAU;YAC9B;YACA,OAAO;gBACLjD,MAAMiD;gBACNzD,MAAMP,GAAGoB,iBAAiB,CAACmC,WAAW;gBACtCjC,eAAetB,GAAGuB,yBAAyB,CAACa,IAAI;gBAChDyB,cAAc,EAAE;gBAChBC,eAAe;oBACb;wBACEvD,MAAM;wBACNO,MAAMmD;oBACR;iBACD;YACH;QACF;IACF;IAEA,yCAAyC;IACzCC,kDACEnE,MAA2B,EAC3BI,IAAgC;QAEhC,MAAMH,KAAKvC;QAEX,MAAM0G,cAAqC,EAAE;QAE7C,yCAAyC;QACzC,IAAInE,GAAGU,yBAAyB,CAACP,KAAKQ,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAeT,KAAKQ,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAME,OAAOH,YAAYG,IAAI;gBAC7B,IAAIf,GAAGoE,YAAY,CAACrD,OAAO;oBACzB,IAAI,CAACnD,gBAAgByG,QAAQ,CAACtD,KAAKD,IAAI,KAAK,CAAChD,QAAQ,CAACiD,KAAKD,IAAI,CAAC,EAAE;wBAChEqD,YAAYpB,IAAI,CAAC;4BACfuB,MAAMvE;4BACNwE,UAAUvE,GAAGwE,kBAAkB,CAACC,KAAK;4BACrCC,MAAM/G,eAAegH,oBAAoB;4BACzCC,aAAa,CAAC,CAAC,EAAE7D,KAAKD,IAAI,CAAC,4CAA4C,CAAC;4BACxE2C,OAAO1C,KAAK2C,QAAQ;4BACpBC,QAAQ5C,KAAK6C,QAAQ;wBACvB;oBACF,OAAO,IAAI9F,QAAQ,CAACiD,KAAKD,IAAI,CAAC,EAAE;wBAC9B,8BAA8B;wBAC9B,MAAMxC,QAAQsC,YAAYwC,WAAW;wBACrC,MAAMnF,UAAUH,QAAQ,CAACiD,KAAKD,IAAI,CAAC,CAAC7C,OAAO;wBAE3C,IAAIK,SAASL,SAAS;4BACpB,IAAI4G,iBAAiB;4BACrB,IAAIC,eAAe;4BACnB,IAAIC,YAAY;4BAEhB,IACE/E,GAAGsD,eAAe,CAAChF,UACnB0B,GAAGgF,+BAA+B,CAAC1G,QACnC;oCAQGR,6BAAAA;gCAPH,MAAMmH,MAAM,MAAMvH,mBAAmBY,MAAM0C,OAAO,MAAM;gCACxD,MAAMkE,gBAAgB3C,OAAOS,IAAI,CAAC/E,SAASkH,MAAM,CAAC,CAACtG,IACjD,QAAQuG,IAAI,CAACvG;gCAGf,IACE,CAACqG,cAAcb,QAAQ,CAACY,QACxB,GAACnH,8BAAAA,CAAAA,sBAAAA,QAAQ,CAACiD,KAAKD,IAAI,CAAC,EAACzC,OAAO,qBAA3BP,iCAAAA,qBAA8BmH,OAC/B;oCACAF,YAAY;oCACZF,iBAAiBI;gCACnB;4BACF,OAAO,IACLjF,GAAGqF,gBAAgB,CAAC/G,UACnB0B,GAAGsF,uBAAuB,CAAChH,UAC1B0B,GAAGuF,YAAY,CAAC,AAACjH,MAAckH,QAAQ,KACtCxF,CAAAA,GAAGqF,gBAAgB,CAAC,AAAC/G,MAAcmH,OAAO,CAAClF,IAAI,KAC7CP,GAAGoE,YAAY,CAAC,AAAC9F,MAAcmH,OAAO,CAAClF,IAAI,KAC1C,AAACjC,MAAcmH,OAAO,CAAClF,IAAI,CAACS,OAAO,OAAO,UAAU,KACzDhB,GAAGoE,YAAY,CAAC9F,UAAUA,MAAM0C,OAAO,OAAO,YAC/C;oCAEKlD,8BAAAA;gCADL,MAAMe,IAAIP,MAAM0C,OAAO;gCACvB,IAAI,GAAClD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACiD,KAAKD,IAAI,CAAC,EAACzC,OAAO,qBAA3BP,kCAAAA,sBAA8Be,KAAI;oCACrCkG,YAAY;oCACZF,iBAAiBhG;gCACnB;4BACF,OAAO,IACLP,MAAMiC,IAAI,KAAKP,GAAGQ,UAAU,CAACkF,WAAW,IACxCpH,MAAMiC,IAAI,KAAKP,GAAGQ,UAAU,CAACmF,YAAY,EACzC;oCAEK7H,8BAAAA;gCADL,MAAMe,IAAIP,MAAM0C,OAAO;gCACvB,IAAI,GAAClD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACiD,KAAKD,IAAI,CAAC,EAACzC,OAAO,qBAA3BP,kCAAAA,sBAA8Be,KAAI;oCACrCkG,YAAY;oCACZF,iBAAiBhG;gCACnB;4BACF,OAAO,IAAImB,GAAG4F,wBAAwB,CAACtH,QAAQ;oCAG1CR,8BAAAA;gCAFH,MAAMe,IAAIP,MAAM0C,OAAO;gCACvB,IACE,GAAClD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACiD,KAAKD,IAAI,CAAC,EAACzC,OAAO,qBAA3BP,kCAAAA,sBACCU,KAAKqH,SAAS,CAACvH,MAAMwH,QAAQ,CAACrD,GAAG,CAAC,CAACsD,IAAMA,EAAE/E,OAAO,QAEpD;oCACA+D,YAAY;oCACZF,iBAAiBhG;gCACnB;4BACF,OAAO,IACL,iBAAiB;4BACjBmB,GAAGgG,eAAe,CAAC1H,UACnB0B,GAAGiG,yBAAyB,CAAC3H,UAC7B0B,GAAGkG,0BAA0B,CAAC5H,UAC9B0B,GAAGsF,uBAAuB,CAAChH,QAC3B;gCACAyG,YAAY;gCACZF,iBAAiBvG,MAAM0C,OAAO;4BAChC,OAAO;gCACL,8DAA8D;gCAC9D+D,YAAY;gCACZF,iBAAiBvG,MAAM0C,OAAO;gCAC9B8D,eAAe,CAAC,CAAC,EAAED,eAAe,gCAAgC,EAAE9D,KAAKD,IAAI,CAAC,0DAA0D,CAAC;4BAC3I;4BAEA,IAAIiE,WAAW;gCACbZ,YAAYpB,IAAI,CAAC;oCACfuB,MAAMvE;oCACNwE,UAAUvE,GAAGwE,kBAAkB,CAACC,KAAK;oCACrCC,MAAM/G,eAAewI,oBAAoB;oCACzCvB,aACEE,gBACA,CAAC,CAAC,EAAED,eAAe,gCAAgC,EAAE9D,KAAKD,IAAI,CAAC,SAAS,CAAC;oCAC3E2C,OAAOnF,MAAMoF,QAAQ;oCACrBC,QAAQrF,MAAMsF,QAAQ;gCACxB;4BACF;wBACF;oBACF,OAAO,IAAI7C,KAAKD,IAAI,KAAKjD,sBAAsB;wBAC7C,gCAAgC;wBAChC,4BAA4B;wBAC5B,MAAMS,QAAQsC,YAAYwC,WAAW;wBACrC,IAAI9E,SAAS0B,GAAGiG,yBAAyB,CAAC3H,QAAQ;4BAChD,KAAK,MAAM8H,QAAQ9H,MAAM+H,UAAU,CAAE;gCACnC,IACErG,GAAGsG,oBAAoB,CAACF,SACxBpG,GAAGoE,YAAY,CAACgC,KAAKrF,IAAI,KACzBqF,KAAKrF,IAAI,CAACD,IAAI,KAAK,OACnB;oCACAqD,YAAYpB,IAAI,CAAC;wCACfuB,MAAMvE;wCACNwE,UAAUvE,GAAGwE,kBAAkB,CAACC,KAAK;wCACrCC,MAAM/G,eAAe4I,qBAAqB;wCAC1C3B,aAAa,CAAC,0HAA0H,CAAC;wCACzInB,OAAO2C,KAAK1C,QAAQ;wCACpBC,QAAQyC,KAAKxC,QAAQ;oCACvB;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAOO;IACT;AACF;AAEA,eAAexB,OAAM"}