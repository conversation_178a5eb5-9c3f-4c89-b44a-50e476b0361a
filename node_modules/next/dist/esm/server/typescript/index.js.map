{"version": 3, "sources": ["../../../src/server/typescript/index.ts"], "names": ["init", "getEntryInfo", "isAppEntryFile", "isDefaultFunctionExport", "isPositionInsideNode", "getSource", "isInsideApp", "NEXT_TS_ERRORS", "entryConfig", "serverLayer", "<PERSON><PERSON><PERSON><PERSON>", "clientBoundary", "serverBoundary", "metadata", "errorEntry", "createTSPlugin", "typescript", "ts", "create", "info", "proxy", "Object", "k", "keys", "languageService", "x", "args", "apply", "getCompletionsAtPosition", "fileName", "position", "options", "prior", "isGlobalCompletion", "isMemberCompletion", "isNewIdentifierLocation", "entries", "entryInfo", "client", "filterCompletionsAtPosition", "addCompletionsAtPosition", "source", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "push", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "data", "entryCompletionEntryDetails", "metadataCompletionEntryDetails", "getQuickInfoAtPosition", "definitions", "getDefinitionAtPosition", "hasDisallowedReactAPIDefinition", "metadataInfo", "overridden", "getSemanticDiagnostics", "isClientEntry", "isServerEntry", "isAppEntry", "server", "e", "file", "category", "DiagnosticCategory", "Error", "code", "MISPLACED_ENTRY_DIRECTIVE", "errorDiagnostic", "isImportDeclaration", "diagnostics", "getSemanticDiagnosticsForImportDeclaration", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "getSemanticDiagnosticsForExportVariableStatement", "metadataDiagnostics", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "getSemanticDiagnosticsForFunctionExport", "isFunctionDeclaration", "isExportDeclaration", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "getSemanticDiagnosticsForExportDeclaration", "getDefinitionAndBoundSpan", "metadataDefinition"], "mappings": "AAAA;;;;;;;;CAQC,GAED,SACEA,IAAI,EACJC,YAAY,EACZC,cAAc,EACdC,uBAAuB,EACvBC,oBAAoB,EACpBC,SAAS,EACTC,WAAW,QACN,UAAS;AAChB,SAASC,cAAc,QAAQ,aAAY;AAE3C,OAAOC,iBAAiB,iBAAgB;AACxC,OAAOC,iBAAiB,iBAAgB;AACxC,OAAOC,kBAAkB,gBAAe;AACxC,OAAOC,oBAAoB,0BAAyB;AACpD,OAAOC,oBAAoB,0BAAyB;AACpD,OAAOC,cAAc,mBAAkB;AACvC,OAAOC,gBAAgB,gBAAe;AAGtC,OAAO,MAAMC,iBAAsD,CAAC,EAClEC,YAAYC,EAAE,EACf;IACC,SAASC,OAAOC,IAAsC;QACpDnB,KAAK;YACHiB;YACAE;QACF;QAEA,0BAA0B;QAC1B,MAAMC,QAAQC,OAAOH,MAAM,CAAC;QAC5B,KAAK,IAAII,KAAKD,OAAOE,IAAI,CAACJ,KAAKK,eAAe,EAAG;YAC/C,MAAMC,IAAI,AAACN,KAAKK,eAAe,AAAQ,CAACF,EAAE;YAC1CF,KAAK,CAACE,EAAE,GAAG,CAAC,GAAGI,OAAoBD,EAAEE,KAAK,CAACR,KAAKK,eAAe,EAAEE;QACnE;QAEA,kBAAkB;QAClBN,MAAMQ,wBAAwB,GAAG,CAC/BC,UACAC,UACAC;YAEA,IAAIC,QAAQb,KAAKK,eAAe,CAACI,wBAAwB,CACvDC,UACAC,UACAC,YACG;gBACHE,oBAAoB;gBACpBC,oBAAoB;gBACpBC,yBAAyB;gBACzBC,SAAS,EAAE;YACb;YACA,IAAI,CAAClC,eAAe2B,WAAW,OAAOG;YAEtC,0BAA0B;YAC1B,MAAMK,YAAYpC,aAAa4B;YAC/B,IAAI,CAACQ,UAAUC,MAAM,EAAE;gBACrB,gDAAgD;gBAChDN,MAAMI,OAAO,GAAG3B,YAAY8B,2BAA2B,CAACP,MAAMI,OAAO;gBAErE,6CAA6C;gBAC7CJ,QAAQnB,SAAS0B,2BAA2B,CAC1CV,UACAC,UACAC,SACAC;YAEJ;YAEA,2CAA2C;YAC3CxB,YAAYgC,wBAAwB,CAACX,UAAUC,UAAUE;YAEzD,MAAMS,SAASpC,UAAUwB;YACzB,IAAI,CAACY,QAAQ,OAAOT;YAEpBf,GAAGyB,YAAY,CAACD,QAAS,CAACE;gBACxB,uDAAuD;gBACvD,IACEvC,qBAAqB0B,UAAUa,SAC/BxC,wBAAwBwC,OACxB;oBACAX,MAAMI,OAAO,CAACQ,IAAI,IACblC,aAAakB,wBAAwB,CACtCC,UACAc,MACAb;gBAGN;YACF;YAEA,OAAOE;QACT;QAEA,+BAA+B;QAC/BZ,MAAMyB,yBAAyB,GAAG,CAChChB,UACAC,UACAgB,WACAC,eACAN,QACAO,aACAC;YAEA,MAAMC,8BAA8B1C,YAAYqC,yBAAyB,CACvEC,WACAG;YAEF,IAAIC,6BAA6B,OAAOA;YAExC,MAAMC,iCAAiCtC,SAASgC,yBAAyB,CACvEhB,UACAC,UACAgB,WACAC,eACAN,QACAO,aACAC;YAEF,IAAIE,gCAAgC,OAAOA;YAE3C,OAAOhC,KAAKK,eAAe,CAACqB,yBAAyB,CACnDhB,UACAC,UACAgB,WACAC,eACAN,QACAO,aACAC;QAEJ;QAEA,aAAa;QACb7B,MAAMgC,sBAAsB,GAAG,CAACvB,UAAkBC;YAChD,MAAME,QAAQb,KAAKK,eAAe,CAAC4B,sBAAsB,CACvDvB,UACAC;YAEF,IAAI,CAAC5B,eAAe2B,WAAW,OAAOG;YAEtC,oEAAoE;YACpE,MAAMK,YAAYpC,aAAa4B;YAC/B,IAAI,CAACQ,UAAUC,MAAM,EAAE;gBACrB,MAAMe,cAAclC,KAAKK,eAAe,CAAC8B,uBAAuB,CAC9DzB,UACAC;gBAEF,IACEuB,eACA5C,YAAY8C,+BAA+B,CAACF,cAC5C;oBACA;gBACF;gBAEA,MAAMG,eAAe3C,SAASuC,sBAAsB,CAACvB,UAAUC;gBAC/D,IAAI0B,cAAc,OAAOA;YAC3B;YAEA,MAAMC,aAAajD,YAAY4C,sBAAsB,CAACvB,UAAUC;YAChE,IAAI2B,YAAY,OAAOA;YAEvB,OAAOzB;QACT;QAEA,qCAAqC;QACrCZ,MAAMsC,sBAAsB,GAAG,CAAC7B;YAC9B,MAAMG,QAAQb,KAAKK,eAAe,CAACkC,sBAAsB,CAAC7B;YAC1D,MAAMY,SAASpC,UAAUwB;YACzB,IAAI,CAACY,QAAQ,OAAOT;YAEpB,IAAI2B,gBAAgB;YACpB,IAAIC,gBAAgB;YACpB,MAAMC,aAAa3D,eAAe2B;YAElC,IAAI;gBACF,MAAMQ,YAAYpC,aAAa4B,UAAU;gBACzC8B,gBAAgBtB,UAAUC,MAAM;gBAChCsB,gBAAgBvB,UAAUyB,MAAM;YAClC,EAAE,OAAOC,GAAQ;gBACf/B,MAAMY,IAAI,CAAC;oBACToB,MAAMvB;oBACNwB,UAAUhD,GAAGiD,kBAAkB,CAACC,KAAK;oBACrCC,MAAM7D,eAAe8D,yBAAyB;oBAC9C,GAAGN,CAAC;gBACN;gBACAJ,gBAAgB;gBAChBC,gBAAgB;YAClB;YAEA,IAAItD,YAAYuB,WAAW;gBACzB,MAAMyC,kBAAkBxD,WAAW4C,sBAAsB,CACvDjB,QACAkB;gBAEF3B,MAAMY,IAAI,IAAI0B;YAChB;YAEArD,GAAGyB,YAAY,CAACD,QAAS,CAACE;oBAgBtBA,iBAqEAA;gBApFF,IAAI1B,GAAGsD,mBAAmB,CAAC5B,OAAO;oBAChC,aAAa;oBACb,IAAIkB,YAAY;wBACd,IAAI,CAACF,iBAAiBC,eAAe;4BACnC,oDAAoD;4BACpD,MAAMY,cACJ/D,YAAYgE,0CAA0C,CACpDhC,QACAE;4BAEJX,MAAMY,IAAI,IAAI4B;wBAChB;oBACF;gBACF,OAAO,IACLvD,GAAGyD,mBAAmB,CAAC/B,WACvBA,kBAAAA,KAAKgC,SAAS,qBAAdhC,gBAAgBiC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK7D,GAAG8D,UAAU,CAACC,aAAa,IAClE;oBACA,mBAAmB;oBACnB,IAAInB,YAAY;wBACd,yCAAyC;wBACzC,MAAMW,cACJhE,YAAYyE,gDAAgD,CAC1DxC,QACAE;wBAEJ,MAAMuC,sBAAsBvB,gBACxB9C,SAASsE,6DAA6D,CACpEtD,UACAc,QAEF9B,SAASoE,gDAAgD,CACvDpD,UACAc;wBAENX,MAAMY,IAAI,IAAI4B,gBAAgBU;oBAChC;oBAEA,IAAIvB,eAAe;wBACjB3B,MAAMY,IAAI,IACLjC,eAAesE,gDAAgD,CAChExC,QACAE;oBAGN;oBAEA,IAAIiB,eAAe;wBACjB5B,MAAMY,IAAI,IACLhC,eAAeqE,gDAAgD,CAChExC,QACAE;oBAGN;gBACF,OAAO,IAAIxC,wBAAwBwC,OAAO;oBACxC,8BAA8B;oBAC9B,IAAIkB,YAAY;wBACd,MAAMW,cAAc9D,aAAagD,sBAAsB,CACrD7B,UACAY,QACAE;wBAEFX,MAAMY,IAAI,IAAI4B;oBAChB;oBAEA,IAAIb,eAAe;wBACjB3B,MAAMY,IAAI,IACLjC,eAAeyE,uCAAuC,CACvD3C,QACAE;oBAGN;oBAEA,IAAIiB,eAAe;wBACjB5B,MAAMY,IAAI,IACLhC,eAAewE,uCAAuC,CACvD3C,QACAE;oBAGN;gBACF,OAAO,IACL1B,GAAGoE,qBAAqB,CAAC1C,WACzBA,mBAAAA,KAAKgC,SAAS,qBAAdhC,iBAAgBiC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK7D,GAAG8D,UAAU,CAACC,aAAa,IAClE;oBACA,sBAAsB;oBACtB,IAAInB,YAAY;wBACd,MAAMqB,sBAAsBvB,gBACxB9C,SAASsE,6DAA6D,CACpEtD,UACAc,QAEF9B,SAASoE,gDAAgD,CACvDpD,UACAc;wBAENX,MAAMY,IAAI,IAAIsC;oBAChB;oBAEA,IAAIvB,eAAe;wBACjB3B,MAAMY,IAAI,IACLjC,eAAeyE,uCAAuC,CACvD3C,QACAE;oBAGN;oBAEA,IAAIiB,eAAe;wBACjB5B,MAAMY,IAAI,IACLhC,eAAewE,uCAAuC,CACvD3C,QACAE;oBAGN;gBACF,OAAO,IAAI1B,GAAGqE,mBAAmB,CAAC3C,OAAO;oBACvC,iBAAiB;oBACjB,IAAIkB,YAAY;wBACd,MAAMqB,sBAAsBvB,gBACxB9C,SAAS0E,uDAAuD,CAC9D1D,UACAc,QAEF9B,SAAS2E,0CAA0C,CACjD3D,UACAc;wBAENX,MAAMY,IAAI,IAAIsC;oBAChB;oBAEA,IAAItB,eAAe;wBACjB5B,MAAMY,IAAI,IACLhC,eAAe4E,0CAA0C,CAC1D/C,QACAE;oBAGN;gBACF;YACF;YAEA,OAAOX;QACT;QAEA,4CAA4C;QAC5CZ,MAAMqE,yBAAyB,GAAG,CAAC5D,UAAkBC;YACnD,MAAMO,YAAYpC,aAAa4B;YAC/B,IAAI3B,eAAe2B,aAAa,CAACQ,UAAUC,MAAM,EAAE;gBACjD,MAAMoD,qBAAqB7E,SAAS4E,yBAAyB,CAC3D5D,UACAC;gBAEF,IAAI4D,oBAAoB,OAAOA;YACjC;YAEA,OAAOvE,KAAKK,eAAe,CAACiE,yBAAyB,CAAC5D,UAAUC;QAClE;QAEA,OAAOV;IACT;IAEA,OAAO;QAAEF;IAAO;AAClB,EAAC"}