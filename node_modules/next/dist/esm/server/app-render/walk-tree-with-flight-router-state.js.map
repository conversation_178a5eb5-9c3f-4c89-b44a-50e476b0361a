{"version": 3, "sources": ["../../../src/server/app-render/walk-tree-with-flight-router-state.tsx"], "names": ["React", "canSegmentBeOverridden", "matchSegment", "getLinkAndScriptTags", "getPreloadableFonts", "addSearchParamsIfPageSegment", "createFlightRouterStateFromLoaderTree", "parseLoaderTree", "getLayerAssets", "hasLoadingComponentInTree", "createComponentTree", "DEFAULT_SEGMENT_KEY", "walkTreeWithFlightRouterState", "createSegmentPath", "loaderTreeToFilter", "parentParams", "<PERSON><PERSON><PERSON><PERSON>", "flightRouterState", "parentRendered", "rscPayloadHead", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "asNotFound", "metadataOutlet", "ctx", "renderOpts", "nextFontManifest", "experimental", "query", "isPrefetch", "getDynamicParamFromSegment", "componentMod", "tree", "loaderTree", "segment", "parallelRoutes", "components", "parallelRoutesKeys", "Object", "keys", "layout", "isLayout", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "renderComponentsOnThisLevel", "length", "shouldSkipComponentTree", "ppr", "Boolean", "loading", "overriddenSegment", "routerState", "seedData", "firstItem", "layoutOrPagePath", "layerAssets", "Set", "head", "<PERSON><PERSON><PERSON>", "injectedCSSWithCurrentLayout", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "clientReferenceManifest", "paths", "Promise", "all", "map", "parallelRouteKey", "parallelRoute", "currentSegmentPath", "path", "child", "item", "filter", "flat"], "mappings": ";AAMA,OAAOA,WAAW,QAAO;AACzB,SACEC,sBAAsB,EACtBC,YAAY,QACP,yCAAwC;AAE/C,SAASC,oBAAoB,QAAQ,8BAA6B;AAClE,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SACEC,4BAA4B,EAC5BC,qCAAqC,QAChC,gDAA+C;AACtD,SAASC,eAAe,QAAQ,sBAAqB;AAErD,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,mBAAmB,QAAQ,2BAA0B;AAE9D;;;CAGC,GACD,OAAO,eAAeC,8BAA8B,EAClDC,iBAAiB,EACjBC,kBAAkB,EAClBC,YAAY,EACZC,OAAO,EACPC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,EACdC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,kBAAkB,EAClBC,UAAU,EACVC,cAAc,EACdC,GAAG,EAgBJ;IACC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAE,EAC9CC,KAAK,EACLC,UAAU,EACVC,0BAA0B,EAC1BC,cAAc,EAAEC,MAAMC,UAAU,EAAE,EACnC,GAAGT;IAEJ,MAAM,CAACU,SAASC,gBAAgBC,WAAW,GAAGxB;IAE9C,MAAMyB,qBAAqBC,OAAOC,IAAI,CAACJ;IAEvC,MAAM,EAAEK,MAAM,EAAE,GAAGJ;IACnB,MAAMK,WAAW,OAAOD,WAAW;IAEnC;;GAEC,GACD,MAAME,wBAAwBD,YAAY,CAACpB;IAC3C;;GAEC,GACD,MAAMsB,uCACJtB,sBAAsBqB;IAExB,8JAA8J;IAC9J,MAAME,eAAed,2BAA2BI;IAChD,MAAMW,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAGjC,YAAY;QACf,CAAC+B,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IACAjC;IACN,MAAMmC,gBAAyB7C,6BAC7ByC,eAAeA,aAAaK,WAAW,GAAGf,SAC1CN;IAGF;;GAEC,GACD,MAAMsB,8BACJ,oCAAoC;IACpC,CAACnC,qBACD,yDAAyD;IACzD,CAACf,aAAagD,eAAejC,iBAAiB,CAAC,EAAE,KACjD,wBAAwB;IACxBsB,mBAAmBc,MAAM,KAAK,KAC9B,mBAAmB;IACnBpC,iBAAiB,CAAC,EAAE,KAAK;IAE3B,MAAMqC,0BACJ,+DAA+D;IAC/D,CAACzB,aAAa0B,GAAG,IACjBxB,cACA,CAACyB,QAAQlB,WAAWmB,OAAO,KAC1BxC,CAAAA,qBACC,0HAA0H;IAC1H,CAACR,0BAA0B0B,WAAU;IAEzC,IAAI,CAACjB,kBAAkBkC,6BAA6B;QAClD,MAAMM,oBACJzC,qBACAhB,uBAAuBiD,eAAejC,iBAAiB,CAAC,EAAE,IACtDA,iBAAiB,CAAC,EAAE,GACpBiC;QAEN,MAAMS,cAAcrD,sCAClB,wDAAwD;QACxDQ,oBACAkB,4BACAF;QAGF,IAAIwB,yBAAyB;YAC3B,6BAA6B;YAC7B,OAAO;gBAAC;oBAACI;oBAAmBC;oBAAa;oBAAM;iBAAK;aAAC;QACvD,OAAO;YACL,0DAA0D;YAC1D,MAAM,EAAEC,QAAQ,EAAE,GAAG,MAAMlD,oBACzB,mEAAmE;YACnE;gBACEgB;gBACAb;gBACAsB,YAAYrB;gBACZC,cAAcgC;gBACdc,WAAW7C;gBACXI;gBACAC;gBACAC;gBACA,wKAAwK;gBACxKC;gBACAC;gBACAC;YACF;YAGF,cAAc;YACd,MAAM,EAAEqC,gBAAgB,EAAE,GAAGvD,gBAAgBO;YAC7C,MAAMiD,cAAcvD,eAAe;gBACjCkB;gBACAoC;gBACA1C,aAAa,IAAI4C,IAAI5C;gBACrBC,YAAY,IAAI2C,IAAI3C;gBACpBC,yBAAyB,IAAI0C,IAAI1C;YACnC;YACA,MAAM2C,qBACJ;;oBACGF;oBACA5C;;;YAIL,OAAO;gBAAC;oBAACuC;oBAAmBC;oBAAaC;oBAAUK;iBAAK;aAAC;QAC3D;IACF;IAEA,wEAAwE;IACxE,yEAAyE;IACzE,yBAAyB;IACzB,MAAMC,aAAaxB,0BAAAA,MAAQ,CAAC,EAAE;IAC9B,MAAMyB,+BAA+B,IAAIH,IAAI5C;IAC7C,MAAMgD,8BAA8B,IAAIJ,IAAI3C;IAC5C,MAAMgD,2CAA2C,IAAIL,IACnD1C;IAEF,IAAI4C,YAAY;QACd/D,qBACEuB,IAAI4C,uBAAuB,EAC3BJ,YACAC,8BACAC,6BACA;QAEFhE,oBACEwB,kBACAsC,YACAG;IAEJ;IAEA,oCAAoC;IACpC,MAAME,QAA0B,AAC9B,CAAA,MAAMC,QAAQC,GAAG,CACflC,mBAAmBmC,GAAG,CAAC,OAAOC;QAC5B,uDAAuD;QACvD,MAAMC,gBAAgBvC,cAAc,CAACsC,iBAAiB;QAEtD,MAAME,qBAAwC7D,UAC1C;YAAC2D;SAAiB,GAClB;YAACzB;YAAeyB;SAAiB;QAErC,MAAMG,OAAO,MAAMlE,8BAA8B;YAC/Cc;YACAb,mBAAmB,CAACkE;gBAClB,OAAOlE,kBAAkB;uBAAIgE;uBAAuBE;iBAAM;YAC5D;YACAjE,oBAAoB8D;YACpB7D,cAAcgC;YACd9B,mBACEA,qBAAqBA,iBAAiB,CAAC,EAAE,CAAC0D,iBAAiB;YAC7DzD,gBAAgBA,kBAAkBkC;YAClCpC,SAAS;YACTG;YACAC,aAAa+C;YACb9C,YAAY+C;YACZ9C,yBAAyB+C;YACzB9C,oBAAoBsB;YACpBrB;YACAC;QACF;QAEA,OAAOqD,KACJJ,GAAG,CAAC,CAACM;YACJ,+DAA+D;YAC/D,uEAAuE;YACvE,IACEA,IAAI,CAAC,EAAE,KAAKrE,uBACZM,qBACA,CAAC,CAACA,iBAAiB,CAAC,EAAE,CAAC0D,iBAAiB,CAAC,EAAE,IAC3C1D,iBAAiB,CAAC,EAAE,CAAC0D,iBAAiB,CAAC,EAAE,KAAK,WAC9C;gBACA,OAAO;YACT;YACA,OAAO;gBAACzB;gBAAeyB;mBAAqBK;aAAK;QACnD,GACCC,MAAM,CAACzB;IACZ,GACF,EACA0B,IAAI;IAEN,OAAOX;AACT"}