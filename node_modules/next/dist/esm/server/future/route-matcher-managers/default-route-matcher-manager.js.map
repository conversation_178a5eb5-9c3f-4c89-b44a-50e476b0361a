{"version": 3, "sources": ["../../../../src/server/future/route-matcher-managers/default-route-matcher-manager.ts"], "names": ["isDynamicRoute", "getSortedRoutes", "LocaleRouteMatcher", "ensureLeadingSlash", "Detached<PERSON>romise", "DefaultRouteMatcherManager", "compilationID", "providers", "length", "waitTillReady", "waitTillReadyPromise", "reload", "promise", "resolve", "reject", "matchers", "providersMatchers", "Promise", "all", "map", "provider", "Map", "duplicates", "providerMatchers", "matcher", "duplicated", "duplicate", "get", "definition", "pathname", "others", "push", "set", "previousMatchers", "every", "cachedMatcher", "index", "static", "filter", "isDynamic", "dynamic", "reference", "pathnames", "Array", "indexes", "sorted", "sortedDynamicMatchers", "isArray", "Error", "dynamicMatches", "err", "lastCompilationID", "test", "options", "match", "matchAll", "validate", "i18n", "inferredFromDefault", "skipDynamic"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mCAAkC;AAOjE,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,kBAAkB,QAAQ,qDAAoD;AACvF,SAASC,eAAe,QAAQ,gCAA+B;AAQ/D,OAAO,MAAMC;IASX;;;GAGC,GACD,IAAYC,gBAAgB;QAC1B,OAAO,IAAI,CAACC,SAAS,CAACC,MAAM;IAC9B;IAGA,MAAaC,gBAA+B;QAC1C,IAAI,IAAI,CAACC,oBAAoB,EAAE;YAC7B,MAAM,IAAI,CAACA,oBAAoB;YAC/B,OAAO,IAAI,CAACA,oBAAoB;QAClC;IACF;IAGA,MAAaC,SAAS;QACpB,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAG,IAAIV;QACzC,IAAI,CAACM,oBAAoB,GAAGE;QAE5B,sEAAsE;QACtE,yEAAyE;QACzE,gBAAgB;QAChB,MAAMN,gBAAgB,IAAI,CAACA,aAAa;QAExC,IAAI;YACF,+CAA+C;YAC/C,MAAMS,WAAgC,EAAE;YAExC,kCAAkC;YAClC,MAAMC,oBACJ,MAAMC,QAAQC,GAAG,CAAC,IAAI,CAACX,SAAS,CAACY,GAAG,CAAC,CAACC,WAAaA,SAASL,QAAQ;YAEtE,0CAA0C;YAC1C,MAAMG,MAAM,IAAIG;YAChB,MAAMC,aAA6C,CAAC;YACpD,KAAK,MAAMC,oBAAoBP,kBAAmB;gBAChD,KAAK,MAAMQ,WAAWD,iBAAkB;oBACtC,wEAAwE;oBACxE,IAAIC,QAAQC,UAAU,EAAE,OAAOD,QAAQC,UAAU;oBACjD,yDAAyD;oBACzD,MAAMC,YAAYR,IAAIS,GAAG,CAACH,QAAQI,UAAU,CAACC,QAAQ;oBACrD,IAAIH,WAAW;wBACb,6DAA6D;wBAC7D,+DAA+D;wBAC/D,gEAAgE;wBAChE,mEAAmE;wBACnE,+DAA+D;wBAC/D,mEAAmE;wBACnE,2DAA2D;wBAC3D,iEAAiE;wBACjE,oEAAoE;wBACpE,8DAA8D;wBAC9D,oEAAoE;wBACpE,kDAAkD;wBAClD,MAAMI,SAASR,UAAU,CAACE,QAAQI,UAAU,CAACC,QAAQ,CAAC,IAAI;4BACxDH;yBACD;wBACDI,OAAOC,IAAI,CAACP;wBACZF,UAAU,CAACE,QAAQI,UAAU,CAACC,QAAQ,CAAC,GAAGC;wBAE1C,wCAAwC;wBACxCJ,UAAUD,UAAU,GAAGK;wBACvBN,QAAQC,UAAU,GAAGK;oBAErB,6DAA6D;oBAC/D;oBAEAf,SAASgB,IAAI,CAACP;oBAEd,yCAAyC;oBACzCN,IAAIc,GAAG,CAACR,QAAQI,UAAU,CAACC,QAAQ,EAAEL;gBACvC;YACF;YAEA,yEAAyE;YACzE,4BAA4B;YAC5B,IAAI,CAACT,QAAQ,CAACO,UAAU,GAAGA;YAE3B,uEAAuE;YACvE,wEAAwE;YACxE,iEAAiE;YACjE,IACE,IAAI,CAACW,gBAAgB,CAACzB,MAAM,KAAKO,SAASP,MAAM,IAChD,IAAI,CAACyB,gBAAgB,CAACC,KAAK,CACzB,CAACC,eAAeC,QAAUD,kBAAkBpB,QAAQ,CAACqB,MAAM,GAE7D;gBACA;YACF;YACA,IAAI,CAACH,gBAAgB,GAAGlB;YAExB,4DAA4D;YAC5D,IAAI,CAACA,QAAQ,CAACsB,MAAM,GAAGtB,SAASuB,MAAM,CAAC,CAACd,UAAY,CAACA,QAAQe,SAAS;YAEtE,2EAA2E;YAC3E,MAAMC,UAAUzB,SAASuB,MAAM,CAAC,CAACd,UAAYA,QAAQe,SAAS;YAE9D,yEAAyE;YACzE,sEAAsE;YACtE,wEAAwE;YACxE,qEAAqE;YACrE,uBAAuB;YAEvB,MAAME,YAAY,IAAIpB;YACtB,MAAMqB,YAAY,IAAIC;YACtB,IAAK,IAAIP,QAAQ,GAAGA,QAAQI,QAAQhC,MAAM,EAAE4B,QAAS;gBACnD,yCAAyC;gBACzC,MAAMP,WAAWW,OAAO,CAACJ,MAAM,CAACR,UAAU,CAACC,QAAQ;gBAEnD,mEAAmE;gBACnE,MAAMe,UAAUH,UAAUd,GAAG,CAACE,aAAa,EAAE;gBAC7Ce,QAAQb,IAAI,CAACK;gBAEb,iEAAiE;gBACjE,mEAAmE;gBACnE,uDAAuD;gBACvD,IAAIQ,QAAQpC,MAAM,KAAK,GAAGiC,UAAUT,GAAG,CAACH,UAAUe;qBAE7C;gBAELF,UAAUX,IAAI,CAACF;YACjB;YAEA,+BAA+B;YAC/B,MAAMgB,SAAS5C,gBAAgByC;YAE/B,yEAAyE;YACzE,wEAAwE;YACxE,wEAAwE;YACxE,uEAAuE;YACvE,iBAAiB;YACjB,MAAMI,wBAA6C,EAAE;YACrD,KAAK,MAAMjB,YAAYgB,OAAQ;gBAC7B,MAAMD,UAAUH,UAAUd,GAAG,CAACE;gBAC9B,IAAI,CAACc,MAAMI,OAAO,CAACH,UAAU;oBAC3B,MAAM,IAAII,MAAM;gBAClB;gBAEA,MAAMC,iBAAiBL,QAAQzB,GAAG,CAAC,CAACiB,QAAUI,OAAO,CAACJ,MAAM;gBAE5DU,sBAAsBf,IAAI,IAAIkB;YAChC;YAEA,IAAI,CAAClC,QAAQ,CAACyB,OAAO,GAAGM;YAExB,uEAAuE;YACvE,IAAI,IAAI,CAACxC,aAAa,KAAKA,eAAe;gBACxC,MAAM,IAAI0C,MACR;YAEJ;QACF,EAAE,OAAOE,KAAK;YACZpC,OAAOoC;QACT,SAAU;YACR,oEAAoE;YACpE,IAAI,CAACC,iBAAiB,GAAG7C;YACzBO;QACF;IACF;IAEOkB,KAAKX,QAA8B,EAAQ;QAChD,IAAI,CAACb,SAAS,CAACwB,IAAI,CAACX;IACtB;IAEA,MAAagC,KAAKvB,QAAgB,EAAEwB,OAAqB,EAAoB;QAC3E,6CAA6C;QAC7C,MAAMC,QAAQ,MAAM,IAAI,CAACA,KAAK,CAACzB,UAAUwB;QAEzC,0EAA0E;QAC1E,uEAAuE;QACvE,0BAA0B;QAC1B,OAAOC,UAAU;IACnB;IAEA,MAAaA,MACXzB,QAAgB,EAChBwB,OAAqB,EACmC;QACxD,4EAA4E;QAC5E,yEAAyE;QACzE,mBAAmB;QACnB,WAAW,MAAMC,SAAS,IAAI,CAACC,QAAQ,CAAC1B,UAAUwB,SAAU;YAC1D,OAAOC;QACT;QAEA,OAAO;IACT;IAEA;;;;;;;GAOC,GACD,AAAUE,SACR3B,QAAgB,EAChBL,OAAqB,EACrB6B,OAAqB,EACF;YAQfA;QAPJ,IAAI7B,mBAAmBtB,oBAAoB;YACzC,OAAOsB,QAAQ8B,KAAK,CAACzB,UAAUwB;QACjC;QAEA,wEAAwE;QACxE,sEAAsE;QACtE,qDAAqD;QACrD,KAAIA,gBAAAA,QAAQI,IAAI,qBAAZJ,cAAcK,mBAAmB,EAAE;YACrC,OAAOlC,QAAQ8B,KAAK,CAACD,QAAQI,IAAI,CAAC5B,QAAQ;QAC5C;QAEA,OAAOL,QAAQ8B,KAAK,CAACzB;IACvB;IAEA,OAAc0B,SACZ1B,QAAgB,EAChBwB,OAAqB,EACoD;QACzE,yEAAyE;QACzE,4EAA4E;QAC5E,2EAA2E;QAC3E,4EAA4E;QAC5E,4EAA4E;QAC5E,SAAS;QACT,IAAI,IAAI,CAACF,iBAAiB,KAAK,IAAI,CAAC7C,aAAa,EAAE;YACjD,MAAM,IAAI0C,MACR;QAEJ;QAEA,0DAA0D;QAC1DnB,WAAW1B,mBAAmB0B;QAE9B,2EAA2E;QAC3E,wEAAwE;QACxE,4EAA4E;QAC5E,sCAAsC;QACtC,IAAI,CAAC7B,eAAe6B,WAAW;YAC7B,KAAK,MAAML,WAAW,IAAI,CAACT,QAAQ,CAACsB,MAAM,CAAE;gBAC1C,MAAMiB,QAAQ,IAAI,CAACE,QAAQ,CAAC3B,UAAUL,SAAS6B;gBAC/C,IAAI,CAACC,OAAO;gBAEZ,MAAMA;YACR;QACF;QAEA,uDAAuD;QACvD,IAAID,2BAAAA,QAASM,WAAW,EAAE,OAAO;QAEjC,uDAAuD;QACvD,KAAK,MAAMnC,WAAW,IAAI,CAACT,QAAQ,CAACyB,OAAO,CAAE;YAC3C,MAAMc,QAAQ,IAAI,CAACE,QAAQ,CAAC3B,UAAUL,SAAS6B;YAC/C,IAAI,CAACC,OAAO;YAEZ,MAAMA;QACR;QAEA,4EAA4E;QAC5E,gCAAgC;QAChC,OAAO;IACT;;aA/QiB/C,YAAyC,EAAE;aACzCQ,WAA0B;YAC3CsB,QAAQ,EAAE;YACVG,SAAS,EAAE;YACXlB,YAAY,CAAC;QACf;aACQ6B,oBAAoB,IAAI,CAAC7C,aAAa;aAkBtC2B,mBAAgD,EAAE;;AAwP5D"}