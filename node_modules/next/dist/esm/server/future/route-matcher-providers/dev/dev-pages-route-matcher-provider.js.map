{"version": 3, "sources": ["../../../../../src/server/future/route-matcher-providers/dev/dev-pages-route-matcher-provider.ts"], "names": ["PagesRouteMatcher", "PagesLocaleRouteMatcher", "RouteKind", "path", "FileCacheRouteMatcherProvider", "DevPagesNormalizers", "DevPagesRouteMatcherProvider", "constructor", "pagesDir", "extensions", "reader", "localeNormalizer", "expression", "RegExp", "join", "normalizers", "test", "filename", "startsWith", "extension", "transform", "files", "matchers", "pathname", "normalize", "page", "bundlePath", "push", "kind", "PAGES", "i18n"], "mappings": "AACA,SACEA,iBAAiB,EACjBC,uBAAuB,QAClB,2CAA0C;AACjD,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,OAAOC,UAAU,OAAM;AAEvB,SAASC,6BAA6B,QAAQ,sCAAqC;AACnF,SAASC,mBAAmB,QAAQ,gCAA+B;AAEnE,OAAO,MAAMC,qCAAqCF;IAIhDG,YACE,AAAiBC,QAAgB,EACjC,AAAiBC,UAAiC,EAClDC,MAAkB,EAClB,AAAiBC,gBAAwC,CACzD;QACA,KAAK,CAACH,UAAUE;aALCF,WAAAA;aACAC,aAAAA;aAEAE,mBAAAA;QAIjB,4EAA4E;QAC5E,mBAAmB;QACnB,IAAI,CAACC,UAAU,GAAG,IAAIC,OAAO,CAAC,MAAM,EAAEJ,WAAWK,IAAI,CAAC,KAAK,EAAE,CAAC;QAE9D,IAAI,CAACC,WAAW,GAAG,IAAIV,oBAAoBG,UAAUC;IACvD;IAEQO,KAAKC,QAAgB,EAAW;QACtC,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAACL,UAAU,CAACI,IAAI,CAACC,WAAW,OAAO;QAE5C,qEAAqE;QACrE,yEAAyE;QACzE,6CAA6C;QAE7C,2DAA2D;QAC3D,IAAIA,SAASC,UAAU,CAACf,KAAKW,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE,WAAW,OAAO;QAEnE,KAAK,MAAMW,aAAa,IAAI,CAACV,UAAU,CAAE;YACvC,qEAAqE;YACrE,uBAAuB;YACvB,IAAIQ,aAAad,KAAKW,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE,CAAC,IAAI,EAAEW,UAAU,CAAC,GAAG;gBAC7D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAgBC,UACdC,KAA4B,EACe;QAC3C,MAAMC,WAAqC,EAAE;QAC7C,KAAK,MAAML,YAAYI,MAAO;YAC5B,4DAA4D;YAC5D,IAAI,CAAC,IAAI,CAACL,IAAI,CAACC,WAAW;YAE1B,MAAMM,WAAW,IAAI,CAACR,WAAW,CAACQ,QAAQ,CAACC,SAAS,CAACP;YACrD,MAAMQ,OAAO,IAAI,CAACV,WAAW,CAACU,IAAI,CAACD,SAAS,CAACP;YAC7C,MAAMS,aAAa,IAAI,CAACX,WAAW,CAACW,UAAU,CAACF,SAAS,CAACP;YAEzD,IAAI,IAAI,CAACN,gBAAgB,EAAE;gBACzBW,SAASK,IAAI,CACX,IAAI1B,wBAAwB;oBAC1B2B,MAAM1B,UAAU2B,KAAK;oBACrBN;oBACAE;oBACAC;oBACAT;oBACAa,MAAM,CAAC;gBACT;YAEJ,OAAO;gBACLR,SAASK,IAAI,CACX,IAAI3B,kBAAkB;oBACpB4B,MAAM1B,UAAU2B,KAAK;oBACrBN;oBACAE;oBACAC;oBACAT;gBACF;YAEJ;QACF;QAEA,OAAOK;IACT;AACF"}