{"version": 3, "sources": ["../../../../../src/server/future/route-matcher-providers/dev/dev-app-route-route-matcher-provider.ts"], "names": ["AppRouteRouteMatcher", "RouteKind", "FileCacheRouteMatcherProvider", "isAppRouteRoute", "DevAppNormalizers", "DevAppRouteRouteMatcherProvider", "constructor", "appDir", "extensions", "reader", "normalizers", "transform", "files", "matchers", "filename", "page", "normalize", "includes", "pathname", "bundlePath", "push", "kind", "APP_ROUTE"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,+CAA8C;AACnF,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,SAASC,6BAA6B,QAAQ,sCAAqC;AACnF,SAASC,eAAe,QAAQ,qCAAoC;AACpE,SAASC,iBAAiB,QAAQ,8BAA6B;AAE/D,OAAO,MAAMC,wCAAwCH;IAOnDI,YACEC,MAAc,EACdC,UAAiC,EACjCC,MAAkB,CAClB;QACA,KAAK,CAACF,QAAQE;QAEd,IAAI,CAACC,WAAW,GAAG,IAAIN,kBAAkBG,QAAQC;IACnD;IAEA,MAAgBG,UACdC,KAA4B,EACkB;QAC9C,MAAMC,WAAwC,EAAE;QAChD,KAAK,MAAMC,YAAYF,MAAO;YAC5B,MAAMG,OAAO,IAAI,CAACL,WAAW,CAACK,IAAI,CAACC,SAAS,CAACF;YAE7C,4DAA4D;YAC5D,IAAI,CAACX,gBAAgBY,OAAO;YAE5B,6CAA6C;YAC7C,IAAIA,KAAKE,QAAQ,CAAC,OAAO;YAEzB,MAAMC,WAAW,IAAI,CAACR,WAAW,CAACQ,QAAQ,CAACF,SAAS,CAACF;YACrD,MAAMK,aAAa,IAAI,CAACT,WAAW,CAACS,UAAU,CAACH,SAAS,CAACF;YAEzDD,SAASO,IAAI,CACX,IAAIpB,qBAAqB;gBACvBqB,MAAMpB,UAAUqB,SAAS;gBACzBJ;gBACAH;gBACAI;gBACAL;YACF;QAEJ;QAEA,OAAOD;IACT;AACF"}