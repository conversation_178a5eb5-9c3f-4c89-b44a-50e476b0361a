{"version": 3, "sources": ["../../../../../src/server/future/route-matcher-providers/dev/dev-app-page-route-matcher-provider.ts"], "names": ["AppPageRouteMatcher", "RouteKind", "FileCacheRouteMatcherProvider", "DevAppNormalizers", "normalizeCatchAllRoutes", "DevAppPageRouteMatcherProvider", "constructor", "appDir", "extensions", "reader", "normalizers", "expression", "RegExp", "join", "transform", "files", "cache", "Map", "routeFilenames", "Array", "appPaths", "filename", "test", "page", "normalize", "includes", "push", "pathname", "bundlePath", "set", "Object", "fromEntries", "entries", "map", "k", "v", "sort", "matchers", "cached", "get", "Error", "kind", "APP_PAGE"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,8CAA6C;AACjF,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,SAASC,6BAA6B,QAAQ,sCAAqC;AAEnF,SAASC,iBAAiB,QAAQ,8BAA6B;AAC/D,SAASC,uBAAuB,QAAQ,8CAA6C;AAErF,OAAO,MAAMC,uCAAuCH;IAIlDI,YACEC,MAAc,EACdC,UAAiC,EACjCC,MAAkB,CAClB;QACA,KAAK,CAACF,QAAQE;QAEd,IAAI,CAACC,WAAW,GAAG,IAAIP,kBAAkBI,QAAQC;QAEjD,mGAAmG;QACnG,aAAa;QACb,IAAI,CAACG,UAAU,GAAG,IAAIC,OACpB,CAAC,2BAA2B,EAAEJ,WAAWK,IAAI,CAAC,KAAK,EAAE,CAAC;IAE1D;IAEA,MAAgBC,UACdC,KAA4B,EACiB;QAC7C,2EAA2E;QAC3E,UAAU;QACV,MAAMC,QAAQ,IAAIC;QAIlB,MAAMC,iBAAiB,IAAIC;QAC3B,IAAIC,WAAqC,CAAC;QAC1C,KAAK,MAAMC,YAAYN,MAAO;YAC5B,4DAA4D;YAC5D,IAAI,CAAC,IAAI,CAACJ,UAAU,CAACW,IAAI,CAACD,WAAW;YAErC,MAAME,OAAO,IAAI,CAACb,WAAW,CAACa,IAAI,CAACC,SAAS,CAACH;YAE7C,6CAA6C;YAC7C,IAAIE,KAAKE,QAAQ,CAAC,OAAO;YAEzB,6DAA6D;YAC7DP,eAAeQ,IAAI,CAACL;YAEpB,MAAMM,WAAW,IAAI,CAACjB,WAAW,CAACiB,QAAQ,CAACH,SAAS,CAACH;YACrD,MAAMO,aAAa,IAAI,CAAClB,WAAW,CAACkB,UAAU,CAACJ,SAAS,CAACH;YAEzD,kCAAkC;YAClCL,MAAMa,GAAG,CAACR,UAAU;gBAAEE;gBAAMI;gBAAUC;YAAW;YAEjD,IAAID,YAAYP,UAAUA,QAAQ,CAACO,SAAS,CAACD,IAAI,CAACH;iBAC7CH,QAAQ,CAACO,SAAS,GAAG;gBAACJ;aAAK;QAClC;QAEAnB,wBAAwBgB;QAExB,sEAAsE;QACtEA,WAAWU,OAAOC,WAAW,CAC3BD,OAAOE,OAAO,CAACZ,UAAUa,GAAG,CAAC,CAAC,CAACC,GAAGC,EAAE,GAAK;gBAACD;gBAAGC,EAAEC,IAAI;aAAG;QAGxD,MAAMC,WAAuC,EAAE;QAC/C,KAAK,MAAMhB,YAAYH,eAAgB;YACrC,6CAA6C;YAC7C,MAAMoB,SAAStB,MAAMuB,GAAG,CAAClB;YACzB,IAAI,CAACiB,QAAQ;gBACX,MAAM,IAAIE,MAAM;YAClB;YACA,MAAM,EAAEb,QAAQ,EAAEJ,IAAI,EAAEK,UAAU,EAAE,GAAGU;YAEvCD,SAASX,IAAI,CACX,IAAI1B,oBAAoB;gBACtByC,MAAMxC,UAAUyC,QAAQ;gBACxBf;gBACAJ;gBACAK;gBACAP;gBACAD,UAAUA,QAAQ,CAACO,SAAS;YAC9B;QAEJ;QACA,OAAOU;IACT;AACF"}