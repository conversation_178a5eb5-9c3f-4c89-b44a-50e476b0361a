{"version": 3, "sources": ["../../../src/client/components/layout-router.tsx"], "names": ["OuterLayoutRouter", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "matchSegment", "hasOwnProperty", "subTree", "undefined", "slice", "findDOMNode", "instance", "window", "process", "env", "NODE_ENV", "originalConsoleError", "console", "error", "messages", "includes", "ReactDOM", "rectProperties", "shouldSkipElement", "element", "getComputedStyle", "position", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "React", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "nextElement<PERSON><PERSON>ling", "handleSmoothScroll", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "useContext", "GlobalLayoutRouterContext", "Error", "InnerLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "url", "childNodes", "tree", "cache<PERSON>ey", "buildId", "changeByServerResponse", "fullTree", "childNode", "get", "newLazyCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "lazyDataResolved", "loading", "set", "resolvedPrefetchRsc", "useDeferredValue", "resolvedRsc", "then", "use", "refetchTree", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "fetchServerResponse", "URL", "location", "origin", "nextUrl", "serverResponse", "setTimeout", "startTransition", "previousTree", "unresolvedThenable", "subtree", "LayoutRouterContext", "Provider", "value", "LoadingBoundary", "hasLoading", "loadingStyles", "loadingScripts", "Suspense", "fallback", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "notFoundStyles", "styles", "childNodesForParallelRouter", "treeSegment", "currentChildSegmentValue", "getSegmentValue", "preservedSegments", "map", "preservedSegment", "preservedSegmentValue", "createRouterCache<PERSON>ey", "TemplateContext", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "Boolean", "NotFoundBoundary", "RedirectBoundary", "isActive"], "mappings": "AAAA;;;;;+BAwfA;;;CAGC,GACD;;;eAAwBA;;;;;;iEAxejB;mEACc;+CAKd;qCAC6B;oCACD;+BACL;+BACD;oCACM;kCACF;kCACA;iCACD;sCACK;mDACa;AAElD;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIC,IAAAA,2BAAY,EAACL,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACM,cAAc,CAACJ,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMI,UAAUT,eACdU,WACAR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBK,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLP,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBU,KAAK,CAAC,IACxBT,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,4FAA4F;AAC5F;;CAEC,GACD,SAASU,YACPC,QAAoD;IAEpD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAC1C,wDAAwD;IACxD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,uBAAuBC,QAAQC,KAAK;QAC1C,IAAI;YACFD,QAAQC,KAAK,GAAG;iDAAIC;oBAAAA;;gBAClB,4DAA4D;gBAC5D,IAAI,CAACA,QAAQ,CAAC,EAAE,CAACC,QAAQ,CAAC,6CAA6C;oBACrEJ,wBAAwBG;gBAC1B;YACF;YACA,OAAOE,iBAAQ,CAACX,WAAW,CAACC;QAC9B,SAAU;YACRM,QAAQC,KAAK,GAAGF;QAClB;IACF;IACA,OAAOK,iBAAQ,CAACX,WAAW,CAACC;AAC9B;AAEA,MAAMW,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACJ,QAAQ,CAACK,iBAAiBD,SAASE,QAAQ,GAAG;QACpE,IAAIb,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1CE,QAAQU,IAAI,CACV,4FACAH;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMI,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOP,eAAeQ,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBR,OAAoB,EAAES,cAAsB;IAC1E,MAAML,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,yBAAxBC,2BACA,8FAA8F;IAC9FA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE;AAE/C;AAMA,MAAMK,mCAAmCC,cAAK,CAACC,SAAS;IAoGtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;;aAhHAN,wBAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAACjD,MAAM,KAAK,KAC1C,CAAC4C,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYtB,KAAK,CAAC,CAAC7B,SAASuD,QAC1BnD,IAAAA,2BAAY,EAACJ,SAASsD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMrB,eAAeY,kBAAkBZ,YAAY;gBAEnD,IAAIA,cAAc;oBAChBqB,UAAUtB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACqB,SAAS;oBACZA,UAAU/C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE+C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMpC,kBAAkBkC,SAAU;oBACtE,uGAAuG;oBACvG,IAAIA,QAAQG,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAH,UAAUA,QAAQG,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7EZ,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBZ,YAAY,GAAG;gBACjCY,kBAAkBK,YAAY,GAAG,EAAE;gBAEnCQ,IAAAA,sCAAkB,EAChB;oBACE,uEAAuE;oBACvE,IAAIzB,cAAc;wBACdqB,QAAwBK,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc1B,SAAS2B,eAAe;oBAC5C,MAAM/B,iBAAiB8B,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAIjC,uBAAuByB,SAAwBxB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7H8B,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAAClC,uBAAuByB,SAAwBxB,iBAAiB;wBAEjEwB,QAAwBK,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBpB,kBAAkBoB,cAAc;gBAClD;gBAGF,wEAAwE;gBACxEpB,kBAAkBoB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3BX,QAAQY,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BlB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMoB,UAAUC,IAAAA,iBAAU,EAACC,wDAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,IAAIG,MAAM;IAClB;IAEA,qBACE,qBAACjC;QACCW,aAAaA;QACbJ,mBAAmBuB,QAAQvB,iBAAiB;kBAE3CG;;AAGP;AAEA;;CAEC,GACD,SAASwB,kBAAkB,KAiB1B;IAjB0B,IAAA,EACzBC,iBAAiB,EACjBC,GAAG,EACHC,UAAU,EACV1B,WAAW,EACX2B,IAAI,EACJ,oDAAoD;IACpD,YAAY;IACZC,QAAQ,EAST,GAjB0B;IAkBzB,MAAMT,UAAUC,IAAAA,iBAAU,EAACC,wDAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,IAAIG,MAAM;IAClB;IAEA,MAAM,EAAEO,OAAO,EAAEC,sBAAsB,EAAEH,MAAMI,QAAQ,EAAE,GAAGZ;IAE5D,yDAAyD;IACzD,IAAIa,YAAYN,WAAWO,GAAG,CAACL;IAE/B,2EAA2E;IAC3E,sBAAsB;IACtB,IAAII,cAAc5E,WAAW;QAC3B,MAAM8E,mBAAkC;YACtCC,UAAU;YACVC,KAAK;YACLC,aAAa;YACbC,MAAM;YACNC,cAAc;YACdC,gBAAgB,IAAIC;YACpBC,kBAAkB;YAClBC,SAAS;QACX;QAEA;;KAEC,GACDX,YAAYE;QACZR,WAAWkB,GAAG,CAAChB,UAAUM;IAC3B;IAEA,yDAAyD;IAEzD,4EAA4E;IAC5E,2EAA2E;IAC3E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,MAAMW,sBACJb,UAAUK,WAAW,KAAK,OAAOL,UAAUK,WAAW,GAAGL,UAAUI,GAAG;IAExE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,EAAE;IACF,qEAAqE;IACrE,0EAA0E;IAC1E,gBAAgB;IAChB,MAAMA,MAAWU,IAAAA,uBAAgB,EAACd,UAAUI,GAAG,EAAES;IAEjD,wEAAwE;IACxE,2EAA2E;IAC3E,8EAA8E;IAC9E,mBAAmB;IACnB,MAAME,cACJ,OAAOX,QAAQ,YAAYA,QAAQ,QAAQ,OAAOA,IAAIY,IAAI,KAAK,aAC3DC,IAAAA,UAAG,EAACb,OACJA;IAEN,IAAI,CAACW,aAAa;QAChB,qEAAqE;QACrE,yEAAyE;QACzE,kCAAkC;QAElC,8CAA8C;QAC9C,IAAIZ,WAAWH,UAAUG,QAAQ;QACjC,IAAIA,aAAa,MAAM;YACrB;;OAEC,GACD,sBAAsB;YACtB,MAAMe,cAAcxG,eAAe;gBAAC;mBAAOsD;aAAY,EAAE+B;YACzD,MAAMoB,iBAAiBC,IAAAA,oEAAiC,EAACrB;YACzDC,UAAUG,QAAQ,GAAGA,WAAWkB,IAAAA,wCAAmB,EACjD,IAAIC,IAAI7B,KAAK8B,SAASC,MAAM,GAC5BN,aACAC,iBAAiBhC,QAAQsC,OAAO,GAAG,MACnC5B;YAEFG,UAAUU,gBAAgB,GAAG;QAC/B;QAEA;;KAEC,GACD,8DAA8D;QAC9D,MAAMgB,iBAAiBT,IAAAA,UAAG,EAACd;QAE3B,IAAI,CAACH,UAAUU,gBAAgB,EAAE;YAC/B,wGAAwG;YACxGiB,WAAW;gBACTC,IAAAA,sBAAe,EAAC;oBACd9B,uBAAuB;wBACrB+B,cAAc9B;wBACd2B;oBACF;gBACF;YACF;YAEA,uHAAuH;YACvH,yBAAyB;YACzB1B,UAAUU,gBAAgB,GAAG;QAC/B;QACA,yGAAyG;QACzG,iIAAiI;QACjIO,IAAAA,UAAG,EAACa,sCAAkB;IACxB;IAEA,yEAAyE;IACzE,MAAMC,UACJ,4EAA4E;kBAC5E,qBAACC,kDAAmB,CAACC,QAAQ;QAC3BC,OAAO;YACLvC,MAAMA,IAAI,CAAC,EAAE,CAACH,kBAAkB;YAChCE,YAAYM,UAAUQ,cAAc;YACpC,kDAAkD;YAClDf,KAAKA;YACLkB,SAASX,UAAUW,OAAO;QAC5B;kBAECI;;IAGL,iFAAiF;IACjF,OAAOgB;AACT;AAEA;;;CAGC,GACD,SAASI,gBAAgB,KAYxB;IAZwB,IAAA,EACvBpE,QAAQ,EACRqE,UAAU,EACVzB,OAAO,EACP0B,aAAa,EACbC,cAAc,EAOf,GAZwB;IAavB,oGAAoG;IACpG,yFAAyF;IACzF,IAAIF,YAAY;QACd,qBACE,qBAACG,eAAQ;YACPC,wBACE;;oBACGH;oBACAC;oBACA3B;;;sBAIJ5C;;IAGP;IAEA,qBAAO;kBAAGA;;AACZ;AAMe,SAAStD,kBAAkB,KAwBzC;IAxByC,IAAA,EACxC+E,iBAAiB,EACjBxB,WAAW,EACXlC,KAAK,EACL2G,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,cAAc,EACdC,MAAM,EAaP,GAxByC;IAyBxC,MAAM7D,UAAUC,IAAAA,iBAAU,EAAC4C,kDAAmB;IAC9C,IAAI,CAAC7C,SAAS;QACZ,MAAM,IAAIG,MAAM;IAClB;IAEA,MAAM,EAAEI,UAAU,EAAEC,IAAI,EAAEF,GAAG,EAAEkB,OAAO,EAAE,GAAGxB;IAE3C,4CAA4C;IAC5C,IAAI8D,8BAA8BvD,WAAWO,GAAG,CAACT;IACjD,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACyD,6BAA6B;QAChCA,8BAA8B,IAAIxC;QAClCf,WAAWkB,GAAG,CAACpB,mBAAmByD;IACpC;IAEA,qCAAqC;IACrC,8IAA8I;IAC9I,MAAMC,cAAcvD,IAAI,CAAC,EAAE,CAACH,kBAAkB,CAAC,EAAE;IAEjD,gIAAgI;IAChI,MAAM2D,2BAA2BC,IAAAA,gCAAe,EAACF;IAEjD;;GAEC,GACD,+DAA+D;IAC/D,MAAMG,oBAA+B;QAACH;KAAY;IAElD,qBACE;;YACGF;YACAK,kBAAkBC,GAAG,CAAC,CAACC;gBACtB,MAAMC,wBAAwBJ,IAAAA,gCAAe,EAACG;gBAC9C,MAAM3D,WAAW6D,IAAAA,0CAAoB,EAACF;gBAEtC,OACE;;;;;;;;UAQA,iBACA,sBAACG,8CAAe,CAACzB,QAAQ;oBAEvBC,qBACE,qBAAChD;wBAAsBlB,aAAaA;kCAClC,cAAA,qBAAC2F,4BAAa;4BACZC,gBAAgB9H;4BAChB2G,aAAaA;4BACbC,cAAcA;sCAEd,cAAA,qBAACP;gCACCC,YAAYyB,QAAQlD;gCACpBA,OAAO,EAAEA,2BAAAA,OAAS,CAAC,EAAE;gCACrB0B,aAAa,EAAE1B,2BAAAA,OAAS,CAAC,EAAE;gCAC3B2B,cAAc,EAAE3B,2BAAAA,OAAS,CAAC,EAAE;0CAE5B,cAAA,qBAACmD,kCAAgB;oCACfhB,UAAUA;oCACVC,gBAAgBA;8CAEhB,cAAA,qBAACgB,kCAAgB;kDACf,cAAA,qBAACxE;4CACCC,mBAAmBA;4CACnBC,KAAKA;4CACLE,MAAMA;4CACND,YAAYuD;4CACZjF,aAAaA;4CACb4B,UAAUA;4CACVoE,UACEb,6BAA6BK;;;;;;;;wBAU5Cb;wBACAC;wBACAC;;mBAvCIY,IAAAA,0CAAoB,EAACF,kBAAkB;YA0ClD;;;AAGN"}