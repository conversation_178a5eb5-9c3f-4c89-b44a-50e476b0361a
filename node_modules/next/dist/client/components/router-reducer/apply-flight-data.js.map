{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-flight-data.ts"], "names": ["applyFlightData", "existingCache", "cache", "flightDataPath", "prefetchEntry", "treePatch", "cacheNodeSeedData", "head", "slice", "length", "rsc", "loading", "prefetchRsc", "fillLazyItemsTillLeafWithHead", "parallelRoutes", "Map", "fillCacheWithNewSubTreeData"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;+CAJ8B;6CACF;AAGrC,SAASA,gBACdC,aAAwB,EACxBC,KAAgB,EAChBC,cAA8B,EAC9BC,aAAkC;IAElC,0DAA0D;IAC1D,MAAM,CAACC,WAAWC,mBAAmBC,KAAK,GAAGJ,eAAeK,KAAK,CAAC,CAAC;IAEnE,8FAA8F;IAC9F,IAAIF,sBAAsB,MAAM;QAC9B,OAAO;IACT;IAEA,IAAIH,eAAeM,MAAM,KAAK,GAAG;QAC/B,MAAMC,MAAMJ,iBAAiB,CAAC,EAAE;QAChC,MAAMK,UAAUL,iBAAiB,CAAC,EAAE;QACpCJ,MAAMS,OAAO,GAAGA;QAChBT,MAAMQ,GAAG,GAAGA;QACZ,kEAAkE;QAClE,oEAAoE;QACpE,2DAA2D;QAC3D,kEAAkE;QAClE,+BAA+B;QAC/BR,MAAMU,WAAW,GAAG;QACpBC,IAAAA,4DAA6B,EAC3BX,OACAD,eACAI,WACAC,mBACAC,MACAH;IAEJ,OAAO;QACL,2CAA2C;QAC3CF,MAAMQ,GAAG,GAAGT,cAAcS,GAAG;QAC7B,oEAAoE;QACpE,kEAAkE;QAClE,2BAA2B;QAC3BR,MAAMU,WAAW,GAAGX,cAAcW,WAAW;QAC7CV,MAAMY,cAAc,GAAG,IAAIC,IAAId,cAAca,cAAc;QAC3DZ,MAAMS,OAAO,GAAGV,cAAcU,OAAO;QACrC,4DAA4D;QAC5DK,IAAAA,wDAA2B,EACzBd,OACAD,eACAE,gBACAC;IAEJ;IAEA,OAAO;AACT"}