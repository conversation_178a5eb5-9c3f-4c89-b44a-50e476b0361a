{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-initial-router-state.ts"], "names": ["createInitialRouterState", "buildId", "initialTree", "initialSeedData", "initialCanonicalUrl", "initialParallelRoutes", "location", "initialHead", "couldBeIntercepted", "isServer", "rsc", "cache", "lazyData", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "lazyDataResolved", "loading", "canonicalUrl", "createHrefFromUrl", "addRefreshMarkerToActiveParallelSegments", "prefetchCache", "size", "fillLazyItemsTillLeafWithHead", "undefined", "extractPathFromFlightRouterState", "initialState", "tree", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "onlyHashChange", "hashFragment", "segmentPaths", "nextUrl", "pathname", "url", "URL", "search", "origin", "initialFlightData", "createPrefetchCacheEntryForInitialLoad", "kind", "PrefetchKind", "AUTO", "data"], "mappings": ";;;;+BA0BgBA;;;eAAAA;;;mCAlBkB;+CACY;oCACG;oCACM;oCACD;iDACG;AAalD,SAASA,yBAAyB,KASV;IATU,IAAA,EACvCC,OAAO,EACPC,WAAW,EACXC,eAAe,EACfC,mBAAmB,EACnBC,qBAAqB,EACrBC,QAAQ,EACRC,WAAW,EACXC,kBAAkB,EACW,GATU;IAUvC,MAAMC,WAAW,CAACH;IAClB,MAAMI,MAAMP,eAAe,CAAC,EAAE;IAE9B,MAAMQ,QAAmB;QACvBC,UAAU;QACVF,KAAKA;QACLG,aAAa;QACbC,MAAM;QACNC,cAAc;QACd,oJAAoJ;QACpJC,gBAAgBP,WAAW,IAAIQ,QAAQZ;QACvCa,kBAAkB;QAClBC,SAAShB,eAAe,CAAC,EAAE;IAC7B;IAEA,MAAMiB,eACJ,6EAA6E;IAC7E,kJAAkJ;IAClJd,WAEIe,IAAAA,oCAAiB,EAACf,YAClBF;IAENkB,IAAAA,yEAAwC,EAACpB,aAAakB;IAEtD,MAAMG,gBAAgB,IAAIN;IAE1B,yEAAyE;IACzE,IAAIZ,0BAA0B,QAAQA,sBAAsBmB,IAAI,KAAK,GAAG;QACtEC,IAAAA,4DAA6B,EAC3Bd,OACAe,WACAxB,aACAC,iBACAI;IAEJ;QAsBI,sEAAsE;IACrEoB;IArBL,MAAMC,eAAe;QACnB3B;QACA4B,MAAM3B;QACNS;QACAY;QACAO,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,mEAAmE;YACnE,gFAAgF;YAChFC,4BAA4B;QAC9B;QACAC,mBAAmB;YACjBC,OAAO;YACPC,gBAAgB;YAChBC,cAAc;YACdC,cAAc,EAAE;QAClB;QACAlB;QACAmB,SAEE,CAACZ,OAAAA,IAAAA,oDAAgC,EAACzB,iBAAgBI,4BAAAA,SAAUkC,QAAQ,aAAnEb,OACD;IACJ;IAEA,IAAIrB,UAAU;QACZ,iDAAiD;QACjD,gFAAgF;QAChF,+FAA+F;QAC/F,MAAMmC,MAAM,IAAIC,IACd,AAAC,KAAEpC,SAASkC,QAAQ,GAAGlC,SAASqC,MAAM,EACtCrC,SAASsC,MAAM;QAGjB,MAAMC,oBAAgC;YAAC;gBAAC;gBAAI3C;gBAAa;gBAAM;aAAK;SAAC;QACrE4C,IAAAA,0DAAsC,EAAC;YACrCL;YACAM,MAAMC,gCAAY,CAACC,IAAI;YACvBC,MAAM;gBAACL;gBAAmBnB;gBAAW;gBAAOlB;aAAmB;YAC/DqB,MAAMD,aAAaC,IAAI;YACvBN,eAAeK,aAAaL,aAAa;YACzCgB,SAASX,aAAaW,OAAO;QAC/B;IACF;IAEA,OAAOX;AACT"}