"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    useReducerWithReduxDevtools: null,
    useUnwrapState: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    useReducerWithReduxDevtools: function() {
        return useReducerWithReduxDevtools;
    },
    useUnwrapState: function() {
        return useUnwrapState;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _routerreducertypes = require("./router-reducer/router-reducer-types");
const _actionqueue = require("../../shared/lib/router/action-queue");
function normalizeRouterState(val) {
    if (val instanceof Map) {
        const obj = {};
        for (const [key, value] of val.entries()){
            if (typeof value === "function") {
                obj[key] = "fn()";
                continue;
            }
            if (typeof value === "object" && value !== null) {
                if (value.$$typeof) {
                    obj[key] = value.$$typeof.toString();
                    continue;
                }
                if (value._bundlerConfig) {
                    obj[key] = "FlightData";
                    continue;
                }
            }
            obj[key] = normalizeRouterState(value);
        }
        return obj;
    }
    if (typeof val === "object" && val !== null) {
        const obj = {};
        for(const key in val){
            const value = val[key];
            if (typeof value === "function") {
                obj[key] = "fn()";
                continue;
            }
            if (typeof value === "object" && value !== null) {
                if (value.$$typeof) {
                    obj[key] = value.$$typeof.toString();
                    continue;
                }
                if (value.hasOwnProperty("_bundlerConfig")) {
                    obj[key] = "FlightData";
                    continue;
                }
            }
            obj[key] = normalizeRouterState(value);
        }
        return obj;
    }
    if (Array.isArray(val)) {
        return val.map(normalizeRouterState);
    }
    return val;
}
function useUnwrapState(state) {
    // reducer actions can be async, so sometimes we need to suspend until the state is resolved
    if ((0, _routerreducertypes.isThenable)(state)) {
        const result = (0, _react.use)(state);
        return result;
    }
    return state;
}
function useReducerWithReduxDevtoolsNoop(initialState) {
    return [
        initialState,
        ()=>{},
        ()=>{}
    ];
}
function useReducerWithReduxDevtoolsImpl(initialState) {
    const [state, setState] = _react.default.useState(initialState);
    const actionQueue = (0, _react.useContext)(_actionqueue.ActionQueueContext);
    if (!actionQueue) {
        throw new Error("Invariant: Missing ActionQueueContext");
    }
    const devtoolsConnectionRef = (0, _react.useRef)();
    const enabledRef = (0, _react.useRef)();
    (0, _react.useEffect)(()=>{
        if (devtoolsConnectionRef.current || enabledRef.current === false) {
            return;
        }
        if (enabledRef.current === undefined && typeof window.__REDUX_DEVTOOLS_EXTENSION__ === "undefined") {
            enabledRef.current = false;
            return;
        }
        devtoolsConnectionRef.current = window.__REDUX_DEVTOOLS_EXTENSION__.connect({
            instanceId: 8000,
            name: "next-router"
        });
        if (devtoolsConnectionRef.current) {
            devtoolsConnectionRef.current.init(normalizeRouterState(initialState));
            if (actionQueue) {
                actionQueue.devToolsInstance = devtoolsConnectionRef.current;
            }
        }
        return ()=>{
            devtoolsConnectionRef.current = undefined;
        };
    }, [
        initialState,
        actionQueue
    ]);
    const dispatch = (0, _react.useCallback)((action)=>{
        if (!actionQueue.state) {
            // we lazy initialize the mutable action queue state since the data needed
            // to generate the state is not available when the actionQueue context is created
            actionQueue.state = initialState;
        }
        actionQueue.dispatch(action, setState);
    }, [
        actionQueue,
        initialState
    ]);
    // Sync is called after a state update in the HistoryUpdater,
    // for debugging purposes. Since the reducer state may be a Promise,
    // we let the app router use() it and sync on the resolved value if
    // something changed.
    // Using the `state` here would be referentially unstable and cause
    // undesirable re-renders and history updates.
    const sync = (0, _react.useCallback)((resolvedState)=>{
        if (devtoolsConnectionRef.current) {
            devtoolsConnectionRef.current.send({
                type: "RENDER_SYNC"
            }, normalizeRouterState(resolvedState));
        }
    }, []);
    return [
        state,
        dispatch,
        sync
    ];
}
const useReducerWithReduxDevtools = typeof window !== "undefined" ? useReducerWithReduxDevtoolsImpl : useReducerWithReduxDevtoolsNoop;

if ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {
  Object.defineProperty(exports.default, '__esModule', { value: true });
  Object.assign(exports.default, exports);
  module.exports = exports.default;
}

//# sourceMappingURL=use-reducer-with-devtools.js.map