{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/middleware.ts"], "names": ["createOriginalStackFrame", "getOverlayMiddleware", "getServerError", "getSourceById", "parseStack", "getModuleId", "compilation", "module", "chunkGraph", "getModuleById", "id", "modules", "find", "searchModule", "findModuleNotFoundFromError", "errorMessage", "match", "getModuleSource", "codeGenerationResults", "get", "sources", "getSourcePath", "source", "replace", "findOriginalSourcePositionAndContent", "webpackSource", "position", "consumer", "SourceMapConsumer", "map", "sourcePosition", "originalPositionFor", "line", "column", "sourceContent", "sourceContentFor", "destroy", "findOriginalSourcePositionAndContentFromCompilation", "moduleId", "importedModule", "buildInfo", "importLocByPath", "modulePath", "rootDirectory", "frame", "lineNumber", "moduleNotFound", "result", "filePath", "path", "resolve", "includes", "traced", "file", "relative", "methodName", "name", "arguments", "originalStackFrame", "originalCodeFrame", "getOriginalCodeFrame", "sourcePackage", "findSourcePackage", "isFile", "fileContent", "fs", "readFile", "catch", "getRawSourceMap", "moduleSource", "err", "console", "error", "options", "req", "res", "next", "pathname", "searchParams", "URL", "url", "parseInt", "getAll", "filter", "Boolean", "isServer", "isEdgeServer", "isAppDirectory", "isClient", "test", "json", "badRequest", "startsWith", "stats", "serverStats", "edgeServerStats", "log", "internalServerError", "noContent", "originalStackFrameResponse", "fileExists", "access", "FS", "F_OK", "then", "launchEditor"], "mappings": ";;;;;;;;;;;;;;;;;;IA2FsBA,wBAAwB;eAAxBA;;IAiHNC,oBAAoB;eAApBA;;IA7LPC,cAAc;eAAdA,+BAAc;;IAqJDC,aAAa;eAAbA;;IApJbC,UAAU;eAAVA,sBAAU;;;;oBAhB6B;+DAC/B;6BACiB;iCAEF;8BACH;wBAStB;iCACwB;4BACJ;AAO3B,SAASC,YAAYC,WAAgB,EAAEC,OAAW;IAChD,OAAOD,YAAYE,UAAU,CAACH,WAAW,CAACE;AAC5C;AAEA,SAASE,cACPC,EAAsB,EACtBJ,WAAgC;IAEhC,OAAO;WAAIA,YAAYK,OAAO;KAAC,CAACC,IAAI,CAClC,CAACC,eAAiBR,YAAYC,aAAaO,kBAAkBH;AAEjE;AAEA,SAASI,4BAA4BC,YAAgC;QAC5DA;IAAP,OAAOA,iCAAAA,sBAAAA,aAAcC,KAAK,CAAC,wCAApBD,mBAAyC,CAAC,EAAE;AACrD;AAEA,SAASE,gBAAgBX,WAAgB,EAAEC,OAAW;QAGlDD;IAFF,IAAI,CAACC,SAAQ,OAAO;QAElBD;IADF,OACEA,CAAAA,sDAAAA,yCAAAA,YAAYY,qBAAqB,CAACC,GAAG,CAACZ,6BAAtCD,uCAA+Cc,OAAO,CAACD,GAAG,CAAC,yBAA3Db,qDACA;AAEJ;AAEA,SAASe,cAAcC,MAAc;IACnC,OAAOA,OAAOC,OAAO,CAAC,qDAAqD;AAC7E;AAEA,eAAeC,qCACbC,aAAkB,EAClBC,QAAiD;IAEjD,MAAMC,WAAW,MAAM,IAAIC,8BAAiB,CAACH,cAAcI,GAAG;IAC9D,IAAI;YAGQH;QAFV,MAAMI,iBAAiBH,SAASI,mBAAmB,CAAC;YAClDC,MAAMN,SAASM,IAAI;YACnBC,QAAQP,CAAAA,mBAAAA,SAASO,MAAM,YAAfP,mBAAmB;QAC7B;QAEA,IAAI,CAACI,eAAeR,MAAM,EAAE;YAC1B,OAAO;QACT;YAGEK;QADF,MAAMO,gBACJP,CAAAA,6BAAAA,SAASQ,gBAAgB,CACvBL,eAAeR,MAAM,EACrB,uBAAuB,GAAG,iBAF5BK,6BAGK;QAEP,OAAO;YACLG;YACAI;QACF;IACF,SAAU;QACRP,SAASS,OAAO;IAClB;AACF;AAEA,SAASC,oDACPC,QAA4B,EAC5BC,cAAsB,EACtBjC,WAAgC;QAGzBC,mCAAAA;IADP,MAAMA,UAASE,cAAc6B,UAAUhC;QAChCC;IAAP,OAAOA,CAAAA,wCAAAA,4BAAAA,oBAAAA,QAAQiC,SAAS,sBAAjBjC,oCAAAA,kBAAmBkC,eAAe,qBAAlClC,kCAAoCY,GAAG,CAACoB,2BAAxChC,wCAA2D;AACpE;AAEO,eAAeP,yBAAyB,KAgB9C;IAhB8C,IAAA,EAC7CsB,MAAM,EACNgB,QAAQ,EACRI,UAAU,EACVC,aAAa,EACbC,KAAK,EACL7B,YAAY,EACZT,WAAW,EASZ,GAhB8C;QA0DzC,gHAAgH;IAChH,kGAAkG;IAClGsC,2BAAAA;IA3CJ,MAAM,EAAEC,UAAU,EAAEZ,MAAM,EAAE,GAAGW;IAC/B,MAAME,iBAAiBhC,4BAA4BC;IACnD,MAAMgC,SAAS,MAAM,AAAC,CAAA;QACpB,IAAID,gBAAgB;YAClB,IAAI,CAACxC,aAAa,OAAO;YAEzB,OAAO+B,oDACLC,UACAQ,gBACAxC;QAEJ;QACA,iDAAiD;QACjD,OAAO,MAAMkB,qCAAqCF,QAAQ;YACxDU,MAAMa,qBAAAA,aAAc;YACpBZ;QACF;IACF,CAAA;IAEA,IAAI,EAACc,0BAAAA,OAAQjB,cAAc,CAACR,MAAM,GAAE,OAAO;IAE3C,MAAM,EAAEQ,cAAc,EAAEI,aAAa,EAAE,GAAGa;IAE1C,MAAMC,WAAWC,aAAI,CAACC,OAAO,CAC3BP,eACAtB,cAEE,AADA,oFAAoF;IACnFS,CAAAA,eAAeR,MAAM,CAAC6B,QAAQ,CAAC,OAC5BT,aACAZ,eAAeR,MAAM,AAAD,KAAMoB;QASvBZ;IALX,MAAMsB,SAAS;QACbC,MAAMnB,gBACFe,aAAI,CAACK,QAAQ,CAACX,eAAeK,YAC7BlB,eAAeR,MAAM;QACzBuB,YAAYf,eAAeE,IAAI;QAC/BC,QAAQ,AAACH,CAAAA,CAAAA,yBAAAA,eAAeG,MAAM,YAArBH,yBAAyB,CAAA,IAAK;QACvCyB,YACEzB,eAAe0B,IAAI,MAGnBZ,oBAAAA,MAAMW,UAAU,sBAAhBX,4BAAAA,kBACIrB,OAAO,CAAC,8BAA8B,+BAD1CqB,0BAEIrB,OAAO,CAAC,wBAAwB;QACtCkC,WAAW,EAAE;IACf;IAEA,OAAO;QACLC,oBAAoBN;QACpBO,mBAAmBC,IAAAA,4BAAoB,EAACR,QAAQlB;QAChD2B,eAAeC,IAAAA,yBAAiB,EAACV;IACnC;AACF;AAEO,eAAejD,cACpB4D,MAAe,EACfrD,EAAU,EACVJ,WAAiC;IAEjC,IAAIyD,QAAQ;QACV,MAAMC,cAA6B,MAAMC,YAAE,CACxCC,QAAQ,CAACxD,IAAI,SACbyD,KAAK,CAAC,IAAM;QAEf,IAAIH,eAAe,MAAM;YACvB,OAAO;QACT;QAEA,MAAMnC,MAAMuC,IAAAA,gCAAe,EAACJ;QAC5B,IAAInC,OAAO,MAAM;YACf,OAAO;QACT;QAEA,OAAO;YACLA;gBACE,OAAOA;YACT;QACF;IACF;IAEA,IAAI;QACF,IAAI,CAACvB,aAAa;YAChB,OAAO;QACT;QAEA,MAAMC,UAASE,cAAcC,IAAIJ;QACjC,MAAM+D,eAAepD,gBAAgBX,aAAaC;QAClD,OAAO8D;IACT,EAAE,OAAOC,KAAK;QACZC,QAAQC,KAAK,CAAC,AAAC,qCAAkC9D,KAAG,OAAM4D;QAC1D,OAAO;IACT;AACF;AAEO,SAASrE,qBAAqBwE,OAKpC;IACC,OAAO,eACLC,GAAoB,EACpBC,GAAmB,EACnBC,IAAc;QAEd,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUL,IAAIM,GAAG;YAKtCF,mBACJA;QAJnB,MAAMlC,QAAQ;YACZS,MAAMyB,aAAa3D,GAAG,CAAC;YACvBoC,YAAYuB,aAAa3D,GAAG,CAAC;YAC7B0B,YAAYoC,SAASH,CAAAA,oBAAAA,aAAa3D,GAAG,CAAC,yBAAjB2D,oBAAkC,KAAK,OAAO;YACnE7C,QAAQgD,SAASH,CAAAA,qBAAAA,aAAa3D,GAAG,CAAC,qBAAjB2D,qBAA8B,KAAK,OAAO;YAC3DrB,WAAWqB,aAAaI,MAAM,CAAC,aAAaC,MAAM,CAACC;QACrD;QAEA,MAAMC,WAAWP,aAAa3D,GAAG,CAAC,gBAAgB;QAClD,MAAMmE,eAAeR,aAAa3D,GAAG,CAAC,oBAAoB;QAC1D,MAAMoE,iBAAiBT,aAAa3D,GAAG,CAAC,sBAAsB;QAE9D,IAAI0D,aAAa,kCAAkC;YACjD,MAAMW,WAAW,CAACH,YAAY,CAACC;YAE/B,IAAIzB,gBAAgBC,IAAAA,yBAAiB,EAAClB;YAEtC,IACE,CACE,CAAA,iDAAiD6C,IAAI,CAAC7C,MAAMS,IAAI,KAChET,MAAMC,UAAU,AAAD,GAEjB;gBACA,IAAIgB,eAAe,OAAO6B,IAAAA,YAAI,EAACf,KAAK;oBAAEd;gBAAc;gBACpD,OAAO8B,IAAAA,kBAAU,EAAChB;YACpB;YAEA,MAAMrC,WAAmBM,MAAMS,IAAI,CAAC9B,OAAO,CACzC,8DACA;YAEF,MAAMmB,aAAaE,MAAMS,IAAI,CAAC9B,OAAO,CACnC,yEACA;YAGF,IAAID,SAAiB;YAErB,IAAIhB;YAEJ,MAAMyD,SAASnB,MAAMS,IAAI,CAACuC,UAAU,CAAC;YAErC,IAAI;gBACF,IAAIJ,YAAYD,gBAAgB;wBAChBd;oBAAdnE,eAAcmE,iBAAAA,QAAQoB,KAAK,uBAAbpB,eAAiBnE,WAAW;oBAC1C,+BAA+B;oBAC/B,kDAAkD;oBAClD,oJAAoJ;oBACpJgB,SAAS,MAAMnB,cAAc4D,QAAQzB,UAAUhC;gBACjD;gBACA,yBAAyB;gBACzB,yHAAyH;gBACzH,oKAAoK;gBACpK,IAAI,AAAC+E,CAAAA,YAAYE,cAAa,KAAMjE,WAAW,MAAM;wBACrCmD;oBAAdnE,eAAcmE,uBAAAA,QAAQqB,WAAW,uBAAnBrB,qBAAuBnE,WAAW;oBAChDgB,SAAS,MAAMnB,cAAc4D,QAAQzB,UAAUhC;gBACjD;gBACA,8BAA8B;gBAC9B,uHAAuH;gBACvH,IAAI,AAACgF,CAAAA,gBAAgBC,cAAa,KAAMjE,WAAW,MAAM;wBACzCmD;oBAAdnE,eAAcmE,2BAAAA,QAAQsB,eAAe,uBAAvBtB,yBAA2BnE,WAAW;oBACpDgB,SAAS,MAAMnB,cAAc4D,QAAQzB,UAAUhC;gBACjD;YACF,EAAE,OAAOgE,KAAK;gBACZC,QAAQyB,GAAG,CAAC,6BAA6B1B;gBACzC,OAAO2B,IAAAA,2BAAmB,EAACtB;YAC7B;YAEA,IAAI,CAACrD,QAAQ;gBACX,IAAIuC,eAAe,OAAO6B,IAAAA,YAAI,EAACf,KAAK;oBAAEd;gBAAc;gBACpD,OAAOqC,IAAAA,iBAAS,EAACvB;YACnB;YAEA,IAAI;gBACF,MAAMwB,6BAA6B,MAAMnG,yBAAyB;oBAChE4C;oBACAtB;oBACAgB;oBACAI;oBACAC,eAAe8B,QAAQ9B,aAAa;oBACpCrC;gBACF;gBAEA,IAAI6F,+BAA+B,MAAM;oBACvC,IAAItC,eAAe,OAAO6B,IAAAA,YAAI,EAACf,KAAK;wBAAEd;oBAAc;oBACpD,OAAOqC,IAAAA,iBAAS,EAACvB;gBACnB;gBAEA,OAAOe,IAAAA,YAAI,EAACf,KAAKwB;YACnB,EAAE,OAAO7B,KAAK;gBACZC,QAAQyB,GAAG,CAAC,+BAA+B1B;gBAC3C,OAAO2B,IAAAA,2BAAmB,EAACtB;YAC7B;QACF,OAAO,IAAIE,aAAa,2BAA2B;YACjD,IAAI,CAACjC,MAAMS,IAAI,EAAE,OAAOsC,IAAAA,kBAAU,EAAChB;YAEnC,kFAAkF;YAClF,MAAM3B,WAAWC,aAAI,CAACC,OAAO,CAC3BuB,QAAQ9B,aAAa,EACrBC,MAAMS,IAAI,CAAC9B,OAAO,CAAC,gBAAgB;YAErC,MAAM6E,aAAa,MAAMnC,YAAE,CAACoC,MAAM,CAACrD,UAAUsD,aAAE,CAACC,IAAI,EAAEC,IAAI,CACxD,IAAM,MACN,IAAM;YAER,IAAI,CAACJ,YAAY,OAAOF,IAAAA,iBAAS,EAACvB;YAElC,IAAI;oBAC6C/B;gBAA/C,MAAM6D,IAAAA,0BAAY,EAACzD,UAAUJ,MAAMC,UAAU,EAAED,CAAAA,gBAAAA,MAAMX,MAAM,YAAZW,gBAAgB;YACjE,EAAE,OAAO0B,KAAK;gBACZC,QAAQyB,GAAG,CAAC,4BAA4B1B;gBACxC,OAAO2B,IAAAA,2BAAmB,EAACtB;YAC7B;YAEA,OAAOuB,IAAAA,iBAAS,EAACvB;QACnB;QACA,OAAOC;IACT;AACF"}