{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Terminal/Terminal.tsx"], "names": ["Terminal", "getFile", "lines", "contentFileName", "shift", "fileName", "line", "column", "split", "parsedLine", "Number", "parsedColumn", "hasLocation", "isNaN", "location", "undefined", "getImportTraceFiles", "some", "test", "files", "length", "includes", "file", "pop", "trim", "unshift", "getEditorLinks", "content", "importTraceFiles", "source", "join", "React", "useMemo", "decoded", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "div", "data-nextjs-terminal", "EditorLink", "isSourceFile", "pre", "map", "entry", "index", "span", "style", "color", "fg", "decoration", "fontWeight", "fontStyle", "HotlinkedText", "text", "importTraceFile"], "mappings": ";;;;+BAwDaA;;;eAAAA;;;;;;gEAxDK;iEACK;+BACO;4BACH;AAI3B,SAASC,QAAQC,KAAe;IAC9B,MAAMC,kBAAkBD,MAAME,KAAK;IACnC,IAAI,CAACD,iBAAiB,OAAO;IAC7B,MAAM,CAACE,UAAUC,MAAMC,OAAO,GAAGJ,gBAAgBK,KAAK,CAAC,KAAK;IAE5D,MAAMC,aAAaC,OAAOJ;IAC1B,MAAMK,eAAeD,OAAOH;IAC5B,MAAMK,cAAc,CAACF,OAAOG,KAAK,CAACJ,eAAe,CAACC,OAAOG,KAAK,CAACF;IAE/D,OAAO;QACLN,UAAUO,cAAcP,WAAWF;QACnCW,UAAUF,cACN;YACEN,MAAMG;YACNF,QAAQI;QACV,IACAI;IACN;AACF;AAEA,SAASC,oBAAoBd,KAAe;IAC1C,IACEA,MAAMe,IAAI,CAAC,CAACX,OAAS,8BAA8BY,IAAI,CAACZ,UACxDJ,MAAMe,IAAI,CAAC,CAACX,OAAS,qCAAqCY,IAAI,CAACZ,QAC/D;QACA,iDAAiD;QACjD,MAAMa,QAAQ,EAAE;QAChB,MACE,SAASD,IAAI,CAAChB,KAAK,CAACA,MAAMkB,MAAM,GAAG,EAAE,KACrC,CAAClB,KAAK,CAACA,MAAMkB,MAAM,GAAG,EAAE,CAACC,QAAQ,CAAC,KAClC;YACA,MAAMC,OAAOpB,MAAMqB,GAAG,GAAIC,IAAI;YAC9BL,MAAMM,OAAO,CAACH;QAChB;QAEA,OAAOH;IACT;IAEA,OAAO,EAAE;AACX;AAEA,SAASO,eAAeC,OAAe;IACrC,MAAMzB,QAAQyB,QAAQnB,KAAK,CAAC;IAC5B,MAAMc,OAAOrB,QAAQC;IACrB,MAAM0B,mBAAmBZ,oBAAoBd;IAE7C,OAAO;QAAEoB;QAAMO,QAAQ3B,MAAM4B,IAAI,CAAC;QAAOF;IAAiB;AAC5D;AAEO,MAAM5B,WAAoC,SAASA,SAAS,KAElE;IAFkE,IAAA,EACjE2B,OAAO,EACR,GAFkE;IAGjE,MAAM,EAAEL,IAAI,EAAEO,MAAM,EAAED,gBAAgB,EAAE,GAAGG,OAAMC,OAAO,CACtD,IAAMN,eAAeC,UACrB;QAACA;KAAQ;IAGX,MAAMM,UAAUF,OAAMC,OAAO,CAAC;QAC5B,OAAOE,cAAK,CAACC,UAAU,CAACN,QAAQ;YAC9BO,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAACT;KAAO;IAEX,qBACE,sBAACU;QAAIC,sBAAoB;;YACtBlB,sBACC,qBAACmB,sBAAU;gBACTC,YAAY;gBAEZpB,MAAMA,KAAKjB,QAAQ;gBACnBS,UAAUQ,KAAKR,QAAQ;eAFlBQ,KAAKjB,QAAQ;0BAKtB,sBAACsC;;oBACEV,QAAQW,GAAG,CAAC,CAACC,OAAOC,sBACnB,qBAACC;4BAECC,OAAO;gCACLC,OAAOJ,MAAMK,EAAE,GAAG,AAAC,iBAAcL,MAAMK,EAAE,GAAC,MAAKnC;gCAC/C,GAAI8B,MAAMM,UAAU,KAAK,SACrB;oCAAEC,YAAY;gCAAI,IAClBP,MAAMM,UAAU,KAAK,WACrB;oCAAEE,WAAW;gCAAS,IACtBtC,SAAS;4BACf;sCAEA,cAAA,qBAACuC,4BAAa;gCAACC,MAAMV,MAAMlB,OAAO;;2BAV7B,AAAC,oBAAiBmB;oBAa1BlB,iBAAiBgB,GAAG,CAAC,CAACY,gCACrB,qBAACf,sBAAU;4BACTC,cAAc;4BAEdpB,MAAMkC;2BADDA;;;;;AAOjB"}