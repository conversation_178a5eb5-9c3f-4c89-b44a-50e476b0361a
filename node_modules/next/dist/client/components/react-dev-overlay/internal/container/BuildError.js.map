{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/BuildError.tsx"], "names": ["BuildError", "styles", "message", "versionInfo", "noop", "React", "useCallback", "Overlay", "fixed", "Dialog", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "className", "h1", "id", "p", "VersionStalenessInfo", "DialogBody", "Terminal", "content", "footer", "small", "css"], "mappings": ";;;;;;;;;;;;;;;IAeaA,UAAU;eAAVA;;IAyCAC,MAAM;eAANA;;;;;;iEAxDU;wBAOhB;yBACiB;0BACC;sCACY;8BACT;;;;;;;;;;AAIrB,MAAMD,aAAwC,SAASA,WAAW,KAGxE;IAHwE,IAAA,EACvEE,OAAO,EACPC,WAAW,EACZ,GAHwE;IAIvE,MAAMC,OAAOC,OAAMC,WAAW,CAAC,KAAO,GAAG,EAAE;IAC3C,qBACE,qBAACC,gBAAO;QAACC,KAAK;kBACZ,cAAA,qBAACC,cAAM;YACLC,MAAK;YACLC,mBAAgB;YAChBC,oBAAiB;YACjBC,SAAST;sBAET,cAAA,sBAACU,qBAAa;;kCACZ,sBAACC,oBAAY;wBAACC,WAAU;;0CACtB,qBAACC;gCAAGC,IAAG;0CAAkC;;0CACzC,qBAACC;gCACCD,IAAG;gCACHF,WAAU;0CACX;;4BAGAb,4BAAc,qBAACiB,0CAAoB;gCAAE,GAAGjB,WAAW;iCAAO;;;kCAE7D,sBAACkB,kBAAU;wBAACL,WAAU;;0CACpB,qBAACM,kBAAQ;gCAACC,SAASrB;;0CACnB,qBAACsB;0CACC,cAAA,qBAACL;oCAAED,IAAG;8CACJ,cAAA,qBAACO;kDAAM;;;;;;;;;;AAWvB;AAEO,MAAMxB,aAASyB,kBAAG"}