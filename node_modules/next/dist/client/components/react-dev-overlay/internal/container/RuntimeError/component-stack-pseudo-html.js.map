{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.tsx"], "names": ["PseudoHtmlDiff", "getAdjacentProps", "isAdj", "componentStackFrames", "firstContent", "second<PERSON><PERSON>nt", "hydrationMismatchType", "props", "isHtmlTagsWarning", "MAX_NON_COLLAPSED_FRAMES", "shouldCollapse", "length", "isHtmlCollapsed", "toggleCollapseHtml", "useState", "htmlComponents", "useMemo", "tagNames", "replace", "nestedHtmlStack", "lastText", "componentStack", "map", "frame", "component", "reverse", "matchedIndex", "i", "for<PERSON>ach", "index", "componentList", "spaces", "repeat", "isHighlightedTag", "includes", "isAdjacentTag", "Math", "abs", "isLastFewFrames", "adjProps", "codeLine", "span", "undefined", "wrappedCodeLine", "Fragment", "data-nextjs-container-errors-pseudo-html--hint", "push", "key", "data-nextjs-container-errors-pseudo-html--diff-remove", "data-nextjs-container-errors-pseudo-html--diff-add", "data-nextjs-container-errors-pseudo-html--tag-adjacent", "div", "data-nextjs-container-errors-pseudo-html", "button", "tabIndex", "data-nextjs-container-errors-pseudo-html-collapse", "onClick", "CollapseIcon", "collapsed", "pre", "code"], "mappings": ";;;;+BAwDgBA;;;eAAAA;;;;uBAxD4B;8BAEf;AAE7B,SAASC,iBAAiBC,KAAc;IACtC,OAAO;QAAE,0DAA0DA;IAAM;AAC3E;AAkDO,SAASF,eAAe,KAWS;IAXT,IAAA,EAC7BG,oBAAoB,EACpBC,YAAY,EACZC,aAAa,EACbC,qBAAqB,EACrB,GAAGC,OAMmC,GAXT;IAY7B,MAAMC,oBAAoBF,0BAA0B;IACpD,+FAA+F;IAC/F,MAAMG,2BAA2BD,oBAAoB,IAAI;IACzD,MAAME,iBAAiBP,qBAAqBQ,MAAM,GAAGF;IACrD,MAAM,CAACG,iBAAiBC,mBAAmB,GAAGC,IAAAA,eAAQ,EAACJ;IAEvD,MAAMK,iBAAiBC,IAAAA,cAAO,EAAC;QAC7B,MAAMC,WAAWT,oBAEb;YAACJ,aAAac,OAAO,CAAC,QAAQ;YAAKb,cAAca,OAAO,CAAC,QAAQ;SAAI,GACrE,EAAE;QACN,MAAMC,kBAAqC,EAAE;QAC7C,IAAIC,WAAW;QAEf,MAAMC,iBAAiBlB,qBACpBmB,GAAG,CAAC,CAACC,QAAUA,MAAMC,SAAS,EAC9BC,OAAO;QAEV,8BAA8B;QAC9B,MAAMC,eAAe;YAAC,CAAC;YAAG,CAAC;SAAE;QAC7B,IAAIlB,mBAAmB;YACrB,mCAAmC;YACnC,IAAK,IAAImB,IAAIN,eAAeV,MAAM,GAAG,GAAGgB,KAAK,GAAGA,IAAK;gBACnD,IAAIN,cAAc,CAACM,EAAE,KAAKV,QAAQ,CAAC,EAAE,EAAE;oBACrCS,YAAY,CAAC,EAAE,GAAGC;oBAClB;gBACF;YACF;YACA,kDAAkD;YAClD,IAAK,IAAIA,IAAID,YAAY,CAAC,EAAE,GAAG,GAAGC,KAAK,GAAGA,IAAK;gBAC7C,IAAIN,cAAc,CAACM,EAAE,KAAKV,QAAQ,CAAC,EAAE,EAAE;oBACrCS,YAAY,CAAC,EAAE,GAAGC;oBAClB;gBACF;YACF;QACF;QAEAN,eAAeO,OAAO,CAAC,CAACJ,WAAWK,OAAOC;YACxC,MAAMC,SAAS,IAAIC,MAAM,CAACb,gBAAgBR,MAAM,GAAG;YACnD,iDAAiD;YACjD,iDAAiD;YACjD,gEAAgE;YAEhE,MAAMsB,mBAAmBzB,oBACrBqB,UAAUH,YAAY,CAAC,EAAE,IAAIG,UAAUH,YAAY,CAAC,EAAE,GACtDT,SAASiB,QAAQ,CAACV;YACtB,MAAMW,gBACJF,oBACAG,KAAKC,GAAG,CAACR,QAAQH,YAAY,CAAC,EAAE,KAAK,KACrCU,KAAKC,GAAG,CAACR,QAAQH,YAAY,CAAC,EAAE,KAAK;YAEvC,MAAMY,kBACJ,CAAC9B,qBAAqBqB,SAASC,cAAcnB,MAAM,GAAG;YAExD,MAAM4B,WAAWtC,iBAAiBkC;YAElC,IAAI,AAAC3B,qBAAqB2B,iBAAkBG,iBAAiB;gBAC3D,MAAME,yBACJ,sBAACC;;wBACEV;sCACD,qBAACU;4BACE,GAAGF,QAAQ;4BAEV,GAAIN,mBACA;gCACE,uDACE;4BACJ,IACAS,SAAS;sCAGd,AAAC,MAAGlB,YAAU;;;;gBAIrBJ,WAAWI;gBAEX,MAAMmB,gCACJ,sBAACC,eAAQ;;wBACNJ;wBAEAP,kCACC,qBAACQ;4BAAKI,gDAA8C;sCACjDd,SAAS,IAAIC,MAAM,CAACR,UAAUb,MAAM,GAAG,KAAK;;;mBALpCQ,gBAAgBR,MAAM;gBAUvCQ,gBAAgB2B,IAAI,CAACH;YACvB,OAAO;gBACL,IACExB,gBAAgBR,MAAM,IAAIF,4BAC1BG,iBACA;oBACA;gBACF;gBAEA,IAAI,CAACA,mBAAmB0B,iBAAiB;oBACvCnB,gBAAgB2B,IAAI,eAClB,0BAACL;wBAAM,GAAGF,QAAQ;wBAAEQ,KAAK5B,gBAAgBR,MAAM;;4BAC5CoB;4BACA,MAAMP,YAAY;;;gBAGzB,OAAO,IAAIZ,mBAAmBQ,aAAa,OAAO;oBAChDA,WAAW;oBACXD,gBAAgB2B,IAAI,eAClB,0BAACL;wBAAM,GAAGF,QAAQ;wBAAEQ,KAAK5B,gBAAgBR,MAAM;;4BAC5CoB;4BACA;;;gBAGP;YACF;QACF;QAEA,uCAAuC;QACvC,IAAI,CAACvB,mBAAmB;YACtB,MAAMuB,SAAS,IAAIC,MAAM,CAACb,gBAAgBR,MAAM,GAAG;YACnD,IAAIgC;YACJ,IAAIrC,0BAA0B,QAAQ;gBACpC,uEAAuE;gBACvEqC,gCACE,sBAACC,eAAQ;;sCACP,qBAACH;4BAAKO,uDAAqD;sCACxDjB,SAAS,CAAA,AAAC,MAAG3B,eAAa,KAAG;;sCAEhC,qBAACqC;4BAAKQ,oDAAkD;sCACrDlB,SAAS,CAAA,AAAC,MAAG1B,gBAAc,KAAG;;;mBALpBc,gBAAgBR,MAAM;YASzC,OAAO;gBACL,4EAA4E;gBAC5EgC,gCACE,sBAACC,eAAQ;;sCACP,qBAACH;4BAAKS,wDAAsD;sCACzDnB,SAAS,CAAA,AAAC,MAAG1B,gBAAc,KAAG;;sCAEjC,qBAACoC;4BAAKO,uDAAqD;sCACxDjB,SAAS,CAAA,AAAC,QAAK3B,eAAa,KAAG;;;mBALrBe,gBAAgBR,MAAM;YASzC;YACAQ,gBAAgB2B,IAAI,CAACH;QACvB;QAEA,OAAOxB;IACT,GAAG;QACDhB;QACAS;QACAR;QACAC;QACAG;QACAF;QACAG;KACD;IAED,qBACE,sBAAC0C;QAAIC,0CAAwC;;0BAC3C,qBAACC;gBACCC,UAAU;gBACVC,mDAAiD;gBACjDC,SAAS,IAAM3C,mBAAmB,CAACD;0BAEnC,cAAA,qBAAC6C,0BAAY;oBAACC,WAAW9C;;;0BAE3B,qBAAC+C;gBAAK,GAAGpD,KAAK;0BACZ,cAAA,qBAACqD;8BAAM7C;;;;;AAIf"}