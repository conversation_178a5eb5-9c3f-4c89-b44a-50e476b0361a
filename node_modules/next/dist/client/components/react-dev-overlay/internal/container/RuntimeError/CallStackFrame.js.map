{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.tsx"], "names": ["CallStackFrame", "frame", "f", "originalStackFrame", "sourceStackFrame", "hasSource", "Boolean", "originalCodeFrame", "open", "useOpenInEditor", "file", "lineNumber", "column", "undefined", "div", "data-nextjs-call-stack-frame", "h3", "data-nextjs-frame-expanded", "expanded", "HotlinkedText", "text", "methodName", "data-has-source", "tabIndex", "role", "onClick", "title", "span", "getFrameSource", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "line", "x1", "y1", "x2", "y2"], "mappings": ";;;;+BAQaA;;;eAAAA;;;;4BAJN;iCACyB;+BACF;AAEvB,MAAMA,iBAER,SAASA,eAAe,KAAS;IAAT,IAAA,EAAEC,KAAK,EAAE,GAAT;QAILA;IAHtB,0CAA0C;IAC1C,2CAA2C;IAE3C,MAAMC,IAAgBD,CAAAA,4BAAAA,MAAME,kBAAkB,YAAxBF,4BAA4BA,MAAMG,gBAAgB;IACxE,MAAMC,YAAYC,QAAQL,MAAMM,iBAAiB;IACjD,MAAMC,OAAOC,IAAAA,gCAAe,EAC1BJ,YACI;QACEK,MAAMR,EAAEQ,IAAI;QACZC,YAAYT,EAAES,UAAU;QACxBC,QAAQV,EAAEU,MAAM;IAClB,IACAC;IAGN,qBACE,sBAACC;QAAIC,8BAA4B;;0BAC/B,qBAACC;gBAAGC,8BAA4BX,QAAQL,MAAMiB,QAAQ;0BACpD,cAAA,qBAACC,4BAAa;oBAACC,MAAMlB,EAAEmB,UAAU;;;0BAEnC,sBAACP;gBACCQ,mBAAiBjB,YAAY,SAASQ;gBACtCU,UAAUlB,YAAY,KAAKQ;gBAC3BW,MAAMnB,YAAY,SAASQ;gBAC3BY,SAASjB;gBACTkB,OAAOrB,YAAY,iCAAiCQ;;kCAEpD,qBAACc;kCAAMC,IAAAA,0BAAc,EAAC1B;;kCACtB,sBAAC2B;wBACCC,OAAM;wBACNC,SAAQ;wBACRC,MAAK;wBACLC,QAAO;wBACPC,aAAY;wBACZC,eAAc;wBACdC,gBAAe;;0CAEf,qBAACC;gCAAKC,GAAE;;0CACR,qBAACC;gCAASC,QAAO;;0CACjB,qBAACC;gCAAKC,IAAG;gCAAKC,IAAG;gCAAKC,IAAG;gCAAKC,IAAG;;;;;;;;AAK3C"}