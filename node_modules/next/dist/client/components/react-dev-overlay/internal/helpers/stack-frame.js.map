{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/stack-frame.ts"], "names": ["getFrameSource", "getOriginalStackFrames", "getOriginalStackFrame", "source", "type", "isAppDir", "errorMessage", "_getOriginalStackFrame", "body", "params", "URLSearchParams", "append", "String", "key", "toString", "controller", "AbortController", "tm", "setTimeout", "abort", "res", "self", "fetch", "process", "env", "__NEXT_ROUTER_BASEPATH", "signal", "finally", "clearTimeout", "ok", "status", "Promise", "reject", "Error", "text", "json", "error", "reason", "external", "expanded", "Boolean", "file", "includes", "originalStackFrame", "startsWith", "sourceStackFrame", "originalCodeFrame", "sourcePackage", "match", "resolve", "catch", "err", "message", "frames", "all", "map", "frame", "webpackRegExes", "isWebpackBundled", "some", "regEx", "test", "formatFrameSourceFile", "regex", "replace", "str", "globalThis", "u", "URL", "location", "origin", "protocol", "pathname", "lineNumber", "column", "slice"], "mappings": ";;;;;;;;;;;;;;;IA6HgBA,cAAc;eAAdA;;IAlCAC,sBAAsB;eAAtBA;;;AAhFhB,SAASC,sBACPC,MAAkB,EAClBC,IAAqC,EACrCC,QAAiB,EACjBC,YAAoB;QAiDlBH,cACAA;IAhDF,eAAeI;YAgCT,aAAa,GACZJ,cACCK,+BAAAA,0BACAA,gCAAAA;QAlCN,MAAMC,SAAS,IAAIC;QACnBD,OAAOE,MAAM,CAAC,YAAYC,OAAOR,SAAS;QAC1CK,OAAOE,MAAM,CAAC,gBAAgBC,OAAOR,SAAS;QAC9CK,OAAOE,MAAM,CAAC,kBAAkBC,OAAOP;QACvCI,OAAOE,MAAM,CAAC,gBAAgBL;QAC9B,IAAK,MAAMO,OAAOV,OAAQ;gBACJ;YAApBM,OAAOE,MAAM,CAACE,KAAK,AAAC,CAAA,CAAA,cAAA,AAACV,MAAc,CAACU,IAAI,YAApB,cAAwB,EAAC,EAAGC,QAAQ;QAC1D;QAEA,MAAMC,aAAa,IAAIC;QACvB,MAAMC,KAAKC,WAAW,IAAMH,WAAWI,KAAK,IAAI;QAChD,MAAMC,MAAM,MAAMC,KACfC,KAAK,CACJ,AACEC,CAAAA,QAAQC,GAAG,CAACC,sBAAsB,IAAI,EAAC,IACxC,oCAAiChB,OAAOK,QAAQ,IACjD;YAAEY,QAAQX,WAAWW,MAAM;QAAC,GAE7BC,OAAO,CAAC;YACPC,aAAaX;QACf;QACF,IAAI,CAACG,IAAIS,EAAE,IAAIT,IAAIU,MAAM,KAAK,KAAK;YACjC,OAAOC,QAAQC,MAAM,CAAC,IAAIC,MAAM,MAAMb,IAAIc,IAAI;QAChD;QAEA,MAAM1B,OAAmC,MAAMY,IAAIe,IAAI;YAOlDhC;QANL,OAAO;YACLiC,OAAO;YACPC,QAAQ;YACRC,UAAU;YACVC,UAAU,CAACC,QAET,CAACrC,OAAAA,EAAAA,eAAAA,OAAOsC,IAAI,qBAAXtC,aAAauC,QAAQ,CAAC,sBACrBlC,2BAAAA,KAAKmC,kBAAkB,sBAAvBnC,gCAAAA,yBAAyBiC,IAAI,qBAA7BjC,8BAA+BkC,QAAQ,CAAC,sBACxClC,4BAAAA,KAAKmC,kBAAkB,sBAAvBnC,iCAAAA,0BAAyBiC,IAAI,qBAA7BjC,+BAA+BoC,UAAU,CAAC,4BAF3CzC,OAGC;YAEJ0C,kBAAkB1C;YAClBwC,oBAAoBnC,KAAKmC,kBAAkB;YAC3CG,mBAAmBtC,KAAKsC,iBAAiB,IAAI;YAC7CC,eAAevC,KAAKuC,aAAa;QACnC;IACF;IAEA,IACE5C,OAAOsC,IAAI,KAAK,mBAChBtC,eAAAA,OAAOsC,IAAI,qBAAXtC,aAAa6C,KAAK,CAAC,gBACnB7C,gBAAAA,OAAOsC,IAAI,qBAAXtC,cAAa6C,KAAK,CAAC,iBACnB;QACA,OAAOjB,QAAQkB,OAAO,CAAC;YACrBb,OAAO;YACPC,QAAQ;YACRC,UAAU;YACVC,UAAU;YACVM,kBAAkB1C;YAClBwC,oBAAoB;YACpBG,mBAAmB;YACnBC,eAAe;QACjB;IACF;IAEA,OAAOxC,yBAAyB2C,KAAK,CAAC,CAACC;YAE7BA,cAAAA;eAF6C;YACrDf,OAAO;YACPC,QAAQc,CAAAA,OAAAA,CAAAA,eAAAA,uBAAAA,IAAKC,OAAO,YAAZD,eAAgBA,uBAAAA,IAAKrC,QAAQ,cAA7BqC,OAAmC;YAC3Cb,UAAU;YACVC,UAAU;YACVM,kBAAkB1C;YAClBwC,oBAAoB;YACpBG,mBAAmB;YACnBC,eAAe;QACjB;IAAA;AACF;AAEO,SAAS9C,uBACdoD,MAAoB,EACpBjD,IAAqC,EACrCC,QAAiB,EACjBC,YAAoB;IAEpB,OAAOyB,QAAQuB,GAAG,CAChBD,OAAOE,GAAG,CAAC,CAACC,QACVtD,sBAAsBsD,OAAOpD,MAAMC,UAAUC;AAGnD;AAEA,MAAMmD,iBAAiB;IACrB;IACA;CACD;AAED,SAASC,iBAAiBjB,IAAY;IACpC,OAAOgB,eAAeE,IAAI,CAAC,CAACC,QAAUA,MAAMC,IAAI,CAACpB;AACnD;AAEA;;;;;;CAMC,GACD,SAASqB,sBAAsBrB,IAAY;IACzC,KAAK,MAAMsB,SAASN,eAAgBhB,OAAOA,KAAKuB,OAAO,CAACD,OAAO;IAC/D,OAAOtB;AACT;AAEO,SAASzC,eAAewD,KAAiB;IAC9C,IAAI,CAACA,MAAMf,IAAI,EAAE,OAAO;IAExB,IAAIwB,MAAM;IACV,IAAI;YAIEC;QAHJ,MAAMC,IAAI,IAAIC,IAAIZ,MAAMf,IAAI;QAE5B,4CAA4C;QAC5C,IAAIyB,EAAAA,uBAAAA,WAAWG,QAAQ,qBAAnBH,qBAAqBI,MAAM,MAAKH,EAAEG,MAAM,EAAE;YAC5C,gEAAgE;YAChE,8CAA8C;YAC9C,IAAIH,EAAEG,MAAM,KAAK,QAAQ;gBACvBL,OAAOE,EAAEI,QAAQ;YACnB,OAAO;gBACLN,OAAOE,EAAEG,MAAM;YACjB;QACF;QAEA,qEAAqE;QACrE,cAAc;QACdL,OAAOE,EAAEK,QAAQ;QACjBP,OAAO;QACPA,MAAMH,sBAAsBG;IAC9B,EAAE,UAAM;QACNA,OAAOH,sBAAsBN,MAAMf,IAAI,IAAI,eAAe;IAC5D;IAEA,IAAI,CAACiB,iBAAiBF,MAAMf,IAAI,KAAKe,MAAMiB,UAAU,IAAI,MAAM;QAC7D,IAAIjB,MAAMkB,MAAM,IAAI,MAAM;YACxBT,OAAO,AAAC,MAAGT,MAAMiB,UAAU,GAAC,MAAGjB,MAAMkB,MAAM,GAAC;QAC9C,OAAO;YACLT,OAAO,AAAC,MAAGT,MAAMiB,UAAU,GAAC;QAC9B;IACF;IACA,OAAOR,IAAIU,KAAK,CAAC,GAAG,CAAC;AACvB"}