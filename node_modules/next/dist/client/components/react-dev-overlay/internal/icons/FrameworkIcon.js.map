{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/icons/FrameworkIcon.tsx"], "names": ["FrameworkIcon", "framework", "svg", "data-nextjs-call-stack-framework-icon", "xmlns", "width", "height", "viewBox", "fill", "shapeRendering", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "path", "d", "mask", "id", "maskUnits", "x", "y", "circle", "cx", "cy", "r", "g", "rect", "defs", "linearGradient", "x1", "y1", "x2", "y2", "gradientUnits", "stop", "stopColor", "offset", "stopOpacity"], "mappings": ";;;;+<PERSON>Eg<PERSON>;;;eAAAA;;;;AAAT,SAASA,cAAc,KAI7B;IAJ6B,IAAA,EAC5BC,SAAS,EAGV,GAJ6B;IAK5B,IAAIA,cAAc,SAAS;QACzB,qBACE,sBAACC;YACCC,yCAAsC;YACtCC,OAAM;YACNC,OAAM;YACNC,QAAO;YACPC,SAAQ;YACRC,MAAK;YACLC,gBAAe;YACfC,QAAO;YACPC,eAAc;YACdC,gBAAe;YACfC,aAAY;;8BAEZ,qBAACC;oBACCC,GAAE;oBACFP,MAAK;;8BAEP,qBAACM;oBACCC,GAAE;oBACFP,MAAK;;;;IAIb;IAEA,qBACE,sBAACN;QACCC,yCAAsC;QACtCC,OAAM;QACNC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;;0BA<PERSON>,qBAACQ;gBACCC,IAAG;gBACHC,WAAU;gBACVC,GAAE;gBACFC,GAAE;gBACFf,OAAM;gBACNC,QAAO;0BAEP,cAAA,qBAACe;oBAAOC,IAAG;oBAAKC,IAAG;oBAAKC,GAAE;oBAAKhB,MAAK;;;0BAEtC,sBAACiB;gBAAET,MAAK;;kCACN,qBAACK;wBACCC,IAAG;wBACHC,IAAG;wBACHC,GAAE;wBACFhB,MAAK;wBACLE,QAAO;wBACPG,aAAY;;kCAEd,qBAACC;wBACCC,GAAE;wBACFP,MAAK;;kCAEP,qBAACkB;wBACCP,GAAE;wBACFC,GAAE;wBACFf,OAAM;wBACNC,QAAO;wBACPE,MAAK;;;;0BAGT,sBAACmB;;kCACC,sBAACC;wBACCX,IAAG;wBACHY,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,qBAACC;gCAAKC,WAAU;;0CAChB,qBAACD;gCAAKE,QAAO;gCAAID,WAAU;gCAAQE,aAAY;;;;kCAEjD,sBAACT;wBACCX,IAAG;wBACHY,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,qBAACC;gCAAKC,WAAU;;0CAChB,qBAACD;gCAAKE,QAAO;gCAAID,WAAU;gCAAQE,aAAY;;;;;;;;AAKzD"}