{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/icons/CloseIcon.tsx"], "names": ["CloseIcon", "svg", "width", "height", "viewBox", "fill", "xmlns", "path", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin"], "mappings": ";;;;+BA6BSA;;;eAAAA;;;;;iEA7Bc;AAEvB,MAAMA,YAAY;IAChB,qBACE,sBAACC;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;;0BAEN,qBAACC;gBACCC,GAAE;gBACFC,QAAO;gBACPC,aAAY;gBACZC,eAAc;gBACdC,gBAAe;;0BAEjB,qBAACL;gBACCC,GAAE;gBACFC,QAAO;gBACPC,aAAY;gBACZC,eAAc;gBACdC,gBAAe;;;;AAIvB"}