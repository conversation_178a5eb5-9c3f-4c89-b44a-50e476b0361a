{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/hooks/use-on-click-outside.ts"], "names": ["useOnClickOutside", "el", "handler", "React", "useEffect", "listener", "e", "contains", "target", "root", "getRootNode", "addEventListener", "removeEventListener"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;;iEAFO;AAEhB,SAASA,kBACdC,EAAe,EACfC,OAA2D;IAE3DC,OAAMC,SAAS,CAAC;QACd,IAAIH,MAAM,QAAQC,WAAW,MAAM;YACjC;QACF;QAEA,MAAMG,WAAW,CAACC;YAChB,8DAA8D;YAC9D,IAAI,CAACL,MAAMA,GAAGM,QAAQ,CAACD,EAAEE,MAAM,GAAc;gBAC3C;YACF;YAEAN,QAAQI;QACV;QAEA,MAAMG,OAAOR,GAAGS,WAAW;QAC3BD,KAAKE,gBAAgB,CAAC,aAAaN;QACnCI,KAAKE,gBAAgB,CAAC,cAAcN;QACpC,OAAO;YACLI,KAAKG,mBAAmB,CAAC,aAAaP;YACtCI,KAAKG,mBAAmB,CAAC,cAAcP;QACzC;IACF,GAAG;QAACH;QAASD;KAAG;AAClB"}