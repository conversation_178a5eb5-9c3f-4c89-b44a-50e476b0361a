{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/ReactDevOverlay.tsx"], "names": ["ReactDevOverlay", "React", "PureComponent", "getDerivedStateFromError", "error", "stack", "reactError", "id", "event", "type", "ACTION_UNHANDLED_ERROR", "reason", "frames", "parseStack", "componentDidCatch", "componentErr", "props", "onReactError", "render", "state", "children", "hasBuildError", "buildError", "hasRuntimeErrors", "Boolean", "errors", "length", "hasMissingTags", "rootLayoutMissingTags", "isMounted", "html", "head", "body", "ShadowPort<PERSON>", "CssReset", "Base", "ComponentStyles", "RootLayoutMissingTagsError", "missingTags", "BuildError", "message", "versionInfo", "Errors", "isAppDir", "initialDisplayState", "undefined"], "mappings": ";;;;;;;eAgBqBA;;;;;iEAhBE;wBACmC;8BAE7B;4BACF;wBACJ;4BAEI;sBACN;iCACW;0BACP;4CACkB;AAK5B,MAAMA,wBAAwBC,OAAMC,aAAa;IAU9D,OAAOC,yBAAyBC,KAAY,EAAwB;QAClE,IAAI,CAACA,MAAMC,KAAK,EAAE,OAAO;YAAEC,YAAY;QAAK;QAC5C,OAAO;YACLA,YAAY;gBACVC,IAAI;gBACJC,OAAO;oBACLC,MAAMC,8BAAsB;oBAC5BC,QAAQP;oBACRQ,QAAQC,IAAAA,sBAAU,EAACT,MAAMC,KAAK;gBAChC;YACF;QACF;IACF;IAEAS,kBAAkBC,YAAmB,EAAE;QACrC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACF;IAC1B;IAEAG,SAAS;YAMwBC,8BAmBtBA;QAxBT,MAAM,EAAEA,KAAK,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAACJ,KAAK;QACtC,MAAM,EAAEV,UAAU,EAAE,GAAG,IAAI,CAACa,KAAK;QAEjC,MAAME,gBAAgBF,MAAMG,UAAU,IAAI;QAC1C,MAAMC,mBAAmBC,QAAQL,MAAMM,MAAM,CAACC,MAAM;QACpD,MAAMC,iBAAiBH,SAAQL,+BAAAA,MAAMS,qBAAqB,qBAA3BT,6BAA6BO,MAAM;QAClE,MAAMG,YACJR,iBAAiBE,oBAAoBjB,cAAcqB;QAErD,qBACE;;gBACGrB,2BACC,sBAACwB;;sCACC,qBAACC;sCACD,qBAACC;;qBAGHZ;gBAEDS,0BACC,sBAACI,0BAAY;;sCACX,qBAACC,kBAAQ;sCACT,qBAACC,UAAI;sCACL,qBAACC,gCAAe;wBACfjB,EAAAA,gCAAAA,MAAMS,qBAAqB,qBAA3BT,8BAA6BO,MAAM,kBAClC,qBAACW,sDAA0B;4BACzBC,aAAanB,MAAMS,qBAAqB;6BAExCP,8BACF,qBAACkB,sBAAU;4BACTC,SAASrB,MAAMG,UAAU;4BACzBmB,aAAatB,MAAMsB,WAAW;6BAE9BnC,2BACF,qBAACoC,cAAM;4BACLC,UAAU;4BACVF,aAAatB,MAAMsB,WAAW;4BAC9BG,qBAAoB;4BACpBnB,QAAQ;gCAACnB;6BAAW;6BAEpBiB,iCACF,qBAACmB,cAAM;4BACLC,UAAU;4BACVC,qBAAoB;4BACpBnB,QAAQN,MAAMM,MAAM;4BACpBgB,aAAatB,MAAMsB,WAAW;6BAE9BI;;qBAEJA;;;IAGV;;;aAzEA1B,QAAQ;YAAEb,YAAY;QAAK;;AA0E7B"}