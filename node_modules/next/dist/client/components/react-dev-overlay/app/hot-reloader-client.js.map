{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/hot-reloader-client.tsx"], "names": ["HotReload", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "startLatency", "onBeforeFastRefresh", "dispatcher", "hasUpdates", "onBeforeRefresh", "onFastRefresh", "sendMessage", "updatedModules", "onBuildOk", "reportHmrLatency", "onRefresh", "endLatency", "latency", "console", "log", "JSON", "stringify", "event", "id", "window", "startTime", "endTime", "page", "location", "pathname", "isPageHidden", "document", "visibilityState", "handleAvailableHash", "hash", "isUpdateAvailable", "process", "env", "TURBOPACK", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "stackTrace", "stack", "split", "slice", "join", "message", "hadRuntimeError", "RuntimeError<PERSON>andler", "dependency<PERSON><PERSON>n", "undefined", "reload", "tryApplyUpdates", "onBeforeUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "warn", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "Boolean", "length", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "apply", "processMessage", "obj", "processTurbopackMessage", "router", "handleErrors", "errors", "formatted", "formatWebpackMessages", "warnings", "onBuildError", "i", "error", "stripAnsi", "handleHotUpdate", "onBeforeHotUpdate", "onSuccessfulHotUpdate", "webpackUpdatedModules", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "BUILDING", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "hasErrors", "errorCount", "clientId", "hasWarnings", "warningCount", "formattedMessages", "TURBOPACK_CONNECTED", "type", "TURBOPACK_MESSAGE", "extractModulesFromTurbopackMessage", "data", "SERVER_COMPONENT_CHANGES", "startTransition", "fastRefresh", "RELOAD_PAGE", "ADDED_PAGE", "REMOVED_PAGE", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "assetPrefix", "children", "state", "dispatch", "useErrorOverlayReducer", "useMemo", "ACTION_BUILD_OK", "ACTION_BUILD_ERROR", "ACTION_BEFORE_REFRESH", "ACTION_REFRESH", "ACTION_VERSION_INFO", "handleOnUnhandledError", "useCallback", "errorDetails", "details", "componentStack", "warning", "ACTION_UNHANDLED_ERROR", "reason", "frames", "parseStack", "componentStackFrames", "parseComponentStack", "handleOnUnhandledRejection", "ACTION_UNHANDLED_REJECTION", "handleOnReactError", "useErrorHandler", "webSocketRef", "useWebsocket", "useWebsocketPing", "useSendMessage", "useTurbopack", "useRouter", "useEffect", "websocket", "current", "addEventListener", "removeEventListener", "ReactDevOverlay", "onReactError"], "mappings": ";;;;+BAkcA;;;eAAwBA;;;;;uBAjcyC;oEAC3C;gFACY;4BACR;wBAUnB;4BACoB;0EACC;iCACI;qCACI;8BAM7B;qCAC6B;kCAEQ;oDAKO;AAWnD,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAChB,IAAIC,eAA8B;AAElC,SAASC,oBAAoBC,UAAsB,EAAEC,UAAmB;IACtE,IAAIA,YAAY;QACdD,WAAWE,eAAe;IAC5B;AACF;AAEA,SAASC,cACPH,UAAsB,EACtBI,WAAsC,EACtCC,cAAqC;IAErCL,WAAWM,SAAS;IAEpBC,iBAAiBH,aAAaC;IAE9BL,WAAWQ,SAAS;AACtB;AAEA,SAASD,iBACPH,WAAsC,EACtCC,cAAqC;IAErC,IAAI,CAACP,cAAc;IACnB,IAAIW,aAAad,KAAKC,GAAG;IACzB,MAAMc,UAAUD,aAAaX;IAC7Ba,QAAQC,GAAG,CAAC,AAAC,4BAAyBF,UAAQ;IAC9CN,YACES,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPC,IAAIC,OAAO1B,iBAAiB;QAC5B2B,WAAWpB;QACXqB,SAASV;QACTW,MAAMH,OAAOI,QAAQ,CAACC,QAAQ;QAC9BjB;QACA,oEAAoE;QACpE,sDAAsD;QACtDkB,cAAcC,SAASC,eAAe,KAAK;IAC7C;AAEJ;AAEA,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCrC,4BAA4BqC;AAC9B;AAEA;;;;CAIC,GACD,SAASC;IACP,IAAIC,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,OAAO;IACT;IAEA,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOzC,8BAA8B0C;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,qCAAqC;IACrC,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrB,qCAAqC;gBACrCF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACA,qCAAqC;QACrCJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEvC,WAAgB;IACnD,MAAMwC,aACJD,OACC,CAAA,AAACA,IAAIE,KAAK,IAAIF,IAAIE,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDL,IAAIM,OAAO,IACXN,MAAM,EAAC;IAEXvC,YACES,KAAKC,SAAS,CAAC;QACbC,OAAO;QACP6B;QACAM,iBAAiB,CAAC,CAACC,wCAAmB,CAACD,eAAe;QACtDE,iBAAiBT,MAAMA,IAAIS,eAAe,GAAGC;IAC/C;IAGF,IAAIxD,WAAW;IACfA,YAAY;IACZoB,OAAOI,QAAQ,CAACiC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,gBACPC,cAA6C,EAC7CC,kBAAsD,EACtDrD,WAAgB,EAChBJ,UAAsB;IAEtB,IAAI,CAAC4B,uBAAuB,CAACK,mBAAmB;QAC9CjC,WAAWM,SAAS;QACpB;IACF;IAEA,SAASoD,mBAAmBf,GAAQ,EAAEtC,cAA+B;QACnE,IAAIsC,OAAOQ,wCAAmB,CAACD,eAAe,IAAI,CAAC7C,gBAAgB;YACjE,IAAIsC,KAAK;gBACPhC,QAAQgD,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAIR,wCAAmB,CAACD,eAAe,EAAE;gBAC9CvC,QAAQgD,IAAI,CAACC,4CAAoC;YACnD;YACAlB,kBAAkBC,KAAKvC;YACvB;QACF;QAEA,MAAMH,aAAa4D,QAAQxD,eAAeyD,MAAM;QAChD,IAAI,OAAOL,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmBpD;QACrB;QAEA,IAAIuB,qBAAqB;YACvB,+DAA+D;YAC/D2B,gBACEtD,aAAa,KAAO,IAAIuD,gBACxBvD,aAAa,IAAMD,WAAWM,SAAS,KAAKmD,oBAC5CrD,aACAJ;QAEJ,OAAO;YACLA,WAAWM,SAAS;YACpB,IAAIuB,QAAQC,GAAG,CAACiC,gBAAgB,EAAE;gBAChC1B,kBAAkB;oBAChB,IAAI2B,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,qCAAqC;IACrC/B,OAAOC,GAAG,CACP+B,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAAC9D;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAOmD,mBAAmB,YAAY;YACxC,MAAMvD,aAAa4D,QAAQxD,eAAeyD,MAAM;YAChDN,eAAevD;QACjB;QACA,2DAA2D;QAC3D,qCAAqC;QACrC,OAAOiC,OAAOC,GAAG,CAACiC,KAAK;IACzB,GACCD,IAAI,CACH,CAAC9D;QACCqD,mBAAmB,MAAMrD;IAC3B,GACA,CAACsC;QACCe,mBAAmBf,KAAK;IAC1B;AAEN;AAEA,yDAAyD,GACzD,SAAS0B,eACPC,GAAqB,EACrBlE,WAAsC,EACtCmE,uBAA6D,EAC7DC,MAAoC,EACpCxE,UAAsB;IAEtB,IAAI,CAAE,CAAA,YAAYsE,GAAE,GAAI;QACtB;IACF;IAEA,SAASG,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAYC,IAAAA,8BAAqB,EAAC;YACtCF,QAAQA;YACRG,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B7E,WAAW8E,YAAY,CAACH,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAIK,IAAI,GAAGA,IAAIJ,UAAUD,MAAM,CAACZ,MAAM,EAAEiB,IAAK;YAChDpE,QAAQqE,KAAK,CAACC,IAAAA,kBAAS,EAACN,UAAUD,MAAM,CAACK,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIlD,QAAQC,GAAG,CAACiC,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACU,UAAUD,MAAM,CAAC,EAAE;gBACtCV,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,SAASiB;QACP,IAAIrD,QAAQC,GAAG,CAACC,SAAS,EAAE;YACzB/B,WAAWM,SAAS;QACtB,OAAO;YACLiD,gBACE,SAAS4B,kBAAkBlF,UAAmB;gBAC5CF,oBAAoBC,YAAYC;YAClC,GACA,SAASmF,sBAAsBC,qBAA+B;gBAC5D,qDAAqD;gBACrD,sDAAsD;gBACtDlF,cAAcH,YAAYI,aAAaiF;YACzC,GACAjF,aACAJ;QAEJ;IACF;IAEA,OAAQsE,IAAIgB,MAAM;QAChB,KAAKC,6CAA2B,CAACC,QAAQ;YAAE;gBACzC1F,eAAeH,KAAKC,GAAG;gBACvBe,QAAQC,GAAG,CAAC;gBACZ;YACF;QACA,KAAK2E,6CAA2B,CAACE,KAAK;QACtC,KAAKF,6CAA2B,CAACG,IAAI;YAAE;gBACrC,IAAIpB,IAAI3C,IAAI,EAAE;oBACZD,oBAAoB4C,IAAI3C,IAAI;gBAC9B;gBAEA,MAAM,EAAE+C,MAAM,EAAEG,QAAQ,EAAE,GAAGP;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKtE,WAAW2F,aAAa,CAACrB,IAAIsB,WAAW;gBAElE,MAAMC,YAAYhC,QAAQa,UAAUA,OAAOZ,MAAM;gBACjD,kEAAkE;gBAClE,IAAI+B,WAAW;oBACbzF,YACES,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP+E,YAAYpB,OAAOZ,MAAM;wBACzBiC,UAAUxG;oBACZ;oBAGFkF,aAAaC;oBACb;gBACF;gBAEA,MAAMsB,cAAcnC,QAAQgB,YAAYA,SAASf,MAAM;gBACvD,IAAIkC,aAAa;oBACf5F,YACES,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPkF,cAAcpB,SAASf,MAAM;wBAC7BiC,UAAUxG;oBACZ;oBAGF,iCAAiC;oBACjC,MAAM2G,oBAAoBtB,IAAAA,8BAAqB,EAAC;wBAC9CC,UAAUA;wBACVH,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAIK,IAAI,GAAGA,IAAImB,kBAAkBrB,QAAQ,CAACf,MAAM,EAAEiB,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXpE,QAAQgD,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACAhD,QAAQgD,IAAI,CAACsB,IAAAA,kBAAS,EAACiB,kBAAkBrB,QAAQ,CAACE,EAAE;oBACtD;gBAEA,uHAAuH;gBACzH;gBAEA3E,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPgF,UAAUxG;gBACZ;gBAGF,IAAI+E,IAAIgB,MAAM,KAAKC,6CAA2B,CAACE,KAAK,EAAE;oBACpD,qBAAqB;oBACrBP;gBACF;gBACA;YACF;QACA,KAAKK,6CAA2B,CAACY,mBAAmB;YAAE;gBACpD5B,wBAAwB;oBACtB6B,MAAMb,6CAA2B,CAACY,mBAAmB;gBACvD;gBACA;YACF;QACA,KAAKZ,6CAA2B,CAACc,iBAAiB;YAAE;gBAClD,MAAMhG,iBAAiBiG,IAAAA,sEAAkC,EAAChC,IAAIiC,IAAI;gBAClEvG,WAAWE,eAAe;gBAC1BqE,wBAAwB;oBACtB6B,MAAMb,6CAA2B,CAACc,iBAAiB;oBACnDE,MAAMjC,IAAIiC,IAAI;gBAChB;gBACAvG,WAAWQ,SAAS;gBACpB,IAAI2C,wCAAmB,CAACD,eAAe,EAAE;oBACvCvC,QAAQgD,IAAI,CAACC,4CAAoC;oBACjDlB,kBAAkB,MAAMtC;gBAC1B;gBACAG,iBAAiBH,aAAaC;gBAC9B;YACF;QACA,uDAAuD;QACvD,KAAKkF,6CAA2B,CAACiB,wBAAwB;YAAE;gBACzDpG,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPgF,UAAUxG;gBACZ;gBAEF,IAAI4D,wCAAmB,CAACD,eAAe,EAAE;oBACvC,IAAIrD,WAAW;oBACfA,YAAY;oBACZ,OAAOoB,OAAOI,QAAQ,CAACiC,MAAM;gBAC/B;gBACAmD,IAAAA,sBAAe,EAAC;oBACdjC,OAAOkC,WAAW;oBAClB1G,WAAWQ,SAAS;gBACtB;gBAEA,IAAIqB,QAAQC,GAAG,CAACiC,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAKsB,6CAA2B,CAACoB,WAAW;YAAE;gBAC5CvG,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPgF,UAAUxG;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAOoB,OAAOI,QAAQ,CAACiC,MAAM;YAC/B;QACA,KAAKiC,6CAA2B,CAACqB,UAAU;QAC3C,KAAKrB,6CAA2B,CAACsB,YAAY;YAAE;gBAC7C,qFAAqF;gBACrF,OAAOrC,OAAOkC,WAAW;YAC3B;QACA,KAAKnB,6CAA2B,CAACuB,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGzC;gBACtB,IAAIyC,WAAW;oBACb,MAAM,EAAE9D,OAAO,EAAEJ,KAAK,EAAE,GAAGhC,KAAKmG,KAAK,CAACD;oBACtC,MAAM/B,QAAQ,IAAIiC,MAAMhE;oBACxB+B,MAAMnC,KAAK,GAAGA;oBACd4B,aAAa;wBAACO;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKO,6CAA2B,CAAC2B,yBAAyB;YAAE;gBAC1D;YACF;QACA;YAAS,CACT;IACF;AACF;AAEe,SAAS7H,UAAU,KAMjC;IANiC,IAAA,EAChC8H,WAAW,EACXC,QAAQ,EAIT,GANiC;IAOhC,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,8BAAsB;IAEhD,MAAMvH,aAAawH,IAAAA,cAAO,EAAa;QACrC,OAAO;YACLlH;gBACEgH,SAAS;oBAAElB,MAAMqB,uBAAe;gBAAC;YACnC;YACA3C,cAAa7B,OAAO;gBAClBqE,SAAS;oBAAElB,MAAMsB,0BAAkB;oBAAEzE;gBAAQ;YAC/C;YACA/C;gBACEoH,SAAS;oBAAElB,MAAMuB,6BAAqB;gBAAC;YACzC;YACAnH;gBACE8G,SAAS;oBAAElB,MAAMwB,sBAAc;gBAAC;YAClC;YACAjC,eAAcC,WAAW;gBACvB0B,SAAS;oBAAElB,MAAMyB,2BAAmB;oBAAEjC;gBAAY;YACpD;QACF;IACF,GAAG;QAAC0B;KAAS;IAEb,MAAMQ,yBAAyBC,IAAAA,kBAAW,EACxC,CAAC/C;QACC,MAAMgD,eAAe,AAAChD,MAAciD,OAAO;QAG3C,kGAAkG;QAClG,MAAMC,iBAAiBF,gCAAAA,aAAcE,cAAc;QACnD,MAAMC,UAAUH,gCAAAA,aAAcG,OAAO;QACrCb,SAAS;YACPlB,MAAMgC,8BAAsB;YAC5BC,QAAQrD;YACRsD,QAAQC,IAAAA,sBAAU,EAACvD,MAAMnC,KAAK;YAC9B2F,sBAAsBN,iBAClBO,IAAAA,wCAAmB,EAACP,kBACpB7E;YACJ8E;QACF;IACF,GACA;QAACb;KAAS;IAEZ,MAAMoB,6BAA6BX,IAAAA,kBAAW,EAC5C,CAACM;QACCf,SAAS;YACPlB,MAAMuC,kCAA0B;YAChCN,QAAQA;YACRC,QAAQC,IAAAA,sBAAU,EAACF,OAAOxF,KAAK;QACjC;IACF,GACA;QAACyE;KAAS;IAEZ,MAAMsB,qBAAqBb,IAAAA,kBAAW,EAAC;QACrC5E,wCAAmB,CAACD,eAAe,GAAG;IACxC,GAAG,EAAE;IACL2F,IAAAA,gCAAe,EAACf,wBAAwBY;IAExC,MAAMI,eAAeC,IAAAA,0BAAY,EAAC5B;IAClC6B,IAAAA,8BAAgB,EAACF;IACjB,MAAM1I,cAAc6I,IAAAA,4BAAc,EAACH;IACnC,MAAMvE,0BAA0B2E,IAAAA,0BAAY,EAAC9I,aAAa,CAACuC,MACzDD,kBAAkBC,KAAKvC;IAGzB,MAAMoE,SAAS2E,IAAAA,qBAAS;IAExBC,IAAAA,gBAAS,EAAC;QACR,MAAMC,YAAYP,aAAaQ,OAAO;QACtC,IAAI,CAACD,WAAW;QAEhB,MAAM9G,UAAU,CAACxB;YACf,IAAI;gBACF,MAAMuD,MAAMzD,KAAKmG,KAAK,CAACjG,MAAMwF,IAAI;gBACjClC,eACEC,KACAlE,aACAmE,yBACAC,QACAxE;YAEJ,EAAE,OAAO2C,KAAU;oBAEkCA;gBADnDhC,QAAQgD,IAAI,CACV,4BAA4B5C,MAAMwF,IAAI,GAAG,OAAQ5D,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKE,KAAK,YAAVF,aAAc,EAAC;YAEpE;QACF;QAEA0G,UAAUE,gBAAgB,CAAC,WAAWhH;QACtC,OAAO,IAAM8G,UAAUG,mBAAmB,CAAC,WAAWjH;IACxD,GAAG;QAACnC;QAAaoE;QAAQsE;QAAc9I;QAAYuE;KAAwB;IAE3E,qBACE,qBAACkF,wBAAe;QAACC,cAAcd;QAAoBvB,OAAOA;kBACvDD;;AAGP"}