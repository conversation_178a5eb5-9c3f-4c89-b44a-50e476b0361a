{"version": 3, "sources": ["../../../src/client/components/redirect-boundary.tsx"], "names": ["RedirectBoundary", "RedirectErrorBoundary", "HandleRedirect", "redirect", "reset", "redirectType", "router", "useRouter", "useEffect", "React", "startTransition", "RedirectType", "push", "replace", "Component", "getDerivedStateFromError", "error", "isRedirectError", "url", "getURLFromRedirectError", "getRedirectTypeFromError", "render", "state", "setState", "props", "children", "constructor"], "mappings": "AAAA;;;;;;;;;;;;;;;;IA6EgBA,gBAAgB;eAAhBA;;IApCHC,qBAAqB;eAArBA;;;;;iEAxCoB;4BAEP;0BAMnB;AAOP,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,QAAQ,EACRC,KAAK,EACLC,YAAY,EAKb,GARuB;IAStB,MAAMC,SAASC,IAAAA,qBAAS;IAExBC,IAAAA,gBAAS,EAAC;QACRC,cAAK,CAACC,eAAe,CAAC;YACpB,IAAIL,iBAAiBM,sBAAY,CAACC,IAAI,EAAE;gBACtCN,OAAOM,IAAI,CAACT,UAAU,CAAC;YACzB,OAAO;gBACLG,OAAOO,OAAO,CAACV,UAAU,CAAC;YAC5B;YACAC;QACF;IACF,GAAG;QAACD;QAAUE;QAAcD;QAAOE;KAAO;IAE1C,OAAO;AACT;AAEO,MAAML,8BAA8BQ,cAAK,CAACK,SAAS;IASxD,OAAOC,yBAAyBC,KAAU,EAAE;QAC1C,IAAIC,IAAAA,yBAAe,EAACD,QAAQ;YAC1B,MAAME,MAAMC,IAAAA,iCAAuB,EAACH;YACpC,MAAMX,eAAee,IAAAA,kCAAwB,EAACJ;YAC9C,OAAO;gBAAEb,UAAUe;gBAAKb;YAAa;QACvC;QACA,wCAAwC;QACxC,MAAMW;IACR;IAEA,0IAA0I;IAC1IK,SAA0B;QACxB,MAAM,EAAElB,QAAQ,EAAEE,YAAY,EAAE,GAAG,IAAI,CAACiB,KAAK;QAC7C,IAAInB,aAAa,QAAQE,iBAAiB,MAAM;YAC9C,qBACE,qBAACH;gBACCC,UAAUA;gBACVE,cAAcA;gBACdD,OAAO,IAAM,IAAI,CAACmB,QAAQ,CAAC;wBAAEpB,UAAU;oBAAK;;QAGlD;QAEA,OAAO,IAAI,CAACqB,KAAK,CAACC,QAAQ;IAC5B;IA7BAC,YAAYF,KAA4B,CAAE;QACxC,KAAK,CAACA;QACN,IAAI,CAACF,KAAK,GAAG;YAAEnB,UAAU;YAAME,cAAc;QAAK;IACpD;AA2BF;AAEO,SAASL,iBAAiB,KAA2C;IAA3C,IAAA,EAAEyB,QAAQ,EAAiC,GAA3C;IAC/B,MAAMnB,SAASC,IAAAA,qBAAS;IACxB,qBACE,qBAACN;QAAsBK,QAAQA;kBAASmB;;AAE5C"}