{"version": 3, "sources": ["../../src/client/use-intersection.tsx"], "names": ["useIntersection", "hasIntersectionObserver", "IntersectionObserver", "observers", "Map", "idList", "createObserver", "options", "id", "root", "margin", "rootMargin", "existing", "find", "obj", "instance", "get", "elements", "observer", "entries", "for<PERSON>ach", "entry", "callback", "target", "isVisible", "isIntersecting", "intersectionRatio", "push", "set", "observe", "element", "unobserve", "delete", "size", "disconnect", "index", "findIndex", "splice", "rootRef", "disabled", "isDisabled", "visible", "setVisible", "useState", "elementRef", "useRef", "setElement", "useCallback", "current", "useEffect", "tagName", "idleCallback", "requestIdleCallback", "cancelIdleCallback", "resetVisible"], "mappings": ";;;;+BA+FgBA;;;eAAAA;;;uBA/FyC;qCAIlD;AAqBP,MAAMC,0BAA0B,OAAOC,yBAAyB;AAEhE,MAAMC,YAAY,IAAIC;AACtB,MAAMC,SAAuB,EAAE;AAE/B,SAASC,eAAeC,OAAoC;IAC1D,MAAMC,KAAK;QACTC,MAAMF,QAAQE,IAAI,IAAI;QACtBC,QAAQH,QAAQI,UAAU,IAAI;IAChC;IACA,MAAMC,WAAWP,OAAOQ,IAAI,CAC1B,CAACC,MAAQA,IAAIL,IAAI,KAAKD,GAAGC,IAAI,IAAIK,IAAIJ,MAAM,KAAKF,GAAGE,MAAM;IAE3D,IAAIK;IAEJ,IAAIH,UAAU;QACZG,WAAWZ,UAAUa,GAAG,CAACJ;QACzB,IAAIG,UAAU;YACZ,OAAOA;QACT;IACF;IAEA,MAAME,WAAW,IAAIb;IACrB,MAAMc,WAAW,IAAIhB,qBAAqB,CAACiB;QACzCA,QAAQC,OAAO,CAAC,CAACC;YACf,MAAMC,WAAWL,SAASD,GAAG,CAACK,MAAME,MAAM;YAC1C,MAAMC,YAAYH,MAAMI,cAAc,IAAIJ,MAAMK,iBAAiB,GAAG;YACpE,IAAIJ,YAAYE,WAAW;gBACzBF,SAASE;YACX;QACF;IACF,GAAGjB;IACHQ,WAAW;QACTP;QACAU;QACAD;IACF;IAEAZ,OAAOsB,IAAI,CAACnB;IACZL,UAAUyB,GAAG,CAACpB,IAAIO;IAClB,OAAOA;AACT;AAEA,SAASc,QACPC,OAAgB,EAChBR,QAAyB,EACzBf,OAAoC;IAEpC,MAAM,EAAEC,EAAE,EAAEU,QAAQ,EAAED,QAAQ,EAAE,GAAGX,eAAeC;IAClDU,SAASW,GAAG,CAACE,SAASR;IAEtBJ,SAASW,OAAO,CAACC;IACjB,OAAO,SAASC;QACdd,SAASe,MAAM,CAACF;QAChBZ,SAASa,SAAS,CAACD;QAEnB,uDAAuD;QACvD,IAAIb,SAASgB,IAAI,KAAK,GAAG;YACvBf,SAASgB,UAAU;YACnB/B,UAAU6B,MAAM,CAACxB;YACjB,MAAM2B,QAAQ9B,OAAO+B,SAAS,CAC5B,CAACtB,MAAQA,IAAIL,IAAI,KAAKD,GAAGC,IAAI,IAAIK,IAAIJ,MAAM,KAAKF,GAAGE,MAAM;YAE3D,IAAIyB,QAAQ,CAAC,GAAG;gBACd9B,OAAOgC,MAAM,CAACF,OAAO;YACvB;QACF;IACF;AACF;AAEO,SAASnC,gBAAmC,KAIjC;IAJiC,IAAA,EACjDsC,OAAO,EACP3B,UAAU,EACV4B,QAAQ,EACQ,GAJiC;IAKjD,MAAMC,aAAsBD,YAAY,CAACtC;IAEzC,MAAM,CAACwC,SAASC,WAAW,GAAGC,IAAAA,eAAQ,EAAC;IACvC,MAAMC,aAAaC,IAAAA,aAAM,EAAW;IACpC,MAAMC,aAAaC,IAAAA,kBAAW,EAAC,CAACjB;QAC9Bc,WAAWI,OAAO,GAAGlB;IACvB,GAAG,EAAE;IAELmB,IAAAA,gBAAS,EAAC;QACR,IAAIhD,yBAAyB;YAC3B,IAAIuC,cAAcC,SAAS;YAE3B,MAAMX,UAAUc,WAAWI,OAAO;YAClC,IAAIlB,WAAWA,QAAQoB,OAAO,EAAE;gBAC9B,MAAMnB,YAAYF,QAChBC,SACA,CAACN,YAAcA,aAAakB,WAAWlB,YACvC;oBAAEf,IAAI,EAAE6B,2BAAAA,QAASU,OAAO;oBAAErC;gBAAW;gBAGvC,OAAOoB;YACT;QACF,OAAO;YACL,IAAI,CAACU,SAAS;gBACZ,MAAMU,eAAeC,IAAAA,wCAAmB,EAAC,IAAMV,WAAW;gBAC1D,OAAO,IAAMW,IAAAA,uCAAkB,EAACF;YAClC;QACF;IACA,uDAAuD;IACzD,GAAG;QAACX;QAAY7B;QAAY2B;QAASG;QAASG,WAAWI,OAAO;KAAC;IAEjE,MAAMM,eAAeP,IAAAA,kBAAW,EAAC;QAC/BL,WAAW;IACb,GAAG,EAAE;IAEL,OAAO;QAACI;QAAYL;QAASa;KAAa;AAC5C"}