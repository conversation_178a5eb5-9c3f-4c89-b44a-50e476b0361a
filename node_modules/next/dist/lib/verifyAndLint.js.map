{"version": 3, "sources": ["../../src/lib/verifyAndLint.ts"], "names": ["verifyAndLint", "dir", "cacheLocation", "configLintDirs", "enableWorkerThreads", "telemetry", "lintWorkers", "Worker", "require", "resolve", "numWorkers", "maxRetries", "getStdout", "pipe", "process", "stdout", "getStderr", "stderr", "lintDirs", "ESLINT_DEFAULT_DIRS", "reduce", "res", "d", "currDir", "join", "existsSync", "push", "lintResults", "runLintCheck", "lintDuringBuild", "eslintOptions", "lintOutput", "output", "eventInfo", "record", "eventLintCheckCompleted", "buildLint", "isError", "flush", "CompileError", "console", "log", "end", "err", "type", "error", "red", "message", "exit"], "mappings": ";;;;+BAUsBA;;;eAAAA;;;4BAVF;4BACG;oBACI;sBACN;2BACe;wBAEI;8BACX;gEACT;;;;;;AAEb,eAAeA,cACpBC,GAAW,EACXC,aAAqB,EACrBC,cAAoC,EACpCC,mBAAwC,EACxCC,SAAoB;IAEpB,IAAI;QACF,MAAMC,cAAc,IAAIC,kBAAM,CAACC,QAAQC,OAAO,CAAC,0BAA0B;YACvEC,YAAY;YACZN;YACAO,YAAY;QACd;QAIAL,YAAYM,SAAS,GAAGC,IAAI,CAACC,QAAQC,MAAM;QAC3CT,YAAYU,SAAS,GAAGH,IAAI,CAACC,QAAQG,MAAM;QAE3C,MAAMC,WAAW,AAACf,CAAAA,kBAAkBgB,8BAAmB,AAAD,EAAGC,MAAM,CAC7D,CAACC,KAAeC;YACd,MAAMC,UAAUC,IAAAA,UAAI,EAACvB,KAAKqB;YAC1B,IAAI,CAACG,IAAAA,cAAU,EAACF,UAAU,OAAOF;YACjCA,IAAIK,IAAI,CAACH;YACT,OAAOF;QACT,GACA,EAAE;QAGJ,MAAMM,cAAc,MAAMrB,YAAYsB,YAAY,CAAC3B,KAAKiB,UAAU;YAChEW,iBAAiB;YACjBC,eAAe;gBACb5B;YACF;QACF;QACA,MAAM6B,aACJ,OAAOJ,gBAAgB,WAAWA,cAAcA,+BAAAA,YAAaK,MAAM;QAErE,IAAI,OAAOL,gBAAgB,aAAYA,+BAAAA,YAAaM,SAAS,GAAE;YAC7D5B,UAAU6B,MAAM,CACdC,IAAAA,+BAAuB,EAAC;gBACtB,GAAGR,YAAYM,SAAS;gBACxBG,WAAW;YACb;QAEJ;QAEA,IAAI,OAAOT,gBAAgB,aAAYA,+BAAAA,YAAaU,OAAO,KAAIN,YAAY;YACzE,MAAM1B,UAAUiC,KAAK;YACrB,MAAM,IAAIC,0BAAY,CAACR;QACzB;QAEA,IAAIA,YAAY;YACdS,QAAQC,GAAG,CAACV;QACd;QAEAzB,YAAYoC,GAAG;IACjB,EAAE,OAAOC,KAAK;QACZ,IAAIN,IAAAA,gBAAO,EAACM,MAAM;YAChB,IAAIA,IAAIC,IAAI,KAAK,kBAAkBD,eAAeJ,0BAAY,EAAE;gBAC9DC,QAAQK,KAAK,CAACC,IAAAA,eAAG,EAAC;gBAClBN,QAAQK,KAAK,CAACF,IAAII,OAAO;gBACzBjC,QAAQkC,IAAI,CAAC;YACf,OAAO,IAAIL,IAAIC,IAAI,KAAK,cAAc;gBACpCJ,QAAQK,KAAK,CAACF,IAAII,OAAO;gBACzBjC,QAAQkC,IAAI,CAAC;YACf;QACF;QACA,MAAML;IACR;AACF"}