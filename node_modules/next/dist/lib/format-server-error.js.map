{"version": 3, "sources": ["../../src/lib/format-server-error.ts"], "names": ["formatServerError", "getStackWithoutErrorMessage", "invalidServerComponentReactHooks", "setMessage", "error", "message", "stack", "lines", "split", "join", "replace", "includes", "addedMessage", "clientHook", "regex", "RegExp", "test"], "mappings": ";;;;;;;;;;;;;;;IAwCgBA,iBAAiB;eAAjBA;;IANAC,2BAA2B;eAA3BA;;;AAlChB,MAAMC,mCAAmC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,WAAWC,KAAY,EAAEC,OAAe;IAC/CD,MAAMC,OAAO,GAAGA;IAChB,IAAID,MAAME,KAAK,EAAE;QACf,MAAMC,QAAQH,MAAME,KAAK,CAACE,KAAK,CAAC;QAChCD,KAAK,CAAC,EAAE,GAAGF;QACXD,MAAME,KAAK,GAAGC,MAAME,IAAI,CAAC;IAC3B;AACF;AAYO,SAASR,4BAA4BG,KAAY;IACtD,MAAME,QAAQF,MAAME,KAAK;IACzB,IAAI,CAACA,OAAO,OAAO;IACnB,OAAOA,MAAMI,OAAO,CAAC,aAAa;AACpC;AAEO,SAASV,kBAAkBI,KAAY;IAC5C,IAAI,QAAOA,yBAAAA,MAAOC,OAAO,MAAK,UAAU;IAExC,IACED,MAAMC,OAAO,CAACM,QAAQ,CACpB,+DAEF;QACA,MAAMC,eACJ;QAEF,qEAAqE;QACrE,IAAIR,MAAMC,OAAO,CAACM,QAAQ,CAACC,eAAe;QAE1CT,WACEC,OACA,CAAC,EAAEA,MAAMC,OAAO,CAAC;;AAEvB,EAAEO,aAAa,CAAC;QAEZ;IACF;IAEA,IAAIR,MAAMC,OAAO,CAACM,QAAQ,CAAC,oCAAoC;QAC7DR,WACEC,OACA;QAEF;IACF;IAEA,KAAK,MAAMS,cAAcX,iCAAkC;QACzD,MAAMY,QAAQ,IAAIC,OAAO,CAAC,GAAG,EAAEF,WAAW,sBAAsB,CAAC;QACjE,IAAIC,MAAME,IAAI,CAACZ,MAAMC,OAAO,GAAG;YAC7BF,WACEC,OACA,CAAC,EAAES,WAAW,oLAAoL,CAAC;YAErM;QACF;IACF;AACF"}