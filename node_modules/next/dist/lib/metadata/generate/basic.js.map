{"version": 3, "sources": ["../../../../src/lib/metadata/generate/basic.tsx"], "names": ["AppleWebAppMeta", "BasicMeta", "FormatDetectionMeta", "ItunesMeta", "VerificationMeta", "ViewportMeta", "resolveViewportLayout", "viewport", "resolved", "viewportKey_", "ViewportMetaKeys", "viewportKey", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Meta", "name", "content", "themeColor", "map", "color", "media", "colorScheme", "metadata", "meta", "charSet", "title", "absolute", "description", "applicationName", "authors", "author", "url", "link", "rel", "href", "toString", "manifest", "crossOrigin", "generator", "keywords", "join", "referrer", "creator", "publisher", "robots", "basic", "googleBot", "abstract", "archives", "archive", "assets", "asset", "bookmarks", "bookmark", "category", "classification", "other", "Object", "entries", "Array", "isArray", "contentItem", "itunes", "appId", "appArgument", "formatDetectionKeys", "formatDetection", "key", "appleWebApp", "capable", "startupImage", "statusBarStyle", "image", "verification", "MultiMeta", "namePrefix", "contents", "google", "yahoo", "yandex", "me"], "mappings": ";;;;;;;;;;;;;;;;;;;IA2IgBA,eAAe;eAAfA;;IA7FAC,SAAS;eAATA;;IA6EAC,mBAAmB;eAAnBA;;IAjBAC,UAAU;eAAVA;;IAiEAC,gBAAgB;eAAhBA;;IA7IAC,YAAY;eAAZA;;;;8DAvBE;sBAC0B;2BACX;;;;;;AAEjC,0DAA0D;AAC1D,SAASC,sBAAsBC,QAAkB;IAC/C,IAAIC,WAA0B;IAE9B,IAAID,YAAY,OAAOA,aAAa,UAAU;QAC5CC,WAAW;QACX,IAAK,MAAMC,gBAAgBC,2BAAgB,CAAE;YAC3C,MAAMC,cAAcF;YACpB,IAAIE,eAAeJ,UAAU;gBAC3B,IAAIK,QAAQL,QAAQ,CAACI,YAAY;gBACjC,IAAI,OAAOC,UAAU,WAAWA,QAAQA,QAAQ,QAAQ;gBACxD,IAAIJ,UAAUA,YAAY;gBAC1BA,YAAY,CAAC,EAAEE,2BAAgB,CAACC,YAAY,CAAC,CAAC,EAAEC,MAAM,CAAC;YACzD;QACF;IACF;IACA,OAAOJ;AACT;AAEO,SAASH,aAAa,EAAEE,QAAQ,EAAkC;IACvE,OAAOM,IAAAA,gBAAU,EAAC;QAChBC,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAYC,SAASV,sBAAsBC;QAAU;WAC9DA,SAASU,UAAU,GACnBV,SAASU,UAAU,CAACC,GAAG,CAAC,CAACD,aACvBH,IAAAA,UAAI,EAAC;gBACHC,MAAM;gBACNC,SAASC,WAAWE,KAAK;gBACzBC,OAAOH,WAAWG,KAAK;YACzB,MAEF,EAAE;QACNN,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAgBC,SAAST,SAASc,WAAW;QAAC;KAC5D;AACH;AAEO,SAASpB,UAAU,EAAEqB,QAAQ,EAAkC;QAwBhCA,oBAIFA,kBACGA;IA5BrC,OAAOT,IAAAA,gBAAU,EAAC;sBAChB,qBAACU;YAAKC,SAAQ;;QACdF,SAASG,KAAK,KAAK,QAAQH,SAASG,KAAK,CAACC,QAAQ,iBAChD,qBAACD;sBAAOH,SAASG,KAAK,CAACC,QAAQ;aAC7B;QACJZ,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAeC,SAASM,SAASK,WAAW;QAAC;QAC1Db,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAoBC,SAASM,SAASM,eAAe;QAAC;WAC/DN,SAASO,OAAO,GAChBP,SAASO,OAAO,CAACX,GAAG,CAAC,CAACY,SAAW;gBAC/BA,OAAOC,GAAG,iBACR,qBAACC;oBAAKC,KAAI;oBAASC,MAAMJ,OAAOC,GAAG,CAACI,QAAQ;qBAC1C;gBACJrB,IAAAA,UAAI,EAAC;oBAAEC,MAAM;oBAAUC,SAASc,OAAOf,IAAI;gBAAC;aAC7C,IACD,EAAE;QACNO,SAASc,QAAQ,iBACf,qBAACJ;YACCC,KAAI;YACJC,MAAMZ,SAASc,QAAQ,CAACD,QAAQ;YAChCE,aAAY;aAEZ;QACJvB,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAaC,SAASM,SAASgB,SAAS;QAAC;QACtDxB,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAYC,OAAO,GAAEM,qBAAAA,SAASiB,QAAQ,qBAAjBjB,mBAAmBkB,IAAI,CAAC;QAAK;QAC/D1B,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAYC,SAASM,SAASmB,QAAQ;QAAC;QACpD3B,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAWC,SAASM,SAASoB,OAAO;QAAC;QAClD5B,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAaC,SAASM,SAASqB,SAAS;QAAC;QACtD7B,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAUC,OAAO,GAAEM,mBAAAA,SAASsB,MAAM,qBAAftB,iBAAiBuB,KAAK;QAAC;QACvD/B,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAaC,OAAO,GAAEM,oBAAAA,SAASsB,MAAM,qBAAftB,kBAAiBwB,SAAS;QAAC;QAC9DhC,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAYC,SAASM,SAASyB,QAAQ;QAAC;WAChDzB,SAAS0B,QAAQ,GACjB1B,SAAS0B,QAAQ,CAAC9B,GAAG,CAAC,CAAC+B,wBACrB,qBAACjB;gBAAKC,KAAI;gBAAWC,MAAMe;kBAE7B,EAAE;WACF3B,SAAS4B,MAAM,GACf5B,SAAS4B,MAAM,CAAChC,GAAG,CAAC,CAACiC,sBAAU,qBAACnB;gBAAKC,KAAI;gBAASC,MAAMiB;kBACxD,EAAE;WACF7B,SAAS8B,SAAS,GAClB9B,SAAS8B,SAAS,CAAClC,GAAG,CAAC,CAACmC,yBACtB,qBAACrB;gBAAKC,KAAI;gBAAYC,MAAMmB;kBAE9B,EAAE;QACNvC,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAYC,SAASM,SAASgC,QAAQ;QAAC;QACpDxC,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAkBC,SAASM,SAASiC,cAAc;QAAC;WAC5DjC,SAASkC,KAAK,GACdC,OAAOC,OAAO,CAACpC,SAASkC,KAAK,EAAEtC,GAAG,CAAC,CAAC,CAACH,MAAMC,QAAQ;YACjD,IAAI2C,MAAMC,OAAO,CAAC5C,UAAU;gBAC1B,OAAOA,QAAQE,GAAG,CAAC,CAAC2C,cAClB/C,IAAAA,UAAI,EAAC;wBAAEC;wBAAMC,SAAS6C;oBAAY;YAEtC,OAAO;gBACL,OAAO/C,IAAAA,UAAI,EAAC;oBAAEC;oBAAMC;gBAAQ;YAC9B;QACF,KACA,EAAE;KACP;AACH;AAEO,SAASb,WAAW,EAAE2D,MAAM,EAA0C;IAC3E,IAAI,CAACA,QAAQ,OAAO;IACpB,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAE,GAAGF;IAC/B,IAAI9C,UAAU,CAAC,OAAO,EAAE+C,MAAM,CAAC;IAC/B,IAAIC,aAAa;QACfhD,WAAW,CAAC,eAAe,EAAEgD,YAAY,CAAC;IAC5C;IACA,qBAAO,qBAACzC;QAAKR,MAAK;QAAmBC,SAASA;;AAChD;AAEA,MAAMiD,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AACM,SAAS/D,oBAAoB,EAClCgE,eAAe,EAGhB;IACC,IAAI,CAACA,iBAAiB,OAAO;IAC7B,IAAIlD,UAAU;IACd,KAAK,MAAMmD,OAAOF,oBAAqB;QACrC,IAAIE,OAAOD,iBAAiB;YAC1B,IAAIlD,SAASA,WAAW;YACxBA,WAAW,CAAC,EAAEmD,IAAI,GAAG,CAAC;QACxB;IACF;IACA,qBAAO,qBAAC5C;QAAKR,MAAK;QAAmBC,SAASA;;AAChD;AAEO,SAAShB,gBAAgB,EAC9BoE,WAAW,EAGZ;IACC,IAAI,CAACA,aAAa,OAAO;IAEzB,MAAM,EAAEC,OAAO,EAAE5C,KAAK,EAAE6C,YAAY,EAAEC,cAAc,EAAE,GAAGH;IAEzD,OAAOvD,IAAAA,gBAAU,EAAC;QAChBwD,UACIvD,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAgCC,SAAS;QAAM,KAC5D;QACJF,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAA8BC,SAASS;QAAM;QAC1D6C,eACIA,aAAapD,GAAG,CAAC,CAACsD,sBAChB,qBAACxC;gBACCE,MAAMsC,MAAMzC,GAAG;gBACfX,OAAOoD,MAAMpD,KAAK;gBAClBa,KAAI;kBAGR;QACJsC,iBACIzD,IAAAA,UAAI,EAAC;YACHC,MAAM;YACNC,SAASuD;QACX,KACA;KACL;AACH;AAEO,SAASnE,iBAAiB,EAC/BqE,YAAY,EAGb;IACC,IAAI,CAACA,cAAc,OAAO;IAE1B,OAAO5D,IAAAA,gBAAU,EAAC;QAChB6D,IAAAA,eAAS,EAAC;YACRC,YAAY;YACZC,UAAUH,aAAaI,MAAM;QAC/B;QACAH,IAAAA,eAAS,EAAC;YAAEC,YAAY;YAASC,UAAUH,aAAaK,KAAK;QAAC;QAC9DJ,IAAAA,eAAS,EAAC;YACRC,YAAY;YACZC,UAAUH,aAAaM,MAAM;QAC/B;QACAL,IAAAA,eAAS,EAAC;YAAEC,YAAY;YAAMC,UAAUH,aAAaO,EAAE;QAAC;WACpDP,aAAajB,KAAK,GAClBC,OAAOC,OAAO,CAACe,aAAajB,KAAK,EAAEtC,GAAG,CAAC,CAAC,CAACiD,KAAKvD,MAAM,GAClD8D,IAAAA,eAAS,EAAC;gBAAEC,YAAYR;gBAAKS,UAAUhE;YAAM,MAE/C,EAAE;KACP;AACH"}