{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "names": ["createMetadataComponents", "tree", "pathname", "trailingSlash", "query", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "createDynamicallyTrackedSearchParams", "metadataContext", "split", "resolve", "metadataErrorResolving", "Promise", "res", "MetadataTree", "defaultMetadata", "createDefaultMetadata", "defaultViewport", "createDefaultViewport", "metadata", "viewport", "error", "errorMetadataItem", "errorConvention", "undefined", "searchParams", "resolvedError", "resolvedMetadata", "resolvedViewport", "resolveMetadata", "parentParams", "metadataItems", "isNotFoundError", "notFoundMetadataError", "notFoundMetadata", "notFoundViewport", "elements", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportMeta", "BasicMeta", "AlternatesMetadata", "alternates", "ItunesMeta", "itunes", "FormatDetectionMeta", "formatDetection", "VerificationMeta", "verification", "AppleWebAppMeta", "appleWebApp", "OpenGraphMetadata", "openGraph", "TwitterMetadata", "twitter", "AppLinksMeta", "appLinks", "IconsMetadata", "icons", "push", "meta", "name", "map", "el", "index", "React", "cloneElement", "key", "MetadataOutlet"], "mappings": ";;;;+BAsCgBA;;;eAAAA;;;;8DAlCE;uBAQX;2BAC4B;2BAK5B;uBACuB;iCACE;sBACL;iCAQpB;0BACyB;;;;;;AAQzB,SAASA,yBAAyB,EACvCC,IAAI,EACJC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EACTC,oCAAoC,EAYrC;IACC,MAAMC,kBAAkB;QACtB,8CAA8C;QAC9CP,UAAUA,SAASQ,KAAK,CAAC,IAAI,CAAC,EAAE;QAChCP;IACF;IAEA,IAAIQ;IACJ,8DAA8D;IAC9D,MAAMC,yBAAyB,IAAIC,QAA2B,CAACC;QAC7DH,UAAUG;IACZ;IAEA,eAAeC;QACb,MAAMC,kBAAkBC,IAAAA,sCAAqB;QAC7C,MAAMC,kBAAkBC,IAAAA,sCAAqB;QAC7C,IAAIC,WAAyCJ;QAC7C,IAAIK,WAAyCH;QAC7C,IAAII;QACJ,MAAMC,oBAAwC;YAAC;YAAM;YAAM;SAAK;QAChE,MAAMC,kBAAkBjB,cAAc,aAAakB,YAAYlB;QAC/D,MAAMmB,eAAelB,qCAAqCJ;QAE1D,MAAM,CAACuB,eAAeC,kBAAkBC,iBAAiB,GACvD,MAAMC,IAAAA,gCAAe,EAAC;YACpB7B;YACA8B,cAAc,CAAC;YACfC,eAAe,EAAE;YACjBT;YACAG;YACArB;YACAmB;YACAf;QACF;QACF,IAAI,CAACkB,eAAe;YAClBN,WAAWQ;YACXT,WAAWQ;YACXjB,QAAQc;QACV,OAAO;YACLH,QAAQK;YACR,0FAA0F;YAC1F,kGAAkG;YAClG,kDAAkD;YAClD,IAAI,CAACpB,aAAa0B,IAAAA,yBAAe,EAACN,gBAAgB;gBAChD,MAAM,CAACO,uBAAuBC,kBAAkBC,iBAAiB,GAC/D,MAAMN,IAAAA,gCAAe,EAAC;oBACpB7B;oBACA8B,cAAc,CAAC;oBACfC,eAAe,EAAE;oBACjBT;oBACAG;oBACArB;oBACAmB,iBAAiB;oBACjBf;gBACF;gBACFY,WAAWe;gBACXhB,WAAWe;gBACXb,QAAQY,yBAAyBZ;YACnC;YACAX,QAAQW;QACV;QAEA,MAAMe,WAAWC,IAAAA,gBAAU,EAAC;YAC1BC,IAAAA,mBAAY,EAAC;gBAAElB,UAAUA;YAAS;YAClCmB,IAAAA,gBAAS,EAAC;gBAAEpB;YAAS;YACrBqB,IAAAA,6BAAkB,EAAC;gBAAEC,YAAYtB,SAASsB,UAAU;YAAC;YACrDC,IAAAA,iBAAU,EAAC;gBAAEC,QAAQxB,SAASwB,MAAM;YAAC;YACrCC,IAAAA,0BAAmB,EAAC;gBAAEC,iBAAiB1B,SAAS0B,eAAe;YAAC;YAChEC,IAAAA,uBAAgB,EAAC;gBAAEC,cAAc5B,SAAS4B,YAAY;YAAC;YACvDC,IAAAA,sBAAe,EAAC;gBAAEC,aAAa9B,SAAS8B,WAAW;YAAC;YACpDC,IAAAA,4BAAiB,EAAC;gBAAEC,WAAWhC,SAASgC,SAAS;YAAC;YAClDC,IAAAA,0BAAe,EAAC;gBAAEC,SAASlC,SAASkC,OAAO;YAAC;YAC5CC,IAAAA,uBAAY,EAAC;gBAAEC,UAAUpC,SAASoC,QAAQ;YAAC;YAC3CC,IAAAA,oBAAa,EAAC;gBAAEC,OAAOtC,SAASsC,KAAK;YAAC;SACvC;QAED,IAAIpD,wBAAwB+B,SAASsB,IAAI,eAAC,qBAACC;YAAKC,MAAK;;QAErD,qBACE;sBACGxB,SAASyB,GAAG,CAAC,CAACC,IAAIC;gBACjB,qBAAOC,cAAK,CAACC,YAAY,CAACH,IAA0B;oBAAEI,KAAKH;gBAAM;YACnE;;IAGN;IAEA,eAAeI;QACb,MAAM9C,QAAQ,MAAMV;QACpB,IAAIU,OAAO;YACT,MAAMA;QACR;QACA,OAAO;IACT;IAEA,OAAO;QAACP;QAAcqD;KAAe;AACvC"}