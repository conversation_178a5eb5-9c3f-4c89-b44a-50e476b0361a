{"version": 3, "sources": ["../../../src/lib/typescript/diagnosticFormatter.ts"], "names": ["getFormattedDiagnostic", "DiagnosticCategory", "getFormattedLinkDiagnosticMessageText", "diagnostic", "message", "messageText", "code", "match", "href", "bold", "relatedMessage", "relatedInformation", "suggestion", "getFormattedLayoutAndPageDiagnosticMessageText", "relativeSourceFilepath", "type", "test", "filepathAndType", "main", "processNext", "indent", "next", "item", "mismatchedField", "repeat", "types", "replace", "invalidConfig", "<PERSON><PERSON><PERSON>", "invalidProp", "invalid", "incompatPageProp", "extraLayoutProp", "invalidExportFnArg", "processNextItems", "result", "invalidParamFn", "invalidExportFnReturn", "filepathAndInvalidExport", "getAppEntrySourceFilePath", "baseDir", "sourceFilepath", "file", "text", "trim", "path", "relative", "ts", "distDir", "isAppDirEnabled", "isLayoutOrPageError", "fileName", "startsWith", "join", "appPath", "linkReason", "appReason", "reason", "flattenDiagnosticMessageText", "category", "yellow", "red", "cyan", "codeFrameColumns", "require", "pos", "getLineAndCharacterOfPosition", "start", "line", "character", "posix", "normalize", "toString", "getFullText", "getSourceFile", "column", "forceColor"], "mappings": ";;;;;;;;;;;;;;;;;;IAuTgBA,sBAAsB;eAAtBA;;;4BAvTwB;6DACvB;;;;;;;UAILC;;;;;GAAAA,uBAAAA;AAOZ,SAASC,sCACPC,UAA2C;IAE3C,MAAMC,UAAUD,WAAWE,WAAW;IACtC,IAAI,OAAOD,YAAY,YAAYD,WAAWG,IAAI,KAAK,MAAM;QAC3D,MAAMC,QACJH,QAAQG,KAAK,CACX,6EAEFH,QAAQG,KAAK,CACX;QAGJ,IAAIA,OAAO;YACT,MAAM,GAAGC,KAAK,GAAGD;YACjB,OAAO,CAAC,CAAC,EAAEE,IAAAA,gBAAI,EACbD,MACA,8FAA8F,CAAC;QACnG,OAAO,IACLJ,YAAY,wDACZ;gBACuBD,iCAAAA;YAAvB,MAAMO,kBAAiBP,iCAAAA,WAAWQ,kBAAkB,sBAA7BR,kCAAAA,8BAA+B,CAAC,EAAE,qBAAlCA,gCAAoCE,WAAW;YACtE,IACE,OAAOK,mBAAmB,YAC1BA,eAAeH,KAAK,CAClB,wGAEF;gBACA,OAAO,CAAC,mIAAmI,CAAC;YAC9I;QACF;IACF,OAAO,IAAI,OAAOH,YAAY,YAAYD,WAAWG,IAAI,KAAK,MAAM;QAClE,MAAMC,QACJH,QAAQG,KAAK,CACX,oGAEFH,QAAQG,KAAK,CACX;QAGJ,IAAIA,OAAO;YACT,MAAM,GAAGC,MAAMI,WAAW,GAAGL;YAC7B,OAAO,CAAC,CAAC,EAAEE,IAAAA,gBAAI,EAACD,MAAM,0CAA0C,EAAEC,IAAAA,gBAAI,EACpEG,YACA,6EAA6E,CAAC;QAClF;IACF;AACF;AAEA,SAASC,+CACPC,sBAA8B,EAC9BX,UAA2C;IAE3C,MAAMC,UACJ,OAAOD,WAAWE,WAAW,KAAK,WAC9BF,aACAA,WAAWE,WAAW;IAC5B,MAAMA,cAAcD,QAAQC,WAAW;IAEvC,IAAI,OAAOA,gBAAgB,UAAU;QACnC,MAAMU,OAAO,eAAeC,IAAI,CAACF,0BAC7B,SACA,gBAAgBE,IAAI,CAACF,0BACrB,UACA;QAEJ,4BAA4B;QAC5B,yFAAyF;QACzF,OAAQV,QAAQE,IAAI;YAClB,KAAK;gBACH,MAAMW,kBAAkBZ,YAAYE,KAAK,CAAC;gBAC1C,IAAIU,iBAAiB;oBACnB,IAAIC,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEN,IAAAA,gBAAI,EACzBK,wBACA,iDAAiD,EAAEC,KAAK,CAAC,CAAC;oBAE5D,SAASI,YACPC,MAAc,EACdC,IAAoD;wBAEpD,IAAI,CAACA,MAAM;wBAEX,KAAK,MAAMC,QAAQD,KAAM;4BACvB,OAAQC,KAAKhB,IAAI;gCACf,KAAK;oCACH,MAAMiB,kBACJD,KAAKjB,WAAW,CAACE,KAAK,CAAC;oCACzB,IAAIgB,iBAAiB;wCACnBL,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,CAAC,EAAET,IAAAA,gBAAI,EAACc,eAAe,CAAC,EAAE,EAAE,qBAAqB,CAAC;oCAC7D;oCACA;gCACF,KAAK;oCACH,MAAME,QAAQH,KAAKjB,WAAW,CAACE,KAAK,CAClC;oCAEF,IAAIkB,OAAO;wCACTP,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCAEnC,IACEK,KAAK,CAAC,EAAE,KAAK,mBACbA,KAAK,CAAC,EAAE,KAAK,mBACb;4CACAP,QAAQ,CAAC,aAAa,EAAEH,KAAK,iCAAiC,CAAC;wCACjE,OAAO;4CACLG,QAAQ,CAAC,UAAU,EAAET,IAAAA,gBAAI,EACvBgB,KAAK,CAAC,EAAE,CAACC,OAAO,CACd,iCACA,kBAEF,QAAQ,EAAEjB,IAAAA,gBAAI,EAACgB,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;wCAChC;oCACF;oCACA;gCACF,KAAK;oCACH,MAAME,gBAAgBL,KAAKjB,WAAW,CAACE,KAAK,CAC1C;oCAEFW,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;oCACnCF,QAAQ,CAAC,qBAAqB,EAC5BS,gBAAgB,CAAC,EAAE,EAAElB,IAAAA,gBAAI,EAACkB,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAClD,CAAC,CAAC;oCACH;gCACF,KAAK;oCACH,MAAMC,eAAeN,KAAKjB,WAAW,CAACE,KAAK,CACzC;oCAEF,IAAIqB,cAAc;wCAChBV,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,CAAC,EAAET,IAAAA,gBAAI,EACdmB,YAAY,CAAC,EAAE,EACf,iBAAiB,EAAEb,KAAK,cAAc,CAAC;oCAC3C;oCACA;gCACF,KAAK;oCACH,MAAMc,cAAcP,KAAKjB,WAAW,CAACE,KAAK,CACxC;oCAEF,IAAIsB,aAAa;wCACf,IACEA,WAAW,CAAC,EAAE,KAAK,iBACnBA,WAAW,CAAC,EAAE,KAAK,aACnB;4CACAX,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;4CACnCF,QAAQ,CAAC,MAAM,EAAEW,WAAW,CAAC,EAAE,CAAC,2BAA2B,EAAEd,KAAK,CAAC,CAAC;wCACtE;oCACF;oCACA;gCACF,KAAK;oCACH,MAAMe,UAAUR,KAAKjB,WAAW,CAACE,KAAK,CAAC;oCACvC,IAAIuB,SAAS;wCACXZ,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,MAAM,EAAET,IAAAA,gBAAI,EAACqB,OAAO,CAAC,EAAE,EAAE,gBAAgB,CAAC;oCACrD;oCACA;gCACF,KAAK;oCACH,MAAMC,mBAAmBT,KAAKjB,WAAW,CAACE,KAAK,CAC7C;oCAEF,IAAIwB,kBAAkB;wCACpBb,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,MAAM,EAAET,IAAAA,gBAAI,EACnBsB,gBAAgB,CAAC,EAAE,EACnB,6DAA6D,CAAC;oCAClE,OAAO;wCACL,MAAMC,kBAAkBV,KAAKjB,WAAW,CAACE,KAAK,CAC5C;wCAEF,IAAIyB,iBAAiB;4CACnBd,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;4CACnCF,QAAQ,CAAC,MAAM,EAAET,IAAAA,gBAAI,EACnBuB,eAAe,CAAC,EAAE,EAClB,iDAAiD,CAAC;wCACtD;oCACF;oCACA;gCACF;4BACF;4BAEAb,YAAYC,SAAS,GAAGE,KAAKD,IAAI;wBACnC;oBACF;oBAEA,IAAI,UAAUjB,SAASe,YAAY,GAAGf,QAAQiB,IAAI;oBAClD,OAAOH;gBACT;gBAEA,MAAMe,qBAAqB5B,YAAYE,KAAK,CAC1C;gBAEF,IAAI0B,oBAAoB;oBACtB,MAAMf,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEN,IAAAA,gBAAI,EAC3BK,wBACA,kBAAkB,EAAEL,IAAAA,gBAAI,EACxBwB,kBAAkB,CAAC,EAAE,EACrB,mBAAmB,EAAExB,IAAAA,gBAAI,EAACwB,kBAAkB,CAAC,EAAE,EAAE,eAAe,CAAC;oBACnE,OAAOf;gBACT;gBAEA,SAASgB,iBACPd,MAAc,EACdC,IAAoD;oBAEpD,IAAI,CAACA,MAAM,OAAO;oBAElB,IAAIc,SAAS;oBAEb,KAAK,MAAMb,QAAQD,KAAM;wBACvB,OAAQC,KAAKhB,IAAI;4BACf,KAAK;gCACH,MAAMmB,QAAQH,KAAKjB,WAAW,CAACE,KAAK,CAClC;gCAEF,IAAIkB,OAAO;oCACTU,UAAU,OAAO,IAAIX,MAAM,CAACJ,SAAS;oCACrCe,UAAU,CAAC,UAAU,EAAE1B,IAAAA,gBAAI,EAACgB,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAEhB,IAAAA,gBAAI,EAClDgB,KAAK,CAAC,EAAE,EACR,EAAE,CAAC;gCACP;gCACA;4BACF;wBACF;wBAEAU,UAAUD,iBAAiBd,SAAS,GAAGE,KAAKD,IAAI;oBAClD;oBAEA,OAAOc;gBACT;gBAEA,MAAMC,iBAAiB/B,YAAYE,KAAK,CACtC;gBAEF,IAAI6B,gBAAgB;oBAClB,IAAIlB,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEN,IAAAA,gBAAI,EACzBK,wBACA,iBAAiB,EAAEsB,cAAc,CAAC,EAAE,CAAC,kBAAkB,EAAE3B,IAAAA,gBAAI,EAC7D2B,cAAc,CAAC,EAAE,EACjB,yCAAyC,EACzCA,cAAc,CAAC,EAAE,CAClB,UAAU,CAAC;oBAEZ,IAAI,UAAUhC,SAASc,QAAQgB,iBAAiB,GAAG9B,QAAQiB,IAAI;oBAC/D,OAAOH;gBACT;gBAEA,MAAMmB,wBAAwBhC,YAAYE,KAAK,CAC7C;gBAEF,IAAI8B,uBAAuB;oBACzB,IAAInB,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEN,IAAAA,gBAAI,EACzBK,wBACA,6BAA6B,EAAEL,IAAAA,gBAAI,EACnC4B,qBAAqB,CAAC,EAAE,EACxB,iBAAiB,EAAEA,qBAAqB,CAAC,EAAE,CAAC,aAAa,CAAC;oBAE5D,IAAI,UAAUjC,SAASc,QAAQgB,iBAAiB,GAAG9B,QAAQiB,IAAI;oBAC/D,OAAOH;gBACT;gBAEA;YACF,KAAK;gBACH,MAAMoB,2BAA2BjC,YAAYE,KAAK,CAChD;gBAEF,IAAI+B,0BAA0B;oBAC5B,MAAMpB,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEN,IAAAA,gBAAI,EAC3BK,wBACA,sBAAsB,EAAEL,IAAAA,gBAAI,EAC5B6B,wBAAwB,CAAC,EAAE,EAC3B,SAAS,EAAEvB,KAAK,4IAA4I,CAAC;oBAC/J,OAAOG;gBACT;gBACA;YACF,KAAK;gBACH,MAAMY,UAAUzB,YAAYE,KAAK,CAC/B;gBAEF,IAAIuB,SAAS;oBACX,MAAMZ,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEN,IAAAA,gBAAI,EAC3BK,wBACA,4BAA4B,EAAEL,IAAAA,gBAAI,EAACqB,OAAO,CAAC,EAAE,EAAE,KAAK,EAAEA,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;oBACrE,OAAOZ;gBACT;gBACA;YACF;QACF;IACF;AACF;AAEA,SAASqB,0BACPC,OAAe,EACfrC,UAA2C;QAGzCA,kCAAAA;IADF,MAAMsC,iBACJtC,EAAAA,mBAAAA,WAAWuC,IAAI,sBAAfvC,mCAAAA,iBAAiBwC,IAAI,CAACC,IAAI,GAAGrC,KAAK,CAAC,0CAAnCJ,gCAA0D,CAAC,EAAE,KAAI;IAEnE,OAAO0C,aAAI,CAACC,QAAQ,CAACN,SAASC;AAChC;AAEO,SAASzC,uBACd+C,EAA+B,EAC/BP,OAAe,EACfQ,OAAe,EACf7C,UAA2C,EAC3C8C,eAAyB;QAKvB9C;IAHF,gEAAgE;IAChE,MAAM+C,sBACJD,qBACA9C,mBAAAA,WAAWuC,IAAI,qBAAfvC,iBAAiBgD,QAAQ,CAACC,UAAU,CAACP,aAAI,CAACQ,IAAI,CAACb,SAASQ,SAAS;IAEnE,IAAI5C,UAAU;IAEd,MAAMkD,UAAUJ,sBACZX,0BAA0BC,SAASrC,cACnC;IACJ,MAAMoD,aAAarD,sCAAsCC;IACzD,MAAMqD,YACJ,CAACD,cAAcL,uBAAuBI,UAClCzC,+CAA+CyC,SAASnD,cACxD;IAEN,MAAMsD,SACJF,cACAC,aACAT,GAAGW,4BAA4B,CAACvD,WAAWE,WAAW,EAAE;IAC1D,MAAMsD,WAAWxD,WAAWwD,QAAQ;IACpC,OAAQA;QACN,UAAU;QACV;YAAiC;gBAC/BvD,WAAWwD,IAAAA,kBAAM,EAACnD,IAAAA,gBAAI,EAAC,mBAAmB;gBAC1C;YACF;QACA,QAAQ;QACR;YAA+B;gBAC7BL,WAAWyD,IAAAA,eAAG,EAACpD,IAAAA,gBAAI,EAAC,iBAAiB;gBACrC;YACF;QACA,8BAA8B;QAC9B;QACA;QACA;YAAS;gBACPL,WAAW0D,IAAAA,gBAAI,EAACrD,IAAAA,gBAAI,EAACkD,aAAa,IAAI,eAAe,WAAW;gBAChE;YACF;IACF;IAEAvD,WAAWqD,SAAS;IAEpB,IAAI,CAACP,uBAAuB/C,WAAWuC,IAAI,EAAE;QAC3C,MAAM,EAAEqB,gBAAgB,EAAE,GAAGC,QAAQ;QACrC,MAAMC,MAAM9D,WAAWuC,IAAI,CAACwB,6BAA6B,CAAC/D,WAAWgE,KAAK;QAC1E,MAAMC,OAAOH,IAAIG,IAAI,GAAG;QACxB,MAAMC,YAAYJ,IAAII,SAAS,GAAG;QAElC,IAAIlB,WAAWN,aAAI,CAACyB,KAAK,CAACC,SAAS,CACjC1B,aAAI,CAACC,QAAQ,CAACN,SAASrC,WAAWuC,IAAI,CAACS,QAAQ,EAAEzB,OAAO,CAAC,OAAO;QAElE,IAAI,CAACyB,SAASC,UAAU,CAAC,MAAM;YAC7BD,WAAW,OAAOA;QACpB;QAEA/C,UACE0D,IAAAA,gBAAI,EAACX,YACL,MACAS,IAAAA,kBAAM,EAACQ,KAAKI,QAAQ,MACpB,MACAZ,IAAAA,kBAAM,EAACS,UAAUG,QAAQ,MACzB,OACApE;QAEFA,WACE,OACA2D,iBACE5D,WAAWuC,IAAI,CAAC+B,WAAW,CAACtE,WAAWuC,IAAI,CAACgC,aAAa,KACzD;YACEP,OAAO;gBAAEC,MAAMA;gBAAMO,QAAQN;YAAU;QACzC,GACA;YAAEO,YAAY;QAAK;IAEzB,OAAO,IAAI1B,uBAAuBI,SAAS;QACzClD,UAAU0D,IAAAA,gBAAI,EAACR,WAAW,OAAOlD;IACnC;IAEA,OAAOA;AACT"}