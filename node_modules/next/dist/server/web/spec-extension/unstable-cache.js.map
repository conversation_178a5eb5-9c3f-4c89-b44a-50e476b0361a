{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "names": ["unstable_cache", "noStoreFetchIdx", "cacheNewResult", "result", "incrementalCache", "cache<PERSON>ey", "tags", "revalidate", "fetchIdx", "fetchUrl", "set", "kind", "data", "headers", "body", "JSON", "stringify", "status", "url", "CACHE_ONE_YEAR", "fetchCache", "cb", "keyParts", "options", "Error", "toString", "validateTags", "validateRevalidate", "name", "fixedKey", "Array", "isArray", "join", "cachedCb", "args", "store", "staticGenerationAsyncStorage", "getStore", "maybeIncrementalCache", "globalThis", "__incrementalCache", "pathname", "searchParams", "URL", "urlPathname", "sortedSearchKeys", "keys", "sort", "a", "b", "localeCompare", "sortedSearch", "map", "key", "get", "invocation<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "nextFetchId", "slice", "tag", "includes", "push", "implicitTags", "addImplicitTags", "isOnDemandRevalidate", "isDraftMode", "cacheEntry", "kindHint", "softTags", "value", "console", "error", "cachedResponse", "undefined", "parse", "isStale", "pendingRevalidates", "run", "isUnstableCacheCallback", "then", "catch", "err", "isStaticGeneration", "prerenderState"], "mappings": ";;;;+BAoDgBA;;;eAAAA;;;2BAlDe;4BAKxB;sDACsC;AAI7C,IAAIC,kBAAkB;AAEtB,eAAeC,eACbC,MAAS,EACTC,gBAAkC,EAClCC,QAAgB,EAChBC,IAAc,EACdC,UAAsC,EACtCC,QAAgB,EAChBC,QAAgB;IAEhB,MAAML,iBAAiBM,GAAG,CACxBL,UACA;QACEM,MAAM;QACNC,MAAM;YACJC,SAAS,CAAC;YACV,gCAAgC;YAChCC,MAAMC,KAAKC,SAAS,CAACb;YACrBc,QAAQ;YACRC,KAAK;QACP;QACAX,YAAY,OAAOA,eAAe,WAAWY,yBAAc,GAAGZ;IAChE,GACA;QACEA;QACAa,YAAY;QACZd;QACAE;QACAC;IACF;IAEF;AACF;AAOO,SAAST,eACdqB,EAAK,EACLC,QAAmB,EACnBC,UAMI,CAAC,CAAC;IAEN,IAAIA,QAAQhB,UAAU,KAAK,GAAG;QAC5B,MAAM,IAAIiB,MACR,CAAC,wFAAwF,EAAEH,GAAGI,QAAQ,GAAG,CAAC;IAE9G;IAEA,uCAAuC;IACvC,MAAMnB,OAAOiB,QAAQjB,IAAI,GACrBoB,IAAAA,wBAAY,EAACH,QAAQjB,IAAI,EAAE,CAAC,eAAe,EAAEe,GAAGI,QAAQ,GAAG,CAAC,IAC5D,EAAE;IAEN,kCAAkC;IAClCE,IAAAA,8BAAkB,EAChBJ,QAAQhB,UAAU,EAClB,CAAC,eAAe,EAAEc,GAAGO,IAAI,IAAIP,GAAGI,QAAQ,GAAG,CAAC;IAG9C,wFAAwF;IACxF,wDAAwD;IACxD,mDAAmD;IACnD,8DAA8D;IAC9D,8FAA8F;IAC9F,iGAAiG;IACjG,gBAAgB;IAChB,MAAMI,WAAW,CAAC,EAAER,GAAGI,QAAQ,GAAG,CAAC,EACjCK,MAAMC,OAAO,CAACT,aAAaA,SAASU,IAAI,CAAC,KAC1C,CAAC;IAEF,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,QAAQC,kEAA4B,CAACC,QAAQ;QAEnD,mEAAmE;QACnE,MAAMC,wBAGJH,CAAAA,yBAAAA,MAAO/B,gBAAgB,KAAI,AAACmC,WAAmBC,kBAAkB;QAEnE,IAAI,CAACF,uBAAuB;YAC1B,MAAM,IAAId,MACR,CAAC,sDAAsD,EAAEH,GAAGI,QAAQ,GAAG,CAAC;QAE5E;QACA,MAAMrB,mBAAmBkC;QAEzB,MAAM,EAAEG,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IACrCR,CAAAA,yBAAAA,MAAOS,WAAW,KAAI,KACtB;QAEF,MAAMC,mBAAmB;eAAIH,aAAaI,IAAI;SAAG,CAACC,IAAI,CAAC,CAACC,GAAGC;YACzD,OAAOD,EAAEE,aAAa,CAACD;QACzB;QACA,MAAME,eAAeN,iBAClBO,GAAG,CAAC,CAACC,MAAQ,CAAC,EAAEA,IAAI,CAAC,EAAEX,aAAaY,GAAG,CAACD,KAAK,CAAC,EAC9CrB,IAAI,CAAC;QAER,gEAAgE;QAChE,4FAA4F;QAC5F,gDAAgD;QAChD,MAAMuB,gBAAgB,CAAC,EAAE1B,SAAS,CAAC,EAAEd,KAAKC,SAAS,CAACkB,MAAM,CAAC;QAC3D,MAAM7B,WAAW,MAAMD,iBAAiBoD,aAAa,CAACD;QACtD,4DAA4D;QAC5D,MAAM9C,WAAW,CAAC,eAAe,EAAEgC,SAAS,EAC1CU,aAAaM,MAAM,GAAG,MAAM,GAC7B,EAAEN,aAAa,CAAC,EAAE9B,GAAGO,IAAI,GAAG,CAAC,CAAC,EAAEP,GAAGO,IAAI,CAAC,CAAC,GAAGvB,SAAS,CAAC;QACvD,MAAMG,WAAW,AAAC2B,CAAAA,QAAQA,MAAMuB,WAAW,GAAGzD,eAAc,KAAM;QAElE,IAAIkC,OAAO;YACTA,MAAMuB,WAAW,GAAGlD,WAAW;YAE/B,+FAA+F;YAC/F,qGAAqG;YACrG,4FAA4F;YAE5F,4FAA4F;YAC5F,IAAI,OAAOe,QAAQhB,UAAU,KAAK,UAAU;gBAC1C,IACE,OAAO4B,MAAM5B,UAAU,KAAK,YAC5B4B,MAAM5B,UAAU,GAAGgB,QAAQhB,UAAU,EACrC;gBACA,+EAA+E;gBACjF,OAAO;oBACL4B,MAAM5B,UAAU,GAAGgB,QAAQhB,UAAU;gBACvC;YACF,OAAO,IACLgB,QAAQhB,UAAU,KAAK,SACvB,OAAO4B,MAAM5B,UAAU,KAAK,aAC5B;gBACA,2EAA2E;gBAC3E4B,MAAM5B,UAAU,GAAGgB,QAAQhB,UAAU;YACvC;YAEA,sEAAsE;YACtE,IAAI,CAAC4B,MAAM7B,IAAI,EAAE;gBACf6B,MAAM7B,IAAI,GAAGA,KAAKqD,KAAK;YACzB,OAAO;gBACL,KAAK,MAAMC,OAAOtD,KAAM;oBACtB,4DAA4D;oBAC5D,IAAI,CAAC6B,MAAM7B,IAAI,CAACuD,QAAQ,CAACD,MAAM;wBAC7BzB,MAAM7B,IAAI,CAACwD,IAAI,CAACF;oBAClB;gBACF;YACF;YACA,uGAAuG;YACvG,qDAAqD;YACrD,MAAMG,eAAeC,IAAAA,2BAAe,EAAC7B;YAErC,IACE,sDAAsD;YACtD,4CAA4C;YAC5CA,MAAMf,UAAU,KAAK,oBACrB,CAACe,MAAM8B,oBAAoB,IAC3B,CAAC7D,iBAAiB6D,oBAAoB,IACtC,CAAC9B,MAAM+B,WAAW,EAClB;gBACA,wEAAwE;gBACxE,MAAMC,aAAa,MAAM/D,iBAAiBkD,GAAG,CAACjD,UAAU;oBACtD+D,UAAU;oBACV7D,YAAYgB,QAAQhB,UAAU;oBAC9BD;oBACA+D,UAAUN;oBACVvD;oBACAC;gBACF;gBAEA,IAAI0D,cAAcA,WAAWG,KAAK,EAAE;oBAClC,mCAAmC;oBACnC,IAAIH,WAAWG,KAAK,CAAC3D,IAAI,KAAK,SAAS;wBACrC,qDAAqD;wBACrD,6FAA6F;wBAC7F,0BAA0B;wBAC1B,+FAA+F;wBAC/F4D,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEjB,cAAc,CAAC;oBAE9D,0DAA0D;oBAC5D,OAAO;wBACL,0FAA0F;wBAC1F,0DAA0D;wBAC1D,MAAMkB,iBACJN,WAAWG,KAAK,CAAC1D,IAAI,CAACE,IAAI,KAAK4D,YAC3B3D,KAAK4D,KAAK,CAACR,WAAWG,KAAK,CAAC1D,IAAI,CAACE,IAAI,IACrC4D;wBACN,IAAIP,WAAWS,OAAO,EAAE;4BACtB,4EAA4E;4BAC5E,IAAI,CAACzC,MAAM0C,kBAAkB,EAAE;gCAC7B1C,MAAM0C,kBAAkB,GAAG,CAAC;4BAC9B;4BACA,iFAAiF;4BACjF1C,MAAM0C,kBAAkB,CAACtB,cAAc,GACrCnB,kEAA4B,CACzB0C,GAAG,CACF;gCACE,GAAG3C,KAAK;gCACR,8DAA8D;gCAC9D,8CAA8C;gCAC9Cf,YAAY;gCACZ2D,yBAAyB;4BAC3B,GACA1D,OACGa,MAEJ8C,IAAI,CAAC,CAAC7E;gCACL,OAAOD,eACLC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;4BAEJ,EACA,+DAA+D;6BAC9DwE,KAAK,CAAC,CAACC,MACNX,QAAQC,KAAK,CACX,CAAC,6BAA6B,EAAEjB,cAAc,CAAC,EAC/C2B;wBAGV;wBACA,kDAAkD;wBAClD,OAAOT;oBACT;gBACF;YACF;YAEA,uFAAuF;YACvF,MAAMtE,SAAS,MAAMiC,kEAA4B,CAAC0C,GAAG,CACnD;gBACE,GAAG3C,KAAK;gBACR,8DAA8D;gBAC9D,8CAA8C;gBAC9Cf,YAAY;gBACZ2D,yBAAyB;YAC3B,GACA1D,OACGa;YAELhC,eACEC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;YAEF,OAAON;QACT,OAAO;YACLF,mBAAmB;YACnB,mFAAmF;YACnF,8DAA8D;YAC9D,qGAAqG;YACrG,4FAA4F;YAE5F,IAAI,CAACG,iBAAiB6D,oBAAoB,EAAE;gBAC1C,+EAA+E;gBAE/E,uGAAuG;gBACvG,qDAAqD;gBACrD,MAAMF,eAAe5B,SAAS6B,IAAAA,2BAAe,EAAC7B;gBAE9C,MAAMgC,aAAa,MAAM/D,iBAAiBkD,GAAG,CAACjD,UAAU;oBACtD+D,UAAU;oBACV7D,YAAYgB,QAAQhB,UAAU;oBAC9BD;oBACAE;oBACAC;oBACA4D,UAAUN;gBACZ;gBAEA,IAAII,cAAcA,WAAWG,KAAK,EAAE;oBAClC,mCAAmC;oBACnC,IAAIH,WAAWG,KAAK,CAAC3D,IAAI,KAAK,SAAS;wBACrC,qDAAqD;wBACrD,6FAA6F;wBAC7F,0BAA0B;wBAC1B4D,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEjB,cAAc,CAAC;oBAE9D,0DAA0D;oBAC5D,OAAO,IAAI,CAACY,WAAWS,OAAO,EAAE;wBAC9B,8DAA8D;wBAC9D,OAAOT,WAAWG,KAAK,CAAC1D,IAAI,CAACE,IAAI,KAAK4D,YAClC3D,KAAK4D,KAAK,CAACR,WAAWG,KAAK,CAAC1D,IAAI,CAACE,IAAI,IACrC4D;oBACN;gBACF;YACF;YAEA,uFAAuF;YACvF,8FAA8F;YAC9F,oGAAoG;YACpG,yGAAyG;YACzG,iGAAiG;YACjG,kGAAkG;YAClG,+EAA+E;YAC/E,MAAMvE,SAAS,MAAMiC,kEAA4B,CAAC0C,GAAG,CACnD,uHAAuH;YACvH,0GAA0G;YAC1G,uDAAuD;YACvD;gBACE,8DAA8D;gBAC9D,8CAA8C;gBAC9C1D,YAAY;gBACZ2D,yBAAyB;gBACzBnC,aAAa;gBACbuC,oBAAoB;gBACpBC,gBAAgB;YAClB,GACA/D,OACGa;YAELhC,eACEC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;YAEF,OAAON;QACT;IACF;IACA,yGAAyG;IACzG,OAAO8B;AACT"}