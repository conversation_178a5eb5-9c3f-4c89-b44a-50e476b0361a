{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "names": ["ErrorSource", "getRuntimeContext", "run", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "process", "env", "NODE_ENV", "getServerError", "require", "params", "then", "result", "waitUntil", "catch", "error", "runtime", "evaluateInContext", "getModuleContext", "moduleName", "name", "onWarning", "onError", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "runWithTaggedErrors", "subreq", "request", "headers", "subrequests", "split", "MAX_RECURSION_DEPTH", "depth", "reduce", "acc", "curr", "Promise", "resolve", "response", "Response", "edgeFunction", "_ENTRIES", "default", "cloned", "includes", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "NEXT_RSC_UNION_QUERY", "toString", "Headers", "key", "value", "Object", "entries", "set", "requestStore", "requestToBodyStream", "headerName", "Error", "finalize"], "mappings": ";;;;;;;;;;;;;;;;IAOaA,WAAW;eAAXA;;IA+CSC,iBAAiB;eAAjBA;;IA6BTC,GAAG;eAAHA;;;yBAhFkC;6BACX;kCACC;AAE9B,MAAMF,cAAcG,OAAO;AAElC,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAcD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;QAEV,OAAO,CAACC,SACNN,GAAGM,QACAC,IAAI,CAAC,CAACC;oBAEMA;uBAFM;oBACjB,GAAGA,MAAM;oBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;wBACnC,mGAAmG;wBACnG,MAAMP,eAAeO,OAAO;oBAC9B;gBACF;eACCD,KAAK,CAAC,CAACC;gBACN,+CAA+C;gBAC/C,MAAMP,eAAeO,OAAO;YAC9B;IACN;IAEA,OAAOX;AACT;AAEO,eAAeL,kBAAkBW,MASvC;IACC,MAAM,EAAEM,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMC,IAAAA,yBAAgB,EAAC;QAC5DC,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,SAASZ,OAAOY,OAAO,IAAK,CAAA,KAAO,CAAA;QACnCC,UAAUb,OAAOa,QAAQ,KAAK;QAC9BC,mBAAmBd,OAAOc,iBAAiB;QAC3CC,SAASf,OAAOe,OAAO;IACzB;IAEA,IAAIf,OAAOgB,gBAAgB,EAAE;QAC3BV,QAAQW,OAAO,CAACC,UAAU,CAACC,kBAAkB,GAAGnB,OAAOgB,gBAAgB;IACzE;IAEA,KAAK,MAAMI,aAAapB,OAAOqB,KAAK,CAAE;QACpCd,kBAAkBa;IACpB;IACA,OAAOd;AACT;AAEO,MAAMhB,MAAMG,iBAAiB,eAAe6B,oBAAoBtB,MAAM;QA6BvEA;IA5BJ,MAAMM,UAAU,MAAMjB,kBAAkBW;IACxC,MAAMuB,SAASvB,OAAOwB,OAAO,CAACC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC;IAChE,MAAMC,cAAc,OAAOH,WAAW,WAAWA,OAAOI,KAAK,CAAC,OAAO,EAAE;IAEvE,MAAMC,sBAAsB;IAC5B,MAAMC,QAAQH,YAAYI,MAAM,CAC9B,CAACC,KAAKC,OAAUA,SAAShC,OAAOU,IAAI,GAAGqB,MAAM,IAAIA,KACjD;IAGF,IAAIF,SAASD,qBAAqB;QAChC,OAAO;YACLzB,WAAW8B,QAAQC,OAAO;YAC1BC,UAAU,IAAI7B,QAAQW,OAAO,CAACmB,QAAQ,CAAC,MAAM;gBAC3CX,SAAS;oBACP,qBAAqB;gBACvB;YACF;QACF;IACF;IAEA,MAAMY,eAE4B,AAChC,CAAA,MAAM/B,QAAQW,OAAO,CAACqB,QAAQ,CAAC,CAAC,WAAW,EAAEtC,OAAOU,IAAI,CAAC,CAAC,CAAC,AAAD,EAC1D6B,OAAO;IAET,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACC,QAAQ,CAACzC,OAAOwB,OAAO,CAACkB,MAAM,KAC1D1C,uBAAAA,OAAOwB,OAAO,CAACmB,IAAI,qBAAnB3C,qBAAqB4C,eAAe,KACpCC;IAEJ,MAAMC,cAAcxC,QAAQyC,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAIjD,OAAOwB,OAAO,CAAC0B,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAACC,sCAAoB;IAEpDrD,OAAOwB,OAAO,CAAC0B,GAAG,GAAGF,YAAYM,QAAQ;IAEzC,MAAM7B,UAAU,IAAI8B;IACpB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAC3D,OAAOwB,OAAO,CAACC,OAAO,EAAG;QACjEA,QAAQmC,GAAG,CAACJ,KAAKC,CAAAA,yBAAAA,MAAOH,QAAQ,OAAM;IACxC;IAEA,IAAI;QACF,IAAIpD,SAAuC2C;QAC3C,MAAMgB,qBAAY,CAACvE,GAAG,CAAC;YAAEmC;QAAQ,GAAG;YAClCvB,SAAS,MAAMmC,aAAa;gBAC1Bb,SAAS;oBACP,GAAGxB,OAAOwB,OAAO;oBACjBmB,MACEH,UAAUsB,IAAAA,gCAAmB,EAACxD,QAAQW,OAAO,EAAE6B,aAAaN;gBAChE;YACF;YACA,KAAK,MAAMuB,cAAcvE,kBAAmB;gBAC1CU,OAAOiC,QAAQ,CAACV,OAAO,CAAC2B,MAAM,CAACW;YACjC;QACF;QACA,IAAI,CAAC7D,QAAQ,MAAM,IAAI8D,MAAM;QAC7B,OAAO9D;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAOwB,OAAO,CAACmB,IAAI,qBAAnB3C,sBAAqBiE,QAAQ;IACrC;AACF"}