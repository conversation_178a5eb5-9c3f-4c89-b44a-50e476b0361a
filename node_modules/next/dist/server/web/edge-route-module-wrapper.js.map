{"version": 3, "sources": ["../../../src/server/web/edge-route-module-wrapper.ts"], "names": ["EdgeRouteModuleWrapper", "routeModule", "matcher", "RouteMatcher", "definition", "wrap", "options", "wrapper", "opts", "adapter", "IncrementalCache", "handler", "bind", "request", "evt", "utils", "getUtils", "pageIsDynamic", "isDynamic", "page", "pathname", "basePath", "nextUrl", "rewrites", "caseSensitive", "params", "normalizeDynamicRouteParams", "searchParamsToUrlQuery", "searchParams", "prerenderManifest", "self", "__PRERENDER_MANIFEST", "JSON", "parse", "undefined", "context", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "renderOpts", "supportsDynamicHTML", "experimental", "ppr", "res", "handle", "waitUntilPromises", "internal_getCurrentFunctionWaitUntil", "waitUntil", "push", "Promise", "all"], "mappings": ";;;;+BAwBaA;;;eAAAA;;;QAjBN;yBAEsC;kCACZ;8BACJ;uCAEwB;6BAC5B;6BACc;AAShC,MAAMA;IAGX;;;;;GAKC,GACD,YAAoB,AAAiBC,WAAgC,CAAE;aAAlCA,cAAAA;QACnC,wEAAwE;QACxE,IAAI,CAACC,OAAO,GAAG,IAAIC,0BAAY,CAACF,YAAYG,UAAU;IACxD;IAEA;;;;;;;;GAQC,GACD,OAAcC,KACZJ,WAAgC,EAChCK,UAAuB,CAAC,CAAC,EACzB;QACA,6BAA6B;QAC7B,MAAMC,UAAU,IAAIP,uBAAuBC;QAE3C,gCAAgC;QAChC,OAAO,CAACO;YACN,OAAOC,IAAAA,gBAAO,EAAC;gBACb,GAAGD,IAAI;gBACP,GAAGF,OAAO;gBACVI,kBAAAA,kCAAgB;gBAChB,kEAAkE;gBAClEC,SAASJ,QAAQI,OAAO,CAACC,IAAI,CAACL;YAChC;QACF;IACF;IAEA,MAAcI,QACZE,OAAoB,EACpBC,GAAmB,EACA;QACnB,MAAMC,QAAQC,IAAAA,qBAAQ,EAAC;YACrBC,eAAe,IAAI,CAACf,OAAO,CAACgB,SAAS;YACrCC,MAAM,IAAI,CAACjB,OAAO,CAACE,UAAU,CAACgB,QAAQ;YACtCC,UAAUR,QAAQS,OAAO,CAACD,QAAQ;YAClC,2EAA2E;YAC3EE,UAAU,CAAC;YACX,qEAAqE;YACrEC,eAAe;QACjB;QAEA,MAAM,EAAEC,MAAM,EAAE,GAAGV,MAAMW,2BAA2B,CAClDC,IAAAA,mCAAsB,EAACd,QAAQS,OAAO,CAACM,YAAY;QAGrD,MAAMC,oBACJ,OAAOC,KAAKC,oBAAoB,KAAK,WACjCC,KAAKC,KAAK,CAACH,KAAKC,oBAAoB,IACpCG;QAEN,wEAAwE;QACxE,kBAAkB;QAClB,MAAMC,UAAuC;YAC3CV;YACAI,mBAAmB;gBACjBO,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAASV,CAAAA,qCAAAA,kBAAmBU,OAAO,KAAI;oBACrCC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB;YACAC,YAAY;gBACVC,qBAAqB;gBACrB,mCAAmC;gBACnCC,cAAc;oBAAEC,KAAK;gBAAM;YAC7B;QACF;QAEA,qCAAqC;QACrC,MAAMC,MAAM,MAAM,IAAI,CAAC/C,WAAW,CAACgD,MAAM,CAACpC,SAASsB;QAEnD,MAAMe,oBAAoB;YAACC,IAAAA,2DAAoC;SAAG;QAClE,IAAIhB,QAAQS,UAAU,CAACQ,SAAS,EAAE;YAChCF,kBAAkBG,IAAI,CAAClB,QAAQS,UAAU,CAACQ,SAAS;QACrD;QACAtC,IAAIsC,SAAS,CAACE,QAAQC,GAAG,CAACL;QAE1B,OAAOF;IACT;AACF"}