{"version": 3, "sources": ["../../../../src/server/future/helpers/i18n-provider.ts"], "names": ["I18NProvider", "constructor", "config", "locales", "length", "Error", "lowerCaseLocales", "map", "locale", "toLowerCase", "lowerCaseDomains", "domains", "domainLocale", "domain", "defaultLocale", "hostname", "split", "http", "detectDomainLocale", "detectedLocale", "i", "some", "fromQuery", "pathname", "query", "__next<PERSON><PERSON><PERSON>", "analysis", "analyze", "inferredFromDefault", "__nextInferredLocaleFromDefault", "options", "segments", "segment", "index", "indexOf", "slice"], "mappings": ";;;;+BAoCaA;;;eAAAA;;;AAAN,MAAMA;IAWXC,YAAY,AAAgBC,MAA4B,CAAE;YAMhCA;aANEA,SAAAA;QAC1B,IAAI,CAACA,OAAOC,OAAO,CAACC,MAAM,EAAE;YAC1B,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAI,CAACC,gBAAgB,GAAGJ,OAAOC,OAAO,CAACI,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;QACzE,IAAI,CAACC,gBAAgB,IAAGR,kBAAAA,OAAOS,OAAO,qBAAdT,gBAAgBK,GAAG,CAAC,CAACK;gBAMhCA;YALX,MAAMC,SAASD,aAAaC,MAAM,CAACJ,WAAW;YAC9C,OAAO;gBACLK,eAAeF,aAAaE,aAAa,CAACL,WAAW;gBACrDM,UAAUF,OAAOG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;gBACjCH;gBACAV,OAAO,GAAES,wBAAAA,aAAaT,OAAO,qBAApBS,sBAAsBL,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;gBACjEQ,MAAML,aAAaK,IAAI;YACzB;QACF;IACF;IAEA;;;;;;;GAOC,GACD,AAAOC,mBACLH,QAAiB,EACjBI,cAAuB,EACG;QAC1B,IAAI,CAACJ,YAAY,CAAC,IAAI,CAACL,gBAAgB,IAAI,CAAC,IAAI,CAACR,MAAM,CAACS,OAAO,EAAE;QAEjE,IAAIQ,gBAAgBA,iBAAiBA,eAAeV,WAAW;QAE/D,IAAK,IAAIW,IAAI,GAAGA,IAAI,IAAI,CAACV,gBAAgB,CAACN,MAAM,EAAEgB,IAAK;gBAKnD,sEAAsE;YACtE,yBAAyB;YACzBR;YANF,MAAMA,eAAe,IAAI,CAACF,gBAAgB,CAACU,EAAE;YAC7C,IACE,qDAAqD;YACrDR,aAAaG,QAAQ,KAAKA,cAG1BH,wBAAAA,aAAaT,OAAO,qBAApBS,sBAAsBS,IAAI,CAAC,CAACb,SAAWA,WAAWW,kBAClD;gBACA,OAAO,IAAI,CAACjB,MAAM,CAACS,OAAO,CAACS,EAAE;YAC/B;QACF;QAEA;IACF;IAEA;;;;;;;GAOC,GACD,AAAOE,UACLC,QAAgB,EAChBC,KAAyB,EACH;QACtB,MAAML,iBAAiBK,MAAMC,YAAY;QAEzC,wEAAwE;QACxE,2BAA2B;QAC3B,IAAIN,gBAAgB;YAClB,MAAMO,WAAW,IAAI,CAACC,OAAO,CAACJ;YAE9B,uEAAuE;YACvE,wCAAwC;YACxC,IAAIG,SAASP,cAAc,EAAE;gBAC3B,IAAIO,SAASP,cAAc,KAAKA,gBAAgB;oBAC9C,MAAM,IAAId,MACR,CAAC,yFAAyF,EAAEc,eAAe,MAAM,EAAEI,SAAS,aAAa,EAAEG,SAASP,cAAc,CAAC,EAAE,CAAC;gBAE1K;gBAEAI,WAAWG,SAASH,QAAQ;YAC9B;QACF;QAEA,OAAO;YACLA;YACAJ;YACAS,qBAAqBJ,MAAMK,+BAA+B,KAAK;QACjE;IACF;IAEA;;;;;;;GAOC,GACD,AAAOF,QACLJ,QAAgB,EAChBO,UAAiC,CAAC,CAAC,EACb;QACtB,IAAIX,iBAAqCW,QAAQhB,aAAa;QAE9D,0EAA0E;QAC1E,sBAAsB;QACtB,IAAIc,sBAAsB,OAAOT,mBAAmB;QAEpD,oEAAoE;QACpE,yEAAyE;QACzE,MAAMY,WAAWR,SAASP,KAAK,CAAC,KAAK;QACrC,IAAI,CAACe,QAAQ,CAAC,EAAE,EACd,OAAO;YACLZ;YACAI;YACAK;QACF;QAEF,0DAA0D;QAC1D,MAAMI,UAAUD,QAAQ,CAAC,EAAE,CAACtB,WAAW;QAEvC,yEAAyE;QACzE,mCAAmC;QACnC,MAAMwB,QAAQ,IAAI,CAAC3B,gBAAgB,CAAC4B,OAAO,CAACF;QAC5C,IAAIC,QAAQ,GACV,OAAO;YACLd;YACAI;YACAK;QACF;QAEF,oCAAoC;QACpCT,iBAAiB,IAAI,CAACjB,MAAM,CAACC,OAAO,CAAC8B,MAAM;QAC3CL,sBAAsB;QAEtB,gDAAgD;QAChDL,WAAWA,SAASY,KAAK,CAAChB,eAAef,MAAM,GAAG,MAAM;QAExD,OAAO;YACLe;YACAI;YACAK;QACF;IACF;AACF"}