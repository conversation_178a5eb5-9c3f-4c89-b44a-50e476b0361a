{"version": 3, "sources": ["../../../../../src/server/future/normalizers/request/postponed.ts"], "names": ["PostponedPathnameNormalizer", "prefix", "PrefixPathnameNormalizer", "constructor", "normalize", "pathname", "matched", "match", "denormalizePagePath"], "mappings": ";;;;+BAOaA;;;eAAAA;;;qCAPuB;wBAGK;AAEzC,MAAMC,SAAS;AAER,MAAMD,oCACHE,gCAAwB;IAGhCC,aAAc;QACZ,KAAK,CAACF;IACR;IAEOG,UAAUC,QAAgB,EAAEC,OAAiB,EAAU;QAC5D,uEAAuE;QACvE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACC,KAAK,CAACF,WAAW,OAAOA;QAE9C,qBAAqB;QACrBA,WAAW,KAAK,CAACD,UAAUC,UAAU;QAErC,OAAOG,IAAAA,wCAAmB,EAACH;IAC7B;AACF"}