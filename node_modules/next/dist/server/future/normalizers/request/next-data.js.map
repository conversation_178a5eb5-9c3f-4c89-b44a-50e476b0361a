{"version": 3, "sources": ["../../../../../src/server/future/normalizers/request/next-data.ts"], "names": ["NextDataPathnameNormalizer", "constructor", "buildID", "suffix", "SuffixPathnameNormalizer", "Error", "prefix", "PrefixPathnameNormalizer", "match", "pathname", "normalize", "matched", "denormalizePagePath"], "mappings": ";;;;+BAMaA;;;eAAAA;;;qCAJuB;wBACK;wBACA;AAElC,MAAMA;IAGXC,YAAYC,OAAe,CAAE;aADZC,SAAS,IAAIC,gCAAwB,CAAC;QAErD,IAAI,CAACF,SAAS;YACZ,MAAM,IAAIG,MAAM;QAClB;QAEA,IAAI,CAACC,MAAM,GAAG,IAAIC,gCAAwB,CAAC,CAAC,YAAY,EAAEL,QAAQ,CAAC;IACrE;IAEOM,MAAMC,QAAgB,EAAE;QAC7B,OAAO,IAAI,CAACH,MAAM,CAACE,KAAK,CAACC,aAAa,IAAI,CAACN,MAAM,CAACK,KAAK,CAACC;IAC1D;IAEOC,UAAUD,QAAgB,EAAEE,OAAiB,EAAU;QAC5D,uEAAuE;QACvE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACH,KAAK,CAACC,WAAW,OAAOA;QAE9CA,WAAW,IAAI,CAACH,MAAM,CAACI,SAAS,CAACD,UAAU;QAC3CA,WAAW,IAAI,CAACN,MAAM,CAACO,SAAS,CAACD,UAAU;QAE3C,OAAOG,IAAAA,wCAAmB,EAACH;IAC7B;AACF"}