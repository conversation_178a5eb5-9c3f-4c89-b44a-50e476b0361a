{"version": 3, "sources": ["../../src/server/load-components.ts"], "names": ["evalManifestWithRetries", "loadComponents", "loadManifestWithRetries", "manifestPath", "attempts", "loadManifest", "err", "wait", "evalManifest", "loadClientReferenceManifest", "entryName", "context", "__RSC_MANIFEST", "undefined", "loadComponentsImpl", "distDir", "page", "isAppPath", "DocumentMod", "AppMod", "Promise", "all", "resolve", "then", "requirePage", "hasClientManifest", "endsWith", "UNDERSCORE_NOT_FOUND_ROUTE", "buildManifest", "reactLoadableManifest", "clientReferenceManifest", "serverActionsManifest", "join", "BUILD_MANIFEST", "REACT_LOADABLE_MANIFEST", "replace", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "catch", "setReferenceManifestsSingleton", "serverModuleMap", "createServerModuleMap", "pageName", "ComponentMod", "Component", "interopDefault", "Document", "App", "getServerSideProps", "getStaticProps", "getStaticPaths", "routeModule", "pageConfig", "config", "getTracer", "wrap", "LoadComponentsSpan"], "mappings": ";;;;;;;;;;;;;;;;IA4FsBA,uBAAuB;eAAvBA;;IA+HTC,cAAc;eAAdA;;IAlJSC,uBAAuB;eAAvBA;;;2BAnDf;sBACc;yBACO;gCACG;wBACL;4BACS;8BACQ;sBACtB;iCAC0B;6BACT;AA0C/B,eAAeA,wBACpBC,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOC,IAAAA,0BAAY,EAACF;QACtB,EAAE,OAAOG,KAAK;YACZF;YACA,IAAIA,YAAY,GAAG,MAAME;YAEzB,MAAMC,IAAAA,UAAI,EAAC;QACb;IACF;AACF;AAKO,eAAeP,wBACpBG,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOI,IAAAA,0BAAY,EAACL;QACtB,EAAE,OAAOG,KAAK;YACZF;YACA,IAAIA,YAAY,GAAG,MAAME;YAEzB,MAAMC,IAAAA,UAAI,EAAC;QACb;IACF;AACF;AAEA,eAAeE,4BACbN,YAAoB,EACpBO,SAAiB;IAEjB,IAAI;QACF,MAAMC,UAAW,MAAMX,wBAAwBG;QAG/C,OAAOQ,QAAQC,cAAc,CAACF,UAAU;IAC1C,EAAE,OAAOJ,KAAK;QACZ,OAAOO;IACT;AACF;AAEA,eAAeC,mBAA4B,EACzCC,OAAO,EACPC,IAAI,EACJC,SAAS,EAKV;IACC,IAAIC,cAAc,CAAC;IACnB,IAAIC,SAAS,CAAC;IACd,IAAI,CAACF,WAAW;QACb,CAACC,aAAaC,OAAO,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACzCD,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAMC,IAAAA,oBAAW,EAAC,cAAcT,SAAS;YAChEK,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAMC,IAAAA,oBAAW,EAAC,SAAST,SAAS;SAC5D;IACH;IAEA,6DAA6D;IAC7D,MAAMU,oBACJR,aAAcD,CAAAA,KAAKU,QAAQ,CAAC,YAAYV,SAASW,qCAA0B,AAAD;IAE5E,gCAAgC;IAChC,MAAM,CACJC,eACAC,uBACAC,yBACAC,sBACD,GAAG,MAAMX,QAAQC,GAAG,CAAC;QACpBnB,wBACE8B,IAAAA,UAAI,EAACjB,SAASkB,yBAAc;QAE9B/B,wBACE8B,IAAAA,UAAI,EAACjB,SAASmB,kCAAuB;QAEvCT,oBACIhB,4BACEuB,IAAAA,UAAI,EACFjB,SACA,UACA,OACAC,KAAKmB,OAAO,CAAC,QAAQ,OAAO,MAAMC,oCAAyB,GAAG,QAEhEpB,KAAKmB,OAAO,CAAC,QAAQ,QAEvBtB;QACJI,YACKf,wBACC8B,IAAAA,UAAI,EAACjB,SAAS,UAAUsB,oCAAyB,GAAG,UACpDC,KAAK,CAAC,IAAM,QACd;KACL;IAED,iFAAiF;IACjF,4EAA4E;IAC5E,uCAAuC;IACvC,IAAIP,yBAAyBD,yBAAyB;QACpDS,IAAAA,+CAA8B,EAAC;YAC7BT;YACAC;YACAS,iBAAiBC,IAAAA,kCAAqB,EAAC;gBACrCV;gBACAW,UAAU1B;YACZ;QACF;IACF;IAEA,MAAM2B,eAAe,MAAMvB,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAChDC,IAAAA,oBAAW,EAACR,MAAMD,SAASE;IAG7B,MAAM2B,YAAYC,IAAAA,8BAAc,EAACF;IACjC,MAAMG,WAAWD,IAAAA,8BAAc,EAAC3B;IAChC,MAAM6B,MAAMF,IAAAA,8BAAc,EAAC1B;IAE3B,MAAM,EAAE6B,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE,GACvER;IAEF,OAAO;QACLI;QACAD;QACAF;QACAhB;QACAC;QACAuB,YAAYT,aAAaU,MAAM,IAAI,CAAC;QACpCV;QACAK;QACAC;QACAC;QACApB;QACAC;QACAd;QACAD;QACAmC;IACF;AACF;AAEO,MAAMlD,iBAAiBqD,IAAAA,iBAAS,IAAGC,IAAI,CAC5CC,8BAAkB,CAACvD,cAAc,EACjCa"}