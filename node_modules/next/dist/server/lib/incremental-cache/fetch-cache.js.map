{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/fetch-cache.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "rateLimitedUntil", "memoryCache", "CACHE_TAGS_HEADER", "CACHE_HEADERS_HEADER", "CACHE_STATE_HEADER", "CACHE_REVALIDATE_HEADER", "CACHE_FETCH_URL_HEADER", "CACHE_CONTROL_VALUE_HEADER", "DEBUG", "Boolean", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "fetchRetryWithTimeout", "url", "init", "retryIndex", "controller", "AbortController", "timeout", "setTimeout", "abort", "fetch", "signal", "catch", "err", "console", "log", "finally", "clearTimeout", "hasMatchingTags", "arr1", "arr2", "length", "set1", "Set", "set2", "size", "tag", "has", "isAvailable", "ctx", "_requestHeaders", "SUSPENSE_CACHE_URL", "constructor", "headers", "newHeaders", "JSON", "parse", "k", "scHost", "sc<PERSON><PERSON><PERSON><PERSON>", "SUSPENSE_CACHE_BASEPATH", "SUSPENSE_CACHE_AUTH_TOKEN", "scProto", "SUSPENSE_CACHE_PROTO", "cacheEndpoint", "maxMemoryCacheSize", "L<PERSON><PERSON><PERSON>", "max", "value", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "resetRequestCache", "reset", "revalidateTag", "args", "tags", "Date", "now", "res", "map", "encodeURIComponent", "join", "method", "next", "internal", "status", "retryAfter", "get", "parseInt", "ok", "warn", "key", "softTags", "kindHint", "fetchIdx", "fetchUrl", "hasFetchKindAndMatchingTags", "start", "fetchParams", "fetchType", "NEXT_CACHE_SOFT_TAGS_HEADER", "error", "text", "cached", "json", "includes", "push", "cacheState", "age", "lastModified", "CACHE_ONE_YEAR", "Object", "keys", "set", "newValue", "undefined", "existingCache", "existingValue", "every", "field", "fetchCache", "revalidate", "toString"], "mappings": ";;;;+BAyDA;;;eAAqBA;;;iEAtDA;2BAId;;;;;;AAEP,IAAIC,mBAAmB;AACvB,IAAIC;AASJ,MAAMC,oBAAoB;AAC1B,MAAMC,uBAAuB;AAC7B,MAAMC,qBAAqB;AAC3B,MAAMC,0BAA0B;AAChC,MAAMC,yBAAyB;AAC/B,MAAMC,6BAA6B;AAEnC,MAAMC,QAAQC,QAAQC,QAAQC,GAAG,CAACC,wBAAwB;AAE1D,eAAeC,sBACbC,GAAgC,EAChCC,IAAiC,EACjCC,aAAa,CAAC;IAEd,MAAMC,aAAa,IAAIC;IACvB,MAAMC,UAAUC,WAAW;QACzBH,WAAWI,KAAK;IAClB,GAAG;IAEH,OAAOC,MAAMR,KAAK;QAChB,GAAIC,QAAQ,CAAC,CAAC;QACdQ,QAAQN,WAAWM,MAAM;IAC3B,GACGC,KAAK,CAAC,CAACC;QACN,IAAIT,eAAe,GAAG;YACpB,MAAMS;QACR,OAAO;YACL,IAAIjB,OAAO;gBACTkB,QAAQC,GAAG,CAAC,CAAC,iBAAiB,EAAEb,IAAI,OAAO,EAAEE,WAAW,CAAC;YAC3D;YACA,OAAOH,sBAAsBC,KAAKC,MAAMC,aAAa;QACvD;IACF,GACCY,OAAO,CAAC;QACPC,aAAaV;IACf;AACJ;AAEe,MAAMpB;IAIX+B,gBAAgBC,IAAc,EAAEC,IAAc,EAAE;QACtD,IAAID,KAAKE,MAAM,KAAKD,KAAKC,MAAM,EAAE,OAAO;QAExC,MAAMC,OAAO,IAAIC,IAAIJ;QACrB,MAAMK,OAAO,IAAID,IAAIH;QAErB,IAAIE,KAAKG,IAAI,KAAKD,KAAKC,IAAI,EAAE,OAAO;QAEpC,KAAK,IAAIC,OAAOJ,KAAM;YACpB,IAAI,CAACE,KAAKG,GAAG,CAACD,MAAM,OAAO;QAC7B;QAEA,OAAO;IACT;IAEA,OAAOE,YAAYC,GAElB,EAAE;QACD,OAAO,CAAC,CACNA,CAAAA,IAAIC,eAAe,CAAC,mBAAmB,IAAIhC,QAAQC,GAAG,CAACgC,kBAAkB,AAAD;IAE5E;IAEAC,YAAYH,GAAwB,CAAE;QACpC,IAAI,CAACI,OAAO,GAAG,CAAC;QAChB,IAAI,CAACA,OAAO,CAAC,eAAe,GAAG;QAE/B,IAAI1C,wBAAwBsC,IAAIC,eAAe,EAAE;YAC/C,MAAMI,aAAaC,KAAKC,KAAK,CAC3BP,IAAIC,eAAe,CAACvC,qBAAqB;YAE3C,IAAK,MAAM8C,KAAKH,WAAY;gBAC1B,IAAI,CAACD,OAAO,CAACI,EAAE,GAAGH,UAAU,CAACG,EAAE;YACjC;YACA,OAAOR,IAAIC,eAAe,CAACvC,qBAAqB;QAClD;QACA,MAAM+C,SACJT,IAAIC,eAAe,CAAC,mBAAmB,IAAIhC,QAAQC,GAAG,CAACgC,kBAAkB;QAE3E,MAAMQ,aACJV,IAAIC,eAAe,CAAC,uBAAuB,IAC3ChC,QAAQC,GAAG,CAACyC,uBAAuB;QAErC,IAAI1C,QAAQC,GAAG,CAAC0C,yBAAyB,EAAE;YACzC,IAAI,CAACR,OAAO,CACV,gBACD,GAAG,CAAC,OAAO,EAAEnC,QAAQC,GAAG,CAAC0C,yBAAyB,CAAC,CAAC;QACvD;QAEA,IAAIH,QAAQ;YACV,MAAMI,UAAU5C,QAAQC,GAAG,CAAC4C,oBAAoB,IAAI;YACpD,IAAI,CAACC,aAAa,GAAG,CAAC,EAAEF,QAAQ,GAAG,EAAEJ,OAAO,EAAEC,cAAc,GAAG,CAAC;YAChE,IAAI3C,OAAO;gBACTkB,QAAQC,GAAG,CAAC,wBAAwB,IAAI,CAAC6B,aAAa;YACxD;QACF,OAAO,IAAIhD,OAAO;YAChBkB,QAAQC,GAAG,CAAC;QACd;QAEA,IAAIc,IAAIgB,kBAAkB,EAAE;YAC1B,IAAI,CAACxD,aAAa;gBAChB,IAAIO,OAAO;oBACTkB,QAAQC,GAAG,CAAC;gBACd;gBAEA1B,cAAc,IAAIyD,iBAAQ,CAAC;oBACzBC,KAAKlB,IAAIgB,kBAAkB;oBAC3BxB,QAAO,EAAE2B,KAAK,EAAE;4BAeXb;wBAdH,IAAI,CAACa,OAAO;4BACV,OAAO;wBACT,OAAO,IAAIA,MAAMC,IAAI,KAAK,YAAY;4BACpC,OAAOd,KAAKe,SAAS,CAACF,MAAMG,KAAK,EAAE9B,MAAM;wBAC3C,OAAO,IAAI2B,MAAMC,IAAI,KAAK,SAAS;4BACjC,MAAM,IAAIG,MAAM;wBAClB,OAAO,IAAIJ,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOd,KAAKe,SAAS,CAACF,MAAMK,IAAI,IAAI,IAAIhC,MAAM;wBAChD,OAAO,IAAI2B,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOD,MAAMM,IAAI,CAACjC,MAAM;wBAC1B;wBACA,wCAAwC;wBACxC,OACE2B,MAAMO,IAAI,CAAClC,MAAM,GAChBc,CAAAA,EAAAA,kBAAAA,KAAKe,SAAS,CAACF,MAAMC,IAAI,KAAK,UAAUD,MAAMQ,QAAQ,sBAAtDrB,gBACGd,MAAM,KAAI,CAAA;oBAElB;gBACF;YACF;QACF,OAAO;YACL,IAAIzB,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;QACF;IACF;IAEO0C,oBAA0B;QAC/BpE,+BAAAA,YAAaqE,KAAK;IACpB;IAEA,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAC3C,IAAIjE,OAAO;YACTkB,QAAQC,GAAG,CAAC,iBAAiB8C;QAC/B;QAEA,IAAI,CAACA,KAAKxC,MAAM,EAAE;QAElB,IAAIyC,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC,iBAAiB3B;YAC/B;YACA;QACF;QAEA,IAAI;YACF,MAAM4E,MAAM,MAAM/D,sBAChB,CAAC,EAAE,IAAI,CAAC2C,aAAa,CAAC,mCAAmC,EAAEiB,KACxDI,GAAG,CAAC,CAACvC,MAAQwC,mBAAmBxC,MAChCyC,IAAI,CAAC,KAAK,CAAC,EACd;gBACEC,QAAQ;gBACRnC,SAAS,IAAI,CAACA,OAAO;gBACrB,sCAAsC;gBACtCoC,MAAM;oBAAEC,UAAU;gBAAK;YACzB;YAGF,IAAIN,IAAIO,MAAM,KAAK,KAAK;gBACtB,MAAMC,aAAaR,IAAI/B,OAAO,CAACwC,GAAG,CAAC,kBAAkB;gBACrDrF,mBAAmB0E,KAAKC,GAAG,KAAKW,SAASF;YAC3C;YAEA,IAAI,CAACR,IAAIW,EAAE,EAAE;gBACX,MAAM,IAAIvB,MAAM,CAAC,2BAA2B,EAAEY,IAAIO,MAAM,CAAC,CAAC,CAAC;YAC7D;QACF,EAAE,OAAO1D,KAAK;YACZC,QAAQ8D,IAAI,CAAC,CAAC,yBAAyB,EAAEf,KAAK,CAAC,EAAEhD;QACnD;IACF;IAEA,MAAa4D,IAAI,GAAGb,IAAqC,EAAE;YAqBvDP;QApBF,MAAM,CAACwB,KAAKhD,MAAM,CAAC,CAAC,CAAC,GAAG+B;QACxB,MAAM,EAAEC,IAAI,EAAEiB,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,GAAGpD;QAEzD,IAAIkD,aAAa,SAAS;YACxB,OAAO;QACT;QAEA,IAAIjB,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;YACA,OAAO;QACT;QAEA,qDAAqD;QACrD,qDAAqD;QACrD,yBAAyB;QACzB,IAAIsC,OAAOhE,+BAAAA,YAAaoF,GAAG,CAACI;QAE5B,MAAMK,8BACJ7B,CAAAA,yBAAAA,cAAAA,KAAML,KAAK,qBAAXK,YAAaJ,IAAI,MAAK,WACtB,IAAI,CAAC/B,eAAe,CAAC2C,QAAQ,EAAE,EAAER,KAAKL,KAAK,CAACa,IAAI,IAAI,EAAE;QAExD,8DAA8D;QAC9D,gDAAgD;QAChD,IAAI,IAAI,CAACjB,aAAa,IAAK,CAAA,CAACS,QAAQ,CAAC6B,2BAA0B,GAAI;YACjE,IAAI;gBACF,MAAMC,QAAQrB,KAAKC,GAAG;gBACtB,MAAMqB,cAAoC;oBACxCd,UAAU;oBACVe,WAAW;oBACXJ,UAAUA;oBACVD;gBACF;gBACA,MAAMhB,MAAM,MAAMtD,MAChB,CAAC,EAAE,IAAI,CAACkC,aAAa,CAAC,mBAAmB,EAAEiC,IAAI,CAAC,EAChD;oBACET,QAAQ;oBACRnC,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACvC,uBAAuB,EAAEuF;wBAC1B,CAAC3F,kBAAkB,EAAEuE,CAAAA,wBAAAA,KAAMM,IAAI,CAAC,SAAQ;wBACxC,CAACmB,sCAA2B,CAAC,EAAER,CAAAA,4BAAAA,SAAUX,IAAI,CAAC,SAAQ;oBACxD;oBACAE,MAAMe;gBACR;gBAGF,IAAIpB,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAI/B,OAAO,CAACwC,GAAG,CAAC,kBAAkB;oBACrDrF,mBAAmB0E,KAAKC,GAAG,KAAKW,SAASF;gBAC3C;gBAEA,IAAIR,IAAIO,MAAM,KAAK,KAAK;oBACtB,IAAI3E,OAAO;wBACTkB,QAAQC,GAAG,CACT,CAAC,yBAAyB,EAAE8D,IAAI,YAAY,EAC1Cf,KAAKC,GAAG,KAAKoB,MACd,EAAE,CAAC;oBAER;oBACA,OAAO;gBACT;gBAEA,IAAI,CAACnB,IAAIW,EAAE,EAAE;oBACX7D,QAAQyE,KAAK,CAAC,MAAMvB,IAAIwB,IAAI;oBAC5B,MAAM,IAAIpC,MAAM,CAAC,4BAA4B,EAAEY,IAAIO,MAAM,CAAC,CAAC;gBAC7D;gBAEA,MAAMkB,SAAgC,MAAMzB,IAAI0B,IAAI;gBAEpD,IAAI,CAACD,UAAUA,OAAOxC,IAAI,KAAK,SAAS;oBACtCrD,SAASkB,QAAQC,GAAG,CAAC;wBAAE0E;oBAAO;oBAC9B,MAAM,IAAIrC,MAAM;gBAClB;gBAEA,oEAAoE;gBACpE,IAAIqC,OAAOxC,IAAI,KAAK,SAAS;oBAC3BwC,OAAO5B,IAAI,KAAK,EAAE;oBAClB,KAAK,MAAMnC,OAAOmC,QAAQ,EAAE,CAAE;wBAC5B,IAAI,CAAC4B,OAAO5B,IAAI,CAAC8B,QAAQ,CAACjE,MAAM;4BAC9B+D,OAAO5B,IAAI,CAAC+B,IAAI,CAAClE;wBACnB;oBACF;gBACF;gBAEA,MAAMmE,aAAa7B,IAAI/B,OAAO,CAACwC,GAAG,CAACjF;gBACnC,MAAMsG,MAAM9B,IAAI/B,OAAO,CAACwC,GAAG,CAAC;gBAE5BpB,OAAO;oBACLL,OAAOyC;oBACP,qDAAqD;oBACrD,uCAAuC;oBACvCM,cACEF,eAAe,UACX/B,KAAKC,GAAG,KAAKiC,yBAAc,GAC3BlC,KAAKC,GAAG,KAAKW,SAASoB,OAAO,KAAK,MAAM;gBAChD;gBAEA,IAAIlG,OAAO;oBACTkB,QAAQC,GAAG,CACT,CAAC,0BAA0B,EAAE8D,IAAI,YAAY,EAC3Cf,KAAKC,GAAG,KAAKoB,MACd,UAAU,EACTc,OAAOC,IAAI,CAACT,QAAQpE,MAAM,CAC3B,eAAe,EAAEwE,WAAW,OAAO,EAAEhC,wBAAAA,KAAMM,IAAI,CAC9C,KACA,WAAW,EAAEW,4BAAAA,SAAUX,IAAI,CAAC,KAAK,CAAC;gBAExC;gBAEA,IAAId,MAAM;oBACRhE,+BAAAA,YAAa8G,GAAG,CAACtB,KAAKxB;gBACxB;YACF,EAAE,OAAOxC,KAAK;gBACZ,sCAAsC;gBACtC,IAAIjB,OAAO;oBACTkB,QAAQyE,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAE1E;gBAClD;YACF;QACF;QAEA,OAAOwC,QAAQ;IACjB;IAEA,MAAa8C,IAAI,GAAGvC,IAAqC,EAAE;QACzD,MAAM,CAACiB,KAAKxB,MAAMxB,IAAI,GAAG+B;QAEzB,MAAMwC,WAAW/C,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,UAAUI,KAAKA,IAAI,GAAGgD;QACtD,MAAMC,gBAAgBjH,+BAAAA,YAAaoF,GAAG,CAACI;QACvC,MAAM0B,gBAAgBD,iCAAAA,cAAetD,KAAK;QAC1C,IACEuD,CAAAA,iCAAAA,cAAetD,IAAI,MAAK,WACxBgD,OAAOC,IAAI,CAACK,cAAclD,IAAI,EAAEmD,KAAK,CACnC,CAACC,QACCtE,KAAKe,SAAS,CACZ,AAACqD,cAAclD,IAAI,AAAoC,CAACoD,MAAM,MAEhEtE,KAAKe,SAAS,CAAC,AAACkD,QAA4C,CAACK,MAAM,IAEvE;YACA,IAAI7G,OAAO;gBACTkB,QAAQC,GAAG,CAAC,CAAC,uBAAuB,EAAE8D,IAAI,gBAAgB,CAAC;YAC7D;YACA;QACF;QAEA,MAAM,EAAE6B,UAAU,EAAE1B,QAAQ,EAAEC,QAAQ,EAAEpB,IAAI,EAAE,GAAGhC;QACjD,IAAI,CAAC6E,YAAY;QAEjB,IAAI5C,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;YACA;QACF;QAEA1B,+BAAAA,YAAa8G,GAAG,CAACtB,KAAK;YACpB7B,OAAOK;YACP0C,cAAcjC,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAACnB,aAAa,EAAE;YACtB,IAAI;gBACF,MAAMuC,QAAQrB,KAAKC,GAAG;gBACtB,IAAIV,SAAS,QAAQ,gBAAgBA,MAAM;oBACzC,IAAI,CAACpB,OAAO,CAACxC,wBAAwB,GAAG4D,KAAKsD,UAAU,CAACC,QAAQ;gBAClE;gBACA,IACE,CAAC,IAAI,CAAC3E,OAAO,CAACxC,wBAAwB,IACtC4D,SAAS,QACT,UAAUA,MACV;oBACA,IAAI,CAACpB,OAAO,CAACtC,2BAA2B,GACtC0D,KAAKA,IAAI,CAACpB,OAAO,CAAC,gBAAgB;gBACtC;gBACA,MAAMqB,OAAOnB,KAAKe,SAAS,CAAC;oBAC1B,GAAGG,IAAI;oBACP,yCAAyC;oBACzC,sBAAsB;oBACtBQ,MAAMwC;gBACR;gBAEA,IAAIzG,OAAO;oBACTkB,QAAQC,GAAG,CAAC,aAAa8D;gBAC3B;gBACA,MAAMO,cAAoC;oBACxCd,UAAU;oBACVe,WAAW;oBACXJ;oBACAD;gBACF;gBACA,MAAMhB,MAAM,MAAMtD,MAChB,CAAC,EAAE,IAAI,CAACkC,aAAa,CAAC,mBAAmB,EAAEiC,IAAI,CAAC,EAChD;oBACET,QAAQ;oBACRnC,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACvC,uBAAuB,EAAEuF,YAAY;wBACtC,CAAC3F,kBAAkB,EAAEuE,CAAAA,wBAAAA,KAAMM,IAAI,CAAC,SAAQ;oBAC1C;oBACAb,MAAMA;oBACNe,MAAMe;gBACR;gBAGF,IAAIpB,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAI/B,OAAO,CAACwC,GAAG,CAAC,kBAAkB;oBACrDrF,mBAAmB0E,KAAKC,GAAG,KAAKW,SAASF;gBAC3C;gBAEA,IAAI,CAACR,IAAIW,EAAE,EAAE;oBACX/E,SAASkB,QAAQC,GAAG,CAAC,MAAMiD,IAAIwB,IAAI;oBACnC,MAAM,IAAIpC,MAAM,CAAC,iBAAiB,EAAEY,IAAIO,MAAM,CAAC,CAAC;gBAClD;gBAEA,IAAI3E,OAAO;oBACTkB,QAAQC,GAAG,CACT,CAAC,oCAAoC,EAAE8D,IAAI,YAAY,EACrDf,KAAKC,GAAG,KAAKoB,MACd,UAAU,EAAE7B,KAAKjC,MAAM,CAAC,CAAC;gBAE9B;YACF,EAAE,OAAOR,KAAK;gBACZ,+BAA+B;gBAC/B,IAAIjB,OAAO;oBACTkB,QAAQyE,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE1E;gBAChD;YACF;QACF;QACA;IACF;AACF"}