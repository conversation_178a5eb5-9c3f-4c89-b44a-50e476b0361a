{"version": 3, "sources": ["../../../src/server/app-render/create-flight-router-state-from-loader-tree.ts"], "names": ["addSearchParamsIfPageSegment", "createFlightRouterStateFromLoaderTree", "segment", "searchParams", "isPageSegment", "PAGE_SEGMENT_KEY", "stringified<PERSON><PERSON>y", "JSON", "stringify", "parallelRoutes", "layout", "getDynamicParamFromSegment", "rootLayoutIncluded", "dynamicParam", "treeSegment", "segmentTree", "Object", "keys", "reduce", "existingValue", "currentValue"], "mappings": ";;;;;;;;;;;;;;;IAKgBA,4BAA4B;eAA5BA;;IAgBAC,qCAAqC;eAArCA;;;yBAlBiB;AAE1B,SAASD,6BACdE,OAAgB,EAChBC,YAAiB;IAEjB,MAAMC,gBAAgBF,YAAYG,yBAAgB;IAElD,IAAID,eAAe;QACjB,MAAME,mBAAmBC,KAAKC,SAAS,CAACL;QACxC,OAAOG,qBAAqB,OACxBJ,UAAU,MAAMI,mBAChBJ;IACN;IAEA,OAAOA;AACT;AAEO,SAASD,sCACd,CAACC,SAASO,gBAAgB,EAAEC,MAAM,EAAE,CAAa,EACjDC,0BAAsD,EACtDR,YAAiB,EACjBS,qBAAqB,KAAK;IAE1B,MAAMC,eAAeF,2BAA2BT;IAChD,MAAMY,cAAcD,eAAeA,aAAaC,WAAW,GAAGZ;IAE9D,MAAMa,cAAiC;QACrCf,6BAA6Bc,aAAaX;QAC1C,CAAC;KACF;IAED,IAAI,CAACS,sBAAsB,OAAOF,WAAW,aAAa;QACxDE,qBAAqB;QACrBG,WAAW,CAAC,EAAE,GAAG;IACnB;IAEAA,WAAW,CAAC,EAAE,GAAGC,OAAOC,IAAI,CAACR,gBAAgBS,MAAM,CACjD,CAACC,eAAeC;QACdD,aAAa,CAACC,aAAa,GAAGnB,sCAC5BQ,cAAc,CAACW,aAAa,EAC5BT,4BACAR,cACAS;QAEF,OAAOO;IACT,GACA,CAAC;IAGH,OAAOJ;AACT"}