{"version": 3, "sources": ["../../../src/server/app-render/encryption-utils.ts"], "names": ["arrayBufferToString", "decrypt", "encrypt", "generateEncryptionKeyBase64", "getActionEncryptionKey", "getClientReferenceManifestSingleton", "getServerModuleMap", "setReferenceManifestsSingleton", "stringToUint8Array", "__next_encryption_key_generation_promise", "__next_loaded_action_key", "__next_internal_development_raw_action_key", "buffer", "bytes", "Uint8Array", "len", "byteLength", "String", "fromCharCode", "apply", "binary", "i", "length", "arr", "charCodeAt", "key", "iv", "data", "crypto", "subtle", "name", "dev", "Promise", "resolve", "reject", "<PERSON><PERSON>ey", "exported", "exportKey", "b64", "btoa", "error", "SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "globalThis", "serverActionsManifestSingleton", "Error", "<PERSON><PERSON><PERSON>", "process", "env", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON>", "undefined", "importKey", "atob"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAWgBA,mBAAmB;eAAnBA;;IAwCAC,OAAO;eAAPA;;IAXAC,OAAO;eAAPA;;IAsBMC,2BAA2B;eAA3BA;;IAiHAC,sBAAsB;eAAtBA;;IAjBNC,mCAAmC;eAAnCA;;IAtBAC,kBAAkB;eAAlBA;;IAvBAC,8BAA8B;eAA9BA;;IApFAC,kBAAkB;eAAlBA;;;AA1BhB,wFAAwF;AACxF,mCAAmC;AACnC,IAAIC,2CAEO;AACX,IAAIC;AACJ,IAAIC;AAEG,SAASX,oBAAoBY,MAAmB;IACrD,MAAMC,QAAQ,IAAIC,WAAWF;IAC7B,MAAMG,MAAMF,MAAMG,UAAU;IAE5B,6DAA6D;IAC7D,mCAAmC;IACnC,4EAA4E;IAC5E,IAAID,MAAM,OAAO;QACf,OAAOE,OAAOC,YAAY,CAACC,KAAK,CAAC,MAAMN;IACzC;IAEA,IAAIO,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BD,UAAUH,OAAOC,YAAY,CAACL,KAAK,CAACQ,EAAE;IACxC;IACA,OAAOD;AACT;AAEO,SAASZ,mBAAmBY,MAAc;IAC/C,MAAML,MAAMK,OAAOE,MAAM;IACzB,MAAMC,MAAM,IAAIT,WAAWC;IAE3B,IAAK,IAAIM,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BE,GAAG,CAACF,EAAE,GAAGD,OAAOI,UAAU,CAACH;IAC7B;IAEA,OAAOE;AACT;AAEO,SAASrB,QAAQuB,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAAC3B,OAAO,CAC1B;QACE4B,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEO,SAAS1B,QAAQwB,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAAC5B,OAAO,CAC1B;QACE6B,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEO,eAAexB,4BAA4B4B,GAAa;IAC7D,mEAAmE;IACnE,4BAA4B;IAC5B,IAAIA,KAAK;QACP,IAAI,OAAOpB,+CAA+C,aAAa;YACrE,OAAOA;QACT;IACF;IAEA,6DAA6D;IAC7D,IAAI,CAACF,0CAA0C;QAC7CA,2CAA2C,IAAIuB,QAC7C,OAAOC,SAASC;YACd,IAAI;gBACF,MAAMT,MAAM,MAAMG,OAAOC,MAAM,CAACM,WAAW,CACzC;oBACEL,MAAM;oBACNR,QAAQ;gBACV,GACA,MACA;oBAAC;oBAAW;iBAAU;gBAExB,MAAMc,WAAW,MAAMR,OAAOC,MAAM,CAACQ,SAAS,CAAC,OAAOZ;gBACtD,MAAMa,MAAMC,KAAKvC,oBAAoBoC;gBAErCH,QAAQ;oBAACR;oBAAKa;iBAAI;YACpB,EAAE,OAAOE,OAAO;gBACdN,OAAOM;YACT;QACF;IAEJ;IAEA,MAAM,CAACf,KAAKa,IAAI,GAAG,MAAM7B;IAEzBC,2BAA2Be;IAC3B,IAAIM,KAAK;QACPpB,6CAA6C2B;IAC/C;IAEA,OAAOA;AACT;AAEA,sFAAsF;AACtF,wFAAwF;AACxF,4FAA4F;AAC5F,cAAc;AACd,MAAMG,oCAAoCC,OAAOC,GAAG,CAClD;AAGK,SAASpC,+BAA+B,EAC7CqC,uBAAuB,EACvBC,qBAAqB,EACrBC,eAAe,EAWhB;IACC,aAAa;IACbC,UAAU,CAACN,kCAAkC,GAAG;QAC9CG;QACAC;QACAC;IACF;AACF;AAEO,SAASxC;IACd,MAAM0C,iCAAiC,AAACD,UAAkB,CACxDN,kCACD;IAUD,IAAI,CAACO,gCAAgC;QACnC,MAAM,IAAIC,MACR;IAEJ;IAEA,OAAOD,+BAA+BF,eAAe;AACvD;AAEO,SAASzC;IACd,MAAM2C,iCAAiC,AAACD,UAAkB,CACxDN,kCACD;IAKD,IAAI,CAACO,gCAAgC;QACnC,MAAM,IAAIC,MACR;IAEJ;IAEA,OAAOD,+BAA+BJ,uBAAuB;AAC/D;AAEO,eAAexC;IACpB,IAAIM,0BAA0B;QAC5B,OAAOA;IACT;IAEA,MAAMsC,iCAAiC,AAACD,UAAkB,CACxDN,kCACD;IAKD,IAAI,CAACO,gCAAgC;QACnC,MAAM,IAAIC,MACR;IAEJ;IAEA,MAAMC,SACJC,QAAQC,GAAG,CAACC,kCAAkC,IAC9CL,+BAA+BH,qBAAqB,CAACS,aAAa;IAEpE,IAAIJ,WAAWK,WAAW;QACxB,MAAM,IAAIN,MAAM;IAClB;IAEAvC,2BAA2B,MAAMkB,OAAOC,MAAM,CAAC2B,SAAS,CACtD,OACAhD,mBAAmBiD,KAAKP,UACxB,WACA,MACA;QAAC;QAAW;KAAU;IAGxB,OAAOxC;AACT"}