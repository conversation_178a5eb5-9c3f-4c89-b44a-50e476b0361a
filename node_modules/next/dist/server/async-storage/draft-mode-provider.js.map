{"version": 3, "sources": ["../../../src/server/async-storage/draft-mode-provider.ts"], "names": ["DraftModeProvider", "constructor", "previewProps", "req", "cookies", "mutableCookies", "isOnDemandRevalidate", "checkIsOnDemandRevalidate", "cookieValue", "get", "COOKIE_NAME_PRERENDER_BYPASS", "value", "isEnabled", "Boolean", "previewModeId", "_previewModeId", "_mutableCookies", "enable", "Error", "set", "name", "httpOnly", "sameSite", "process", "env", "NODE_ENV", "secure", "path", "disable", "expires", "Date"], "mappings": ";;;;+BAYaA;;;eAAAA;;;0BAHN;AAGA,MAAMA;IAaXC,YACEC,YAA2C,EAC3CC,GAA6D,EAC7DC,OAA+B,EAC/BC,cAA+B,CAC/B;YAOoBD;QANpB,mEAAmE;QACnE,4DAA4D;QAC5D,MAAME,uBACJJ,gBACAK,IAAAA,mCAAyB,EAACJ,KAAKD,cAAcI,oBAAoB;QAEnE,MAAME,eAAcJ,eAAAA,QAAQK,GAAG,CAACC,sCAA4B,sBAAxCN,aAA2CO,KAAK;QAEpE,IAAI,CAACC,SAAS,GAAGC,QACf,CAACP,wBACCE,eACAN,gBACAM,gBAAgBN,aAAaY,aAAa;QAG9C,IAAI,CAACC,cAAc,GAAGb,gCAAAA,aAAcY,aAAa;QACjD,IAAI,CAACE,eAAe,GAAGX;IACzB;IAEAY,SAAS;QACP,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;YACxB,MAAM,IAAIG,MACR;QAEJ;QAEA,IAAI,CAACF,eAAe,CAACG,GAAG,CAAC;YACvBC,MAAMV,sCAA4B;YAClCC,OAAO,IAAI,CAACI,cAAc;YAC1BM,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCE,MAAM;QACR;IACF;IAEAC,UAAU;QACR,2DAA2D;QAC3D,oDAAoD;QACpD,wEAAwE;QACxE,IAAI,CAACZ,eAAe,CAACG,GAAG,CAAC;YACvBC,MAAMV,sCAA4B;YAClCC,OAAO;YACPU,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCE,MAAM;YACNE,SAAS,IAAIC,KAAK;QACpB;IACF;AACF"}