{"version": 3, "sources": ["../../../src/build/templates/edge-ssr-app.ts"], "names": ["ComponentMod", "nH<PERSON><PERSON>", "self", "Document", "appMod", "errorMod", "error500Mod", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "__BUILD_MANIFEST", "prerenderManifest", "__PRERENDER_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "subresourceIntegrityManifest", "sriEnabled", "__SUBRESOURCE_INTEGRITY_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "setReferenceManifestsSingleton", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "createServerModuleMap", "pageName", "render", "getRender", "pagesType", "PAGE_TYPES", "APP", "dev", "page", "pageMod", "renderToHTML", "isServerComponent", "serverActions", "config", "nextConfig", "buildId", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "opts", "adapter", "IncrementalCache", "handler"], "mappings": ";;;;;;;;;;;;;;;IAsFaA,YAAY;eAAZA;;IAEb,OAMC;eANuBC;;;QAxFjB;yBACiB;wBACE;kCACO;2BAEoB;sEAC5B;2BAME;iCACoB;6BACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2BlBC;AAxBpB,0CAA0C;AAE1C,MAAMC,WAAyB;AAC/B,MAAMC,SAAS;AACf,MAAMC,WAAW;AACjB,MAAMC,cAAc;AAQpB,oBAAoB;AACpB,2BAA2B;AAC3B,aAAa;AACb,uBAAuB;AACvB,oBAAoB;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,gBAA+BV,KAAKW,gBAAgB;AAC1D,MAAMC,oBAAoBP,eAAeL,KAAKa,oBAAoB;AAClE,MAAMC,wBAAwBT,eAAeL,KAAKe,yBAAyB;AAC3E,MAAMC,eAAchB,uBAAAA,KAAKiB,cAAc,qBAAnBjB,oBAAqB,CAAC,WAAW;AACrD,MAAMkB,oBAAoBb,eAAeL,KAAKmB,qBAAqB;AACnE,MAAMC,+BAA+BC,aACjChB,eAAeL,KAAKsB,gCAAgC,IACpDb;AACJ,MAAMc,mBAAmBlB,eAAeL,KAAKwB,oBAAoB;AAEjE,MAAMC,4BACJpB,eAAeL,KAAK0B,qCAAqC,KAAK,EAAE;AAElE,IAAIV,eAAeE,mBAAmB;IACpCS,IAAAA,+CAA8B,EAAC;QAC7BC,yBAAyBZ;QACzBa,uBAAuBX;QACvBY,iBAAiBC,IAAAA,kCAAqB,EAAC;YACrCF,uBAAuBX;YACvBc,UAAU;QACZ;IACF;AACF;AAEA,MAAMC,SAASC,IAAAA,iBAAS,EAAC;IACvBC,WAAWC,qBAAU,CAACC,GAAG;IACzBC;IACAC,MAAM;IACNrC;IACAsC,SAAAA;IACArC;IACAC;IACAH;IACAS;IACAE;IACA6B,cAAAA,+BAAY;IACZ3B;IACAc,yBAAyBc,oBAAoB1B,cAAc;IAC3Da,uBAAuBa,oBAAoBxB,oBAAoB;IAC/DyB,eAAeD,oBAAoBC,gBAAgBlC;IACnDW;IACAwB,QAAQC;IACRC,SAAS;IACTvB;IACAwB;IACAtB;AACF;AAEO,MAAM3B,eAAe0C;AAEb,SAASzC,SAASiD,IAA4C;IAC3E,OAAOC,IAAAA,gBAAO,EAAC;QACb,GAAGD,IAAI;QACPE,kBAAAA,kCAAgB;QAChBC,SAASlB;IACX;AACF"}