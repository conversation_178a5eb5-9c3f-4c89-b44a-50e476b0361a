{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "names": ["isResourceInPackages", "makeExternalHandler", "resolveExternal", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "nodeModulesRegex", "containsImportInPackages", "request", "packages", "some", "pkg", "startsWith", "resource", "packageNames", "packageDirMapping", "p", "has", "get", "path", "sep", "includes", "join", "replace", "dir", "esmExternalsConfig", "context", "isEsmRequested", "optOutBundlingPackages", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "NODE_ESM_RESOLVE_OPTIONS", "nodeResolveOptions", "NODE_RESOLVE_OPTIONS", "baseEsmResolveOptions", "NODE_BASE_ESM_RESOLVE_OPTIONS", "baseResolveOptions", "NODE_BASE_RESOLVE_OPTIONS", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "preferEsm", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "config", "optOutBundlingPackageRegex", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "isWebpackAppLayer", "test", "notExternalModules", "BARREL_OPTIMIZATION_PREFIX", "isWebpackServerOnlyLayer", "resolveNextExternal", "WEBPACK_LAYERS", "serverSideRendering", "isRelative", "fullRequest", "normalizePathSep", "resolveResult", "undefined", "defaultOverrides", "Error", "externalType", "transpilePackages", "Map", "pkgRes", "set", "dirname", "resolvedBundlingOptOutRes", "resolveBundlingOptOutPackages", "resolvedRes", "shouldBeBundled", "bundlePagesExternals", "isOptOutBundling", "isExternal"], "mappings": ";;;;;;;;;;;;;;;;IAoCgBA,oBAAoB;eAApBA;;IAiHAC,mBAAmB;eAAnBA;;IAhGMC,eAAe;eAAfA;;;2BArDS;6BAEE;4BACU;6DAC1B;+BAMV;uBACqD;kCAE3B;;;;;;AACjC,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,CAAC,EAAEF,SAAS,EAAEF,gBAAgB,EAAE,EAAEC,gBAAgB,CAAC;AAGrD,MAAMI,mBAAmB;AAEzB,SAASC,yBACPC,OAAe,EACfC,QAAkB;IAElB,OAAOA,SAASC,IAAI,CAClB,CAACC,MAAQH,YAAYG,OAAOH,QAAQI,UAAU,CAACD,MAAM;AAEzD;AAEO,SAASf,qBACdiB,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,IAAI,CAACD,cAAc,OAAO;IAC1B,OAAOA,aAAaJ,IAAI,CAAC,CAACM,IACxBD,qBAAqBA,kBAAkBE,GAAG,CAACD,KACvCH,SAASD,UAAU,CAACG,kBAAkBG,GAAG,CAACF,KAAMG,aAAI,CAACC,GAAG,IACxDP,SAASQ,QAAQ,CACfF,aAAI,CAACC,GAAG,GACND,aAAI,CAACG,IAAI,CAAC,gBAAgBN,EAAEO,OAAO,CAAC,OAAOJ,aAAI,CAACC,GAAG,KACnDD,aAAI,CAACC,GAAG;AAGpB;AAEO,eAAetB,gBACpB0B,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACflB,OAAe,EACfmB,cAAuB,EACvBC,sBAAgC,EAChCC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBC,uCAAwB,EACjDC,qBAA0BC,mCAAoB,EAC9CC,wBAA6BC,4CAA6B,EAC1DC,qBAA0BC,wCAAyB;IAEnD,MAAMC,eAAe,CAAC,CAACf;IACvB,MAAMgB,oBAAoBhB,uBAAuB;IAEjD,IAAIiB,MAAqB;IACzB,IAAIC,QAAiB;IAErB,MAAMC,mBACJJ,gBACAb,kBACA,mEAAmE;IACnE,2EAA2E;IAC3E,2EAA2E;IAC3E,CAACpB,yBAAyBC,SAASoB,0BAC/B;QAAC;QAAM;KAAM,GACb;QAAC;KAAM;IAEb,KAAK,MAAMiB,aAAaD,iBAAkB;QACxC,MAAME,UAAUjB,WACdgB,YAAYb,oBAAoBE;QAGlC,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;YACD,CAACQ,KAAKC,MAAM,GAAG,MAAMG,QAAQpB,SAASlB;QACzC,EAAE,OAAOuC,KAAK;YACZL,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACf,kBAAkBgB,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIX,iBAAiB;YACnB,OAAO;gBAAEkB,UAAUlB,gBAAgBY;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIX,kBAAkB;YACpB,IAAIkB;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAActB,WAClBc,QAAQP,wBAAwBE;gBAEjC,CAACW,SAASC,UAAU,GAAG,MAAMC,YAAY3B,KAAKhB;YACjD,EAAE,OAAOuC,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,2EAA2E;YAC3E,wDAAwD;YACxD,IAAID,YAAYP,OAAOC,UAAUO,WAAW;gBAC1CR,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEO,SAAS9C,oBAAoB,EAClCuD,MAAM,EACNxB,sBAAsB,EACtByB,0BAA0B,EAC1B7B,GAAG,EAMJ;QAE2B4B;IAD1B,IAAIE;IACJ,MAAMb,oBAAoBW,EAAAA,uBAAAA,OAAOG,YAAY,qBAAnBH,qBAAqBZ,YAAY,MAAK;IAEhE,OAAO,eAAegB,gBACpB9B,OAAe,EACflB,OAAe,EACfiD,cAAsB,EACtBC,KAA8B,EAC9B7B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAM8B,UACJnD,QAAQI,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBO,aAAI,CAACyC,KAAK,CAACC,UAAU,CAACrD,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBsD,QAAQC,QAAQ,KAAK,WAAW5C,aAAI,CAAC6C,KAAK,CAACH,UAAU,CAACrD;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMyD,aAAaC,IAAAA,wBAAiB,EAACR;QAErC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,aAAaQ,IAAI,CAAC3D,UAAU;gBAC9B,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IAAIT,mBAAmBoE,IAAI,CAAC3D,YAAY,CAACyD,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAEzD,QAAQ,CAAC;YAC9B;YAEA,MAAM4D,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAAC3D,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQa,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIb,QAAQI,UAAU,CAACyD,sCAA0B,GAAG;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAM1C,iBAAiB8B,mBAAmB;QAE1C,4DAA4D;QAC5D,yFAAyF;QACzF,4DAA4D;QAC5D,IACEa,IAAAA,+BAAwB,EAACZ,UACzBlD,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQI,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqDuD,IAAI,CAAC3D,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8C2D,IAAI,CAAC3D,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,8DAA8D2D,IAAI,CAChE3D,YAEF,4CAA4C2D,IAAI,CAAC3D,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,sEAAsE2D,IAAI,CACxE3D,YAEF,2CAA2C2D,IAAI,CAAC3D,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;YAC5B;YAEA,OAAO+D,oBAAoB/D;QAC7B;QAEA,gFAAgF;QAChF,qEAAqE;QACrE,oDAAoD;QACpD,gEAAgE;QAChE,IAAIkD,UAAUc,yBAAc,CAACC,mBAAmB,EAAE;YAChD,MAAMC,aAAalE,QAAQI,UAAU,CAAC;YACtC,MAAM+D,cAAcD,aAChBE,IAAAA,kCAAgB,EAACzD,aAAI,CAACG,IAAI,CAACI,SAASlB,YACpCA;YAEJ,+CAA+C;YAC/C,IAAID,yBAAyBoE,aAAa/C,yBAAyB;gBACjE,OAAO+C;YACT;YACA,OAAOJ,oBAAoBI;QAC7B;QAEA,6FAA6F;QAC7F,MAAME,gBAAgB,MAAM/E,gBAC1B0B,KACA4B,OAAOG,YAAY,CAACf,YAAY,EAChCd,SACAlB,SACAmB,gBACAC,wBACAC,YACA8B,UAAUY,sBAAsBO;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAc7B,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAIxC,YAAY,oBAAoB;YAClCqE,cAAcnC,GAAG,GAAGqC,6BAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAErC,GAAG,EAAEC,KAAK,EAAE,GAAGkC;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAACnC,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACf,kBAAkBgB,SAAS,CAACF,qBAAqB,CAACkB,SAAS;YAC9D,MAAM,IAAIqB,MACR,CAAC,cAAc,EAAExE,QAAQ,2HAA2H,CAAC;QAEzJ;QAEA,MAAMyE,eAAetC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2CwB,IAAI,CAACzB,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2ByB,IAAI,CAACzB,QAChC,8BAA8ByB,IAAI,CAACzB,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIU,OAAO8B,iBAAiB,IAAI,CAAC5B,6BAA6B;YAC5DA,8BAA8B,IAAI6B;YAClC,8DAA8D;YAC9D,KAAK,MAAMxE,OAAOyC,OAAO8B,iBAAiB,CAAE;gBAC1C,MAAME,SAAS,MAAMtF,gBACnB0B,KACA4B,OAAOG,YAAY,CAACf,YAAY,EAChCd,SACAf,MAAM,iBACNgB,gBACAC,wBACAC,YACA8B,UAAUY,sBAAsBO;gBAElC,IAAIM,OAAO1C,GAAG,EAAE;oBACdY,4BAA4B+B,GAAG,CAAC1E,KAAKQ,aAAI,CAACmE,OAAO,CAACF,OAAO1C,GAAG;gBAC9D;YACF;QACF;QAEA,MAAM6C,4BAA4BC,8BAA8B;YAC9DC,aAAa/C;YACbW;YACAD;YACAE;YACAX;YACAsB;YACAP;YACAuB;YACAzE;QACF;QACA,IAAI+E,2BAA2B;YAC7B,OAAOA;QACT;QAEA,2CAA2C;QAC3C;IACF;AACF;AAEA,SAASC,8BAA8B,EACrCC,WAAW,EACXpC,0BAA0B,EAC1BD,MAAM,EACNE,2BAA2B,EAC3BX,KAAK,EACLsB,UAAU,EACVP,KAAK,EACLuB,YAAY,EACZzE,OAAO,EAWR;IACC,MAAMkF,kBACJ9F,qBACE6F,aACArC,OAAO8B,iBAAiB,EACxB5B,gCAEDX,SAASsB,cACT,CAACA,cAAcb,OAAOG,YAAY,CAACoC,oBAAoB;IAE1D,IAAIrF,iBAAiB6D,IAAI,CAACsB,cAAc;QACtC,MAAMG,mBAAmBvC,2BAA2Bc,IAAI,CAACsB;QACzD,IAAInB,IAAAA,+BAAwB,EAACZ,QAAQ;YACnC,IAAIkC,kBAAkB;gBACpB,OAAO,CAAC,EAAEX,aAAa,CAAC,EAAEzE,QAAQ,CAAC,CAAC,2BAA2B;;YACjE;QACF,OAAO,IAAI,CAACkF,mBAAmBE,kBAAkB;YAC/C,OAAO,CAAC,EAAEX,aAAa,CAAC,EAAEzE,QAAQ,CAAC,CAAC,0CAA0C;;QAChF;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS+D,oBAAoBvB,QAAgB;IAC3C,MAAM6C,aAAazF,gBAAgB+D,IAAI,CAACnB;IAExC,sFAAsF;IACtF,sGAAsG;IACtG,IAAI6C,YAAY;QACd,oGAAoG;QACpG,oCAAoC;QACpC,OAAO,CAAC,SAAS,EAAEjB,IAAAA,kCAAgB,EACjC5B,SAASzB,OAAO,CAAC,oBAAoB,cACrC,CAAC;IACL;AACF"}