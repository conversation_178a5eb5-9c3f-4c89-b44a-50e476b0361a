{"version": 3, "sources": ["../../../src/build/webpack-build/impl.ts"], "names": ["webpackBuildImpl", "worker<PERSON>ain", "debug", "origDebug", "isTelemetryPlugin", "plugin", "TelemetryPlugin", "isTraceEntryPointsPlugin", "TraceEntryPointsPlugin", "compilerName", "result", "warnings", "errors", "stats", "webpackBuildStart", "nextBuildSpan", "NextBuildContext", "dir", "config", "runWebpackSpan", "<PERSON><PERSON><PERSON><PERSON>", "entrypoints", "traceAsyncFn", "createEntrypoints", "buildId", "envFiles", "loadedEnvFiles", "isDev", "rootDir", "pageExtensions", "pagesDir", "appDir", "pages", "mappedPages", "appPaths", "mappedAppPages", "previewMode", "previewProps", "rootPaths", "mappedRootPaths", "hasInstrumentationHook", "commonWebpackOptions", "isServer", "<PERSON><PERSON><PERSON>", "rewrites", "originalRewrites", "originalRedirects", "reactProductionProfiling", "noMangling", "clientRouterFilters", "previewModeId", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "configs", "info", "loadProjectInfo", "dev", "Promise", "all", "getBaseWebpackConfig", "middlewareMatchers", "compilerType", "COMPILER_NAMES", "client", "server", "edgeServer", "edgePreviewProps", "clientConfig", "serverConfig", "edgeConfig", "optimization", "minimize", "minimizer", "length", "Log", "warn", "process", "hrtime", "inputFileSystem", "clientResult", "serverResult", "edgeServerResult", "start", "Date", "now", "runCompiler", "pluginState", "getPluginState", "key", "injectedClientEntries", "value", "clientEntry", "entry", "APP_CLIENT_INTERNALS", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "import", "layer", "WEBPACK_LAYERS", "appPagesBrowser", "dependOn", "purge", "filter", "nonNullable", "traceFn", "formatWebpackMessages", "telemetryPlugin", "plugins", "find", "traceEntryPointsPlugin", "webpackBuildEnd", "error", "Boolean", "join", "console", "red", "indexOf", "page_name_regex", "parsed", "exec", "page_name", "groups", "Error", "err", "code", "event", "duration", "buildTraceContext", "telemetryState", "usages", "packagesUsedInServerSideProps", "workerData", "telemetry", "Telemetry", "distDir", "buildContext", "setGlobal", "Object", "assign", "initializeTraceState", "traceState", "resumePluginState", "loadConfig", "PHASE_PRODUCTION_BUILD", "trace", "entriesTrace", "chunksTrace", "entryNameMap", "depModArray", "entryEntries", "entryNameFilesMap", "stop", "debugTraceEvents", "getTraceEvents"], "mappings": ";;;;;;;;;;;;;;;IAkEsBA,gBAAgB;eAAhBA;;IAsRAC,UAAU;eAAVA;;;4BAvVF;8EACc;6BACN;2BAOrB;0BACqB;6DACP;uEACiC;iCAK/C;8BAKA;yBAC2B;+DACX;uBAQhB;4BACwB;4CACQ;8DAIjB;yBACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1B,MAAMC,QAAQC,IAAAA,cAAS,EAAC;AAcxB,SAASC,kBAAkBC,MAAe;IACxC,OAAOA,kBAAkBC,gCAAe;AAC1C;AAEA,SAASC,yBACPF,MAAe;IAEf,OAAOA,kBAAkBG,kDAAsB;AACjD;AAEO,eAAeR,iBACpBS,YAAkD;QA8M1B,uBAIO;IA3M/B,IAAIC,SAAgC;QAClCC,UAAU,EAAE;QACZC,QAAQ,EAAE;QACVC,OAAO,EAAE;IACX;IACA,IAAIC;IACJ,MAAMC,gBAAgBC,8BAAgB,CAACD,aAAa;IACpD,MAAME,MAAMD,8BAAgB,CAACC,GAAG;IAChC,MAAMC,SAASF,8BAAgB,CAACE,MAAM;IAEtC,MAAMC,iBAAiBJ,cAAcK,UAAU,CAAC;IAChD,MAAMC,cAAc,MAAMN,cACvBK,UAAU,CAAC,sBACXE,YAAY,CAAC,IACZC,IAAAA,0BAAiB,EAAC;YAChBC,SAASR,8BAAgB,CAACQ,OAAO;YACjCN,QAAQA;YACRO,UAAUT,8BAAgB,CAACU,cAAc;YACzCC,OAAO;YACPC,SAASX;YACTY,gBAAgBX,OAAOW,cAAc;YACrCC,UAAUd,8BAAgB,CAACc,QAAQ;YACnCC,QAAQf,8BAAgB,CAACe,MAAM;YAC/BC,OAAOhB,8BAAgB,CAACiB,WAAW;YACnCC,UAAUlB,8BAAgB,CAACmB,cAAc;YACzCC,aAAapB,8BAAgB,CAACqB,YAAY;YAC1CC,WAAWtB,8BAAgB,CAACuB,eAAe;YAC3CC,wBAAwBxB,8BAAgB,CAACwB,sBAAsB;QACjE;IAGJ,MAAMC,uBAAuB;QAC3BC,UAAU;QACVlB,SAASR,8BAAgB,CAACQ,OAAO;QACjCmB,eAAe3B,8BAAgB,CAAC2B,aAAa;QAC7CzB,QAAQA;QACRa,QAAQf,8BAAgB,CAACe,MAAM;QAC/BD,UAAUd,8BAAgB,CAACc,QAAQ;QACnCc,UAAU5B,8BAAgB,CAAC4B,QAAQ;QACnCC,kBAAkB7B,8BAAgB,CAAC6B,gBAAgB;QACnDC,mBAAmB9B,8BAAgB,CAAC8B,iBAAiB;QACrDC,0BAA0B/B,8BAAgB,CAAC+B,wBAAwB;QACnEC,YAAYhC,8BAAgB,CAACgC,UAAU;QACvCC,qBAAqBjC,8BAAgB,CAACiC,mBAAmB;QACzDC,eAAelC,8BAAgB,CAACkC,aAAa;QAC7CC,6BAA6BnC,8BAAgB,CAACmC,2BAA2B;QACzEC,qBAAqBpC,8BAAgB,CAACoC,mBAAmB;IAC3D;IAEA,MAAMC,UAAU,MAAMlC,eACnBC,UAAU,CAAC,2BACXE,YAAY,CAAC;QACZ,MAAMgC,OAAO,MAAMC,IAAAA,8BAAe,EAAC;YACjCtC;YACAC,QAAQuB,qBAAqBvB,MAAM;YACnCsC,KAAK;QACP;QACA,OAAOC,QAAQC,GAAG,CAAC;YACjBC,IAAAA,sBAAoB,EAAC1C,KAAK;gBACxB,GAAGwB,oBAAoB;gBACvBmB,oBAAoBvC,YAAYuC,kBAAkB;gBAClDzC;gBACA0C,cAAcC,yBAAc,CAACC,MAAM;gBACnC1C,aAAaA,YAAY0C,MAAM;gBAC/B,GAAGT,IAAI;YACT;YACAK,IAAAA,sBAAoB,EAAC1C,KAAK;gBACxB,GAAGwB,oBAAoB;gBACvBtB;gBACAyC,oBAAoBvC,YAAYuC,kBAAkB;gBAClDC,cAAcC,yBAAc,CAACE,MAAM;gBACnC3C,aAAaA,YAAY2C,MAAM;gBAC/B,GAAGV,IAAI;YACT;YACAK,IAAAA,sBAAoB,EAAC1C,KAAK;gBACxB,GAAGwB,oBAAoB;gBACvBtB;gBACAyC,oBAAoBvC,YAAYuC,kBAAkB;gBAClDC,cAAcC,yBAAc,CAACG,UAAU;gBACvC5C,aAAaA,YAAY4C,UAAU;gBACnCC,kBAAkBlD,8BAAgB,CAACqB,YAAY;gBAC/C,GAAGiB,IAAI;YACT;SACD;IACH;IAEF,MAAMa,eAAed,OAAO,CAAC,EAAE;IAC/B,MAAMe,eAAef,OAAO,CAAC,EAAE;IAC/B,MAAMgB,aAAahB,OAAO,CAAC,EAAE;IAE7B,IACEc,aAAaG,YAAY,IACxBH,CAAAA,aAAaG,YAAY,CAACC,QAAQ,KAAK,QACrCJ,aAAaG,YAAY,CAACE,SAAS,IAClCL,aAAaG,YAAY,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,GACpD;QACAC,KAAIC,IAAI,CACN,CAAC,iIAAiI,CAAC;IAEvI;IAEA7D,oBAAoB8D,QAAQC,MAAM;IAElC3E,MAAM,CAAC,iBAAiB,CAAC,EAAEO;IAC3B,+EAA+E;IAC/E,MAAMU,eAAeG,YAAY,CAAC;YAsEhCwD;QArEA,qDAAqD;QACrD,8DAA8D;QAC9D,IAAIC,eAA4C;QAEhD,uEAAuE;QACvE,yEAAyE;QACzE,IAAIC,eACF;QACF,IAAIC,mBAEO;QAEX,IAAIH;QAEJ,IAAI,CAACrE,gBAAgBA,iBAAiB,UAAU;YAC9CP,MAAM;YACN,MAAMgF,QAAQC,KAAKC,GAAG;YACrB,CAACJ,cAAcF,gBAAgB,GAAG,MAAMO,IAAAA,qBAAW,EAACjB,cAAc;gBACjEjD;gBACA2D;YACF;YACA5E,MAAM,CAAC,yBAAyB,EAAEiF,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC1D;QAEA,IAAI,CAACzE,gBAAgBA,iBAAiB,eAAe;YACnDP,MAAM;YACN,MAAMgF,QAAQC,KAAKC,GAAG;YACrB,CAACH,kBAAkBH,gBAAgB,GAAGT,aACnC,MAAMgB,IAAAA,qBAAW,EAAChB,YAAY;gBAAElD;gBAAgB2D;YAAgB,KAChE;gBAAC;aAAK;YACV5E,MAAM,CAAC,8BAA8B,EAAEiF,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC/D;QAEA,wCAAwC;QACxC,IAAI,EAACF,gCAAAA,aAAcpE,MAAM,CAAC6D,MAAM,KAAI,EAACQ,oCAAAA,iBAAkBrE,MAAM,CAAC6D,MAAM,GAAE;YACpE,MAAMa,cAAcC,IAAAA,4BAAc;YAClC,IAAK,MAAMC,OAAOF,YAAYG,qBAAqB,CAAE;gBACnD,MAAMC,QAAQJ,YAAYG,qBAAqB,CAACD,IAAI;gBACpD,MAAMG,cAAcxB,aAAayB,KAAK;gBACtC,IAAIJ,QAAQK,+BAAoB,EAAE;oBAChCF,WAAW,CAACG,+CAAoC,CAAC,GAAG;wBAClDC,QAAQ;4BACN,6HAA6H;4BAC7H,oFAAoF;+BACjFJ,WAAW,CAACG,+CAAoC,CAAC,CAACC,MAAM;4BAC3DL;yBACD;wBACDM,OAAOC,0BAAc,CAACC,eAAe;oBACvC;gBACF,OAAO;oBACLP,WAAW,CAACH,IAAI,GAAG;wBACjBW,UAAU;4BAACL,+CAAoC;yBAAC;wBAChDC,QAAQL;wBACRM,OAAOC,0BAAc,CAACC,eAAe;oBACvC;gBACF;YACF;YAEA,IAAI,CAACzF,gBAAgBA,iBAAiB,UAAU;gBAC9CP,MAAM;gBACN,MAAMgF,QAAQC,KAAKC,GAAG;gBACrB,CAACL,cAAcD,gBAAgB,GAAG,MAAMO,IAAAA,qBAAW,EAAClB,cAAc;oBACjEhD;oBACA2D;gBACF;gBACA5E,MAAM,CAAC,yBAAyB,EAAEiF,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;YAC1D;QACF;QAEAJ,oCAAAA,yBAAAA,gBAAiBsB,KAAK,qBAAtBtB,4BAAAA;QAEApE,SAAS;YACPC,UAAU;mBACJoE,CAAAA,gCAAAA,aAAcpE,QAAQ,KAAI,EAAE;mBAC5BqE,CAAAA,gCAAAA,aAAcrE,QAAQ,KAAI,EAAE;mBAC5BsE,CAAAA,oCAAAA,iBAAkBtE,QAAQ,KAAI,EAAE;aACrC,CAAC0F,MAAM,CAACC,wBAAW;YACpB1F,QAAQ;mBACFmE,CAAAA,gCAAAA,aAAcnE,MAAM,KAAI,EAAE;mBAC1BoE,CAAAA,gCAAAA,aAAcpE,MAAM,KAAI,EAAE;mBAC1BqE,CAAAA,oCAAAA,iBAAkBrE,MAAM,KAAI,EAAE;aACnC,CAACyF,MAAM,CAACC,wBAAW;YACpBzF,OAAO;gBACLkE,gCAAAA,aAAclE,KAAK;gBACnBmE,gCAAAA,aAAcnE,KAAK;gBACnBoE,oCAAAA,iBAAkBpE,KAAK;aACxB;QACH;IACF;IACAH,SAASK,cACNK,UAAU,CAAC,2BACXmF,OAAO,CAAC,IAAMC,IAAAA,8BAAqB,EAAC9F,QAAQ;IAE/C,MAAM+F,mBAAkB,wBAAA,AAACtC,aAAuCuC,OAAO,qBAA/C,sBAAiDC,IAAI,CAC3EvG;IAGF,MAAMwG,0BAAyB,wBAAA,AAC7BxC,aACAsC,OAAO,qBAFsB,sBAEpBC,IAAI,CAACpG;IAEhB,MAAMsG,kBAAkBjC,QAAQC,MAAM,CAAC/D;IAEvC,IAAIJ,OAAOE,MAAM,CAAC6D,MAAM,GAAG,GAAG;QAC5B,8DAA8D;QAC9D,0DAA0D;QAC1D,IAAI/D,OAAOE,MAAM,CAAC6D,MAAM,GAAG,GAAG;YAC5B/D,OAAOE,MAAM,CAAC6D,MAAM,GAAG;QACzB;QACA,IAAIqC,QAAQpG,OAAOE,MAAM,CAACyF,MAAM,CAACU,SAASC,IAAI,CAAC;QAE/CC,QAAQH,KAAK,CAACI,IAAAA,eAAG,EAAC;QAElB,IACEJ,MAAMK,OAAO,CAAC,wBAAwB,CAAC,KACvCL,MAAMK,OAAO,CAAC,uCAAuC,CAAC,GACtD;YACA,MAAMC,kBAAkB;YACxB,MAAMC,SAASD,gBAAgBE,IAAI,CAACR;YACpC,MAAMS,YAAYF,UAAUA,OAAOG,MAAM,IAAIH,OAAOG,MAAM,CAACD,SAAS;YACpE,MAAM,IAAIE,MACR,CAAC,sFAAsF,EAAEF,UAAU,oFAAoF,CAAC;QAE5L;QAEAN,QAAQH,KAAK,CAACA;QACdG,QAAQH,KAAK;QAEb,IACEA,MAAMK,OAAO,CAAC,wBAAwB,CAAC,KACvCL,MAAMK,OAAO,CAAC,uBAAuB,CAAC,GACtC;YACA,MAAMO,MAAM,IAAID,MACd;YAEFC,IAAIC,IAAI,GAAG;YACX,MAAMD;QACR;QACA,MAAMA,MAAM,IAAID,MAAM;QACtBC,IAAIC,IAAI,GAAG;QACX,MAAMD;IACR,OAAO;QACL,IAAIhH,OAAOC,QAAQ,CAAC8D,MAAM,GAAG,GAAG;YAC9BC,KAAIC,IAAI,CAAC;YACTsC,QAAQtC,IAAI,CAACjE,OAAOC,QAAQ,CAAC0F,MAAM,CAACU,SAASC,IAAI,CAAC;YAClDC,QAAQtC,IAAI;QACd,OAAO,IAAI,CAAClE,cAAc;YACxBiE,KAAIkD,KAAK,CAAC;QACZ;QAEA,OAAO;YACLC,UAAUhB,eAAe,CAAC,EAAE;YAC5BiB,iBAAiB,EAAElB,0CAAAA,uBAAwBkB,iBAAiB;YAC5DxC,aAAaC,IAAAA,4BAAc;YAC3BwC,gBAAgB;gBACdC,QAAQvB,CAAAA,mCAAAA,gBAAiBuB,MAAM,OAAM,EAAE;gBACvCC,+BACExB,CAAAA,mCAAAA,gBAAiBwB,6BAA6B,OAAM,EAAE;YAC1D;QACF;IACF;AACF;AAGO,eAAehI,WAAWiI,UAIhC;IAKC,iCAAiC;IACjC,MAAMC,YAAY,IAAIC,kBAAS,CAAC;QAC9BC,SAASH,WAAWI,YAAY,CAACpH,MAAM,CAAEmH,OAAO;IAClD;IACAE,IAAAA,gBAAS,EAAC,aAAaJ;IACvB,0EAA0E;IAC1EK,OAAOC,MAAM,CAACzH,8BAAgB,EAAEkH,WAAWI,YAAY;IAEvD,0CAA0C;IAC1CI,IAAAA,2BAAoB,EAACR,WAAWS,UAAU;IAE1C,sBAAsB;IACtBC,IAAAA,+BAAiB,EAAC5H,8BAAgB,CAACsE,WAAW;IAE9C,iDAAiD;IACjDtE,8BAAgB,CAACE,MAAM,GAAG,MAAM2H,IAAAA,eAAU,EACxCC,iCAAsB,EACtB9H,8BAAgB,CAACC,GAAG;IAEtBD,8BAAgB,CAACD,aAAa,GAAGgI,IAAAA,YAAK,EACpC,CAAC,YAAY,EAAEb,WAAWzH,YAAY,CAAC,CAAC;IAG1C,MAAMC,SAAS,MAAMV,iBAAiBkI,WAAWzH,YAAY;IAC7D,MAAM,EAAEuI,YAAY,EAAEC,WAAW,EAAE,GAAGvI,OAAOoH,iBAAiB,IAAI,CAAC;IACnE,IAAIkB,cAAc;QAChB,MAAM,EAAEE,YAAY,EAAEC,WAAW,EAAE,GAAGH;QACtC,IAAIG,aAAa;YACfzI,OAAOoH,iBAAiB,CAAEkB,YAAY,CAAEG,WAAW,GAAGA;QACxD;QACA,IAAID,cAAc;YAChB,MAAME,eAAeF;YACrBxI,OAAOoH,iBAAiB,CAAEkB,YAAY,CAAEE,YAAY,GAAGE;QACzD;IACF;IACA,IAAIH,+BAAAA,YAAaI,iBAAiB,EAAE;QAClC,MAAMA,oBAAoBJ,YAAYI,iBAAiB;QACvD3I,OAAOoH,iBAAiB,CAAEmB,WAAW,CAAEI,iBAAiB,GAAGA;IAC7D;IACArI,8BAAgB,CAACD,aAAa,CAACuI,IAAI;IACnC,OAAO;QAAE,GAAG5I,MAAM;QAAE6I,kBAAkBC,IAAAA,qBAAc;IAAG;AACzD"}