{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "names": ["webpackBuild", "debug", "origDebug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNamesArg", "compilerNames", "nextBuildSpan", "prunedBuildContext", "NextBuildContext", "getWorker", "compilerName", "_worker", "Worker", "path", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "getStderr", "pipe", "stderr", "getStdout", "stdout", "worker", "_workerPool", "_workers", "_child", "on", "code", "signal", "combinedResult", "duration", "buildTraceContext", "curR<PERSON>ult", "worker<PERSON>ain", "buildContext", "traceState", "exportTraceState", "defaultParentSpanId", "getId", "shouldSaveTraceEvents", "debugTraceEvents", "recordTraceEvents", "end", "telemetryState", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "Log", "event", "with<PERSON><PERSON>ker", "webpackBuildImpl", "require"], "mappings": ";;;;+BAsIgBA;;;eAAAA;;;6DArIK;8BACY;4BAEV;8DACD;6DAEL;uBACmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpD,MAAMC,QAAQC,IAAAA,cAAS,EAAC;AAExB,MAAMC,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACxDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACjB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAAsD;IAEtD,MAAMC,gBAAgBD,oBAAoBZ;IAC1C,MAAM,EAAEc,aAAa,EAAE,GAAGC,oBAAoB,GAAGC,8BAAgB;IAEjED,mBAAmBd,WAAW,GAAGA;IAEjC,MAAMgB,YAAY,CAACC;YAeK;QAdtB,MAAMC,UAAU,IAAIC,kBAAM,CAACC,aAAI,CAACC,IAAI,CAACC,WAAW,YAAY;YAC1DC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;gBAC7B;YACF;QACF;QACAX,QAAQY,SAAS,GAAGC,IAAI,CAACH,QAAQI,MAAM;QACvCd,QAAQe,SAAS,GAAGF,IAAI,CAACH,QAAQM,MAAM;QAEvC,KAAK,MAAMC,UAAW,EAAA,sBAAA,AAACjB,QAAgBkB,WAAW,qBAA5B,oBAA8BC,QAAQ,KAAI,EAAE,CAE7D;YACHF,OAAOG,MAAM,CAACC,EAAE,CAAC,QAAQ,CAACC,MAAMC;gBAC9B,IAAID,QAASC,UAAUA,WAAW,UAAW;oBAC3C5C,MACE,CAAC,SAAS,EAAEoB,aAAa,gCAAgC,EAAEuB,KAAK,aAAa,EAAEC,OAAO,CAAC;gBAE3F;YACF;QACF;QAEA,OAAOvB;IACT;IAEA,MAAMwB,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,KAAK,MAAM3B,gBAAgBL,cAAe;YA4BpCiC;QA3BJ,MAAMV,SAASnB,UAAUC;QAEzB,MAAM4B,YAAY,MAAMV,OAAOW,UAAU,CAAC;YACxCC,cAAcjC;YACdG;YACA+B,YAAY;gBACV,GAAGC,IAAAA,uBAAgB,GAAE;gBACrBC,mBAAmB,EAAErC,iCAAAA,cAAesC,KAAK;gBACzCC,uBAAuB;YACzB;QACF;QACA,IAAIvC,iBAAiBgC,UAAUQ,gBAAgB,EAAE;YAC/CC,IAAAA,wBAAiB,EAACT,UAAUQ,gBAAgB;QAC9C;QACA,0DAA0D;QAC1D,MAAMlB,OAAOoB,GAAG;QAEhB,sBAAsB;QACtBvD,cAAcC,UAAUD,aAAa6C,UAAU7C,WAAW;QAC1Dc,mBAAmBd,WAAW,GAAGA;QAEjC,IAAI6C,UAAUW,cAAc,EAAE;YAC5BzC,8BAAgB,CAACyC,cAAc,GAAGX,UAAUW,cAAc;QAC5D;QAEAd,eAAeC,QAAQ,IAAIE,UAAUF,QAAQ;QAE7C,KAAIE,+BAAAA,UAAUD,iBAAiB,qBAA3BC,6BAA6BY,YAAY,EAAE;gBAUzCZ;YATJ,MAAM,EAAEa,YAAY,EAAE,GAAGb,UAAUD,iBAAiB,CAACa,YAAY;YAEjE,IAAIC,cAAc;gBAChBhB,eAAeE,iBAAiB,CAACa,YAAY,GAC3CZ,UAAUD,iBAAiB,CAACa,YAAY;gBAC1Cf,eAAeE,iBAAiB,CAACa,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAIb,gCAAAA,UAAUD,iBAAiB,qBAA3BC,8BAA6Bc,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAGf,UAAUD,iBAAiB,CAACe,WAAW;gBAErE,IAAIC,mBAAmB;oBACrBlB,eAAeE,iBAAiB,CAACe,WAAW,GAC1Cd,UAAUD,iBAAiB,CAACe,WAAW;oBAEzCjB,eAAeE,iBAAiB,CAACe,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAIhD,cAAciD,MAAM,KAAK,GAAG;QAC9BC,KAAIC,KAAK,CAAC;IACZ;IAEA,OAAOrB;AACT;AAEO,SAAS9C,aACdoE,UAAmB,EACnBpD,aAAmD;IAEnD,IAAIoD,YAAY;QACdnE,MAAM;QACN,OAAOa,uBAAuBE;IAChC,OAAO;QACLf,MAAM;QACN,MAAMoE,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,OAAOA,iBAAiB,MAAM;IAChC;AACF"}