{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-route-loader.ts"], "names": ["getFilenameAndExtension", "errorOnBadHandler", "resourcePath", "JSON", "stringify", "cacheHeader", "none", "longCache", "revalidate", "filename", "path", "basename", "name", "ext", "split", "getContentType", "imageExtMimeTypeMap", "getStaticAssetRouteCode", "fileBaseName", "cache", "process", "env", "NODE_ENV", "code", "fs", "promises", "readFile", "toString", "getDynamicTextRouteCode", "getDynamicImageRouteCode", "getDynamicSiteMapRouteCode", "page", "loaderContext", "staticGenerationCode", "exportNames", "getLoaderModuleNamedExports", "reExportNames", "filter", "hasGenerateSiteMaps", "includes", "length", "join", "nextMetadataRouterLoader", "isDynamic", "filePath", "getOptions", "addDependency"], "mappings": ";;;;;;;;;;;;;;;IAyRA,OAAuC;eAAvC;;IA3PgBA,uBAAuB;eAAvBA;;;2DA7BD;6DACE;0BACmB;uBACQ;;;;;;AAE5C,SAASC,kBAAkBC,YAAoB;IAC7C,OAAO,CAAC;;kDAEwC,EAAEC,KAAKC,SAAS,CAC5DF,cACA;;EAEJ,CAAC;AACH;AAEA,MAAMG,cAAc;IAClBC,MAAM;IACNC,WAAW;IACXC,YAAY;AACd;AAUO,SAASR,wBAAwBE,YAAoB;IAC1D,MAAMO,WAAWC,aAAI,CAACC,QAAQ,CAACT;IAC/B,MAAM,CAACU,MAAMC,IAAI,GAAGJ,SAASK,KAAK,CAAC,KAAK;IACxC,OAAO;QAAEF;QAAMC;IAAI;AACrB;AAEA,SAASE,eAAeb,YAAoB;IAC1C,IAAI,EAAEU,IAAI,EAAEC,GAAG,EAAE,GAAGb,wBAAwBE;IAC5C,IAAIW,QAAQ,OAAOA,MAAM;IAEzB,IAAID,SAAS,aAAaC,QAAQ,OAAO,OAAO;IAChD,IAAID,SAAS,WAAW,OAAO;IAC/B,IAAIA,SAAS,UAAU,OAAO;IAC9B,IAAIA,SAAS,YAAY,OAAO;IAEhC,IAAIC,QAAQ,SAASA,QAAQ,UAAUA,QAAQ,SAASA,QAAQ,OAAO;QACrE,OAAOG,6BAAmB,CAACH,IAAI;IACjC;IACA,OAAO;AACT;AAEA,eAAeI,wBACbf,YAAoB,EACpBgB,YAAoB;IAEpB,MAAMC,QACJD,iBAAiB,YACb,uCACAE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBjB,YAAYC,IAAI,GAChBD,YAAYE,SAAS;IAC3B,MAAMgB,OAAO,CAAC;;;;oBAII,EAAEpB,KAAKC,SAAS,CAACW,eAAeb,eAAe;2BACxC,EAAEC,KAAKC,SAAS,CACvC,AAAC,CAAA,MAAMoB,WAAE,CAACC,QAAQ,CAACC,QAAQ,CAACxB,aAAY,EAAGyB,QAAQ,CAAC,WACpD;;;;;;;uBAOmB,EAAExB,KAAKC,SAAS,CAACe,OAAO;;;;;;AAM/C,CAAC;IACC,OAAOI;AACT;AAEA,SAASK,wBAAwB1B,YAAoB;IACnD,OAAO,CAAC;;;oBAGU,EAAEC,KAAKC,SAAS,CAACF,cAAc;;;oBAG/B,EAAEC,KAAKC,SAAS,CAACW,eAAeb,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACJ,wBAAwBE,cAAcU,IAAI,EAAE;;AAE9E,EAAEX,kBAAkBC,cAAc;;;;;;;;;uBASX,EAAEC,KAAKC,SAAS,CAACC,YAAYG,UAAU,EAAE;;;;AAIhE,CAAC;AACD;AAEA,iCAAiC;AACjC,SAASqB,yBAAyB3B,YAAoB;IACpD,OAAO,CAAC;;;0BAGgB,EAAEC,KAAKC,SAAS,CAACF,cAAc;;;;;;;AAOzD,EAAED,kBAAkBC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;AAyBlC,CAAC;AACD;AAEA,eAAe4B,2BACb5B,YAAoB,EACpB6B,IAAY,EACZC,aAAyC;IAEzC,IAAIC,uBAAuB;IAE3B,MAAMC,cAAc,MAAMC,IAAAA,kCAA2B,EACnDjC,cACA8B;IAEF,iDAAiD;IACjD,MAAMI,gBAAgBF,YAAYG,MAAM,CACtC,CAACzB,OAASA,SAAS,aAAaA,SAAS;IAG3C,MAAM0B,sBAAsBJ,YAAYK,QAAQ,CAAC;IACjD,IACEnB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBgB,uBACAP,KAAKQ,QAAQ,CAAC,sBACd;QACAN,uBAAuB,CAAC;;;;;;;;;;;IAWxB,CAAC;IACH;IAEA,MAAMV,OAAO,CAAC;;0BAEU,EAAEpB,KAAKC,SAAS,CAACF,cAAc;;;;;;oBAMrC,EAAEC,KAAKC,SAAS,CAACW,eAAeb,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACJ,wBAAwBE,cAAcU,IAAI,EAAE;;AAE9E,EAAEX,kBAAkBC,cAAc;;AAElC,EAAE,GAAG,wCAAwC,IAAG;AAChD,EACEkC,cAAcI,MAAM,GAAG,IACnB,CAAC,SAAS,EAAEJ,cAAcK,IAAI,CAAC,MAAM,QAAQ,EAAEtC,KAAKC,SAAS,CAC3DF,cACA,EAAE,CAAC,GACL,GACL;;;;EAIC,EACE,GAAG,2FAA2F,IAC/F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAkCoB,EAAEC,KAAKC,SAAS,CAACC,YAAYG,UAAU,EAAE;;;;;AAKhE,EAAEyB,qBAAqB;AACvB,CAAC;IACC,OAAOV;AACT;AAEA,gFAAgF;AAChF,oDAAoD;AACpD,MAAMmB,2BACJ;IACE,MAAM,EAAEX,IAAI,EAAEY,SAAS,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAACC,UAAU;IACrD,MAAM,EAAEjC,MAAMM,YAAY,EAAE,GAAGlB,wBAAwB4C;IACvD,IAAI,CAACE,aAAa,CAACF;IAEnB,IAAIrB,OAAO;IACX,IAAIoB,cAAc,KAAK;QACrB,IAAIzB,iBAAiB,YAAYA,iBAAiB,YAAY;YAC5DK,OAAOK,wBAAwBgB;QACjC,OAAO,IAAI1B,iBAAiB,WAAW;YACrCK,OAAO,MAAMO,2BAA2Bc,UAAUb,MAAM,IAAI;QAC9D,OAAO;YACLR,OAAOM,yBAAyBe;QAClC;IACF,OAAO;QACLrB,OAAO,MAAMN,wBAAwB2B,UAAU1B;IACjD;IAEA,OAAOK;AACT;MAEF,WAAemB"}