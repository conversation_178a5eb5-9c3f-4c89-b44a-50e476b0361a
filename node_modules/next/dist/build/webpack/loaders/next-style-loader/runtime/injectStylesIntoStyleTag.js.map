{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.ts"], "names": ["isOldIE", "memo", "memorize", "Boolean", "window", "document", "all", "atob", "getTargetElement", "target", "styleTarget", "querySelector", "HTMLIFrameElement", "contentDocument", "head", "e", "stylesInDom", "getIndexByIdentifier", "identifier", "result", "i", "length", "modulesToDom", "list", "options", "idCountMap", "identifiers", "item", "id", "base", "count", "toString", "index", "obj", "css", "media", "sourceMap", "references", "updater", "push", "addStyle", "insertStyleElement", "style", "createElement", "attributes", "nonce", "__webpack_nonce__", "Object", "keys", "for<PERSON>ach", "key", "setAttribute", "insert", "Error", "append<PERSON><PERSON><PERSON>", "removeStyleElement", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "replaceText", "textStore", "replace", "replacement", "filter", "join", "applyToSingletonTag", "remove", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "applyToTag", "_options", "removeAttribute", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "singleton", "singletonCounter", "update", "styleIndex", "bind", "updateStyle", "newObj", "module", "exports", "lastIdentifiers", "newList", "prototype", "call", "newLastIdentifiers", "splice"], "mappings": ";AAAA,MAAMA,UAAU,AAAC,SAASA;IACxB,IAAIC;IAEJ,OAAO,SAASC;QACd,IAAI,OAAOD,SAAS,aAAa;YAC/B,+CAA+C;YAC/C,sEAAsE;YACtE,mEAAmE;YACnE,sDAAsD;YACtD,kEAAkE;YAClEA,OAAOE,QAAQC,UAAUC,YAAYA,SAASC,GAAG,IAAI,CAACF,OAAOG,IAAI;QACnE;QAEA,OAAON;IACT;AACF;AAEA,MAAMO,mBAAmB,AAAC;IACxB,MAAMP,OAAY,CAAC;IAEnB,OAAO,SAASC,SAASO,MAAW;QAClC,IAAI,OAAOR,IAAI,CAACQ,OAAO,KAAK,aAAa;YACvC,IAAIC,cAAmBL,SAASM,aAAa,CAACF;YAE9C,iEAAiE;YACjE,IACEL,OAAOQ,iBAAiB,IACxBF,uBAAuBN,OAAOQ,iBAAiB,EAC/C;gBACA,IAAI;oBACF,8DAA8D;oBAC9D,mCAAmC;oBACnCF,cAAc,AAACA,YAAoBG,eAAe,CAACC,IAAI;gBACzD,EAAE,OAAOC,GAAG;oBACV,uBAAuB;oBACvBL,cAAc;gBAChB;YACF;YAEAT,IAAI,CAACQ,OAAO,GAAGC;QACjB;QAEA,OAAOT,IAAI,CAACQ,OAAO;IACrB;AACF;AAEA,MAAMO,cAAmB,EAAE;AAE3B,SAASC,qBAAqBC,UAAe;IAC3C,IAAIC,SAAS,CAAC;IAEd,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,YAAYK,MAAM,EAAED,IAAK;QAC3C,IAAIJ,WAAW,CAACI,EAAE,CAACF,UAAU,KAAKA,YAAY;YAC5CC,SAASC;YACT;QACF;IACF;IAEA,OAAOD;AACT;AAEA,SAASG,aAAaC,IAAS,EAAEC,OAAY;IAC3C,MAAMC,aAAkB,CAAC;IACzB,MAAMC,cAAc,EAAE;IAEtB,IAAK,IAAIN,IAAI,GAAGA,IAAIG,KAAKF,MAAM,EAAED,IAAK;QACpC,MAAMO,OAAOJ,IAAI,CAACH,EAAE;QACpB,MAAMQ,KAAKJ,QAAQK,IAAI,GAAGF,IAAI,CAAC,EAAE,GAAGH,QAAQK,IAAI,GAAGF,IAAI,CAAC,EAAE;QAC1D,MAAMG,QAAQL,UAAU,CAACG,GAAG,IAAI;QAChC,MAAMV,aAAaU,KAAK,MAAME,MAAMC,QAAQ;QAE5CN,UAAU,CAACG,GAAG,GAAGE,QAAQ;QAEzB,MAAME,QAAQf,qBAAqBC;QACnC,MAAMe,MAAM;YACVC,KAAKP,IAAI,CAAC,EAAE;YACZQ,OAAOR,IAAI,CAAC,EAAE;YACdS,WAAWT,IAAI,CAAC,EAAE;QACpB;QAEA,IAAIK,UAAU,CAAC,GAAG;YAChBhB,WAAW,CAACgB,MAAM,CAACK,UAAU;YAC7BrB,WAAW,CAACgB,MAAM,CAACM,OAAO,CAACL;QAC7B,OAAO;YACLjB,YAAYuB,IAAI,CAAC;gBACfrB,YAAYA;gBACZ,mEAAmE;gBACnEoB,SAASE,SAASP,KAAKT;gBACvBa,YAAY;YACd;QACF;QAEAX,YAAYa,IAAI,CAACrB;IACnB;IAEA,OAAOQ;AACT;AAEA,SAASe,mBAAmBjB,OAAY;IACtC,MAAMkB,QAAQrC,SAASsC,aAAa,CAAC;IACrC,MAAMC,aAAapB,QAAQoB,UAAU,IAAI,CAAC;IAE1C,IAAI,OAAOA,WAAWC,KAAK,KAAK,aAAa;QAC3C,MAAMA,QACJ,oCAAoC;QACpC,OAAOC,sBAAsB,cAAcA,oBAAoB;QAEjE,IAAID,OAAO;YACTD,WAAWC,KAAK,GAAGA;QACrB;IACF;IAEAE,OAAOC,IAAI,CAACJ,YAAYK,OAAO,CAAC,SAAUC,GAAG;QAC3CR,MAAMS,YAAY,CAACD,KAAKN,UAAU,CAACM,IAAI;IACzC;IAEA,IAAI,OAAO1B,QAAQ4B,MAAM,KAAK,YAAY;QACxC5B,QAAQ4B,MAAM,CAACV;IACjB,OAAO;QACL,MAAMjC,SAASD,iBAAiBgB,QAAQ4B,MAAM,IAAI;QAElD,IAAI,CAAC3C,QAAQ;YACX,MAAM,IAAI4C,MACR;QAEJ;QAEA5C,OAAO6C,WAAW,CAACZ;IACrB;IAEA,OAAOA;AACT;AAEA,SAASa,mBAAmBb,KAAU;IACpC,qBAAqB;IACrB,IAAIA,MAAMc,UAAU,KAAK,MAAM;QAC7B,OAAO;IACT;IAEAd,MAAMc,UAAU,CAACC,WAAW,CAACf;AAC/B;AAEA,yBAAyB,GACzB,MAAMgB,cAAc,AAAC,SAASA;IAC5B,MAAMC,YAAiB,EAAE;IAEzB,OAAO,SAASC,QAAQ5B,KAAU,EAAE6B,WAAgB;QAClDF,SAAS,CAAC3B,MAAM,GAAG6B;QAEnB,OAAOF,UAAUG,MAAM,CAAC3D,SAAS4D,IAAI,CAAC;IACxC;AACF;AAEA,SAASC,oBAAoBtB,KAAU,EAAEV,KAAU,EAAEiC,MAAW,EAAEhC,GAAQ;IACxE,MAAMC,MAAM+B,SACR,KACAhC,IAAIE,KAAK,GACT,YAAYF,IAAIE,KAAK,GAAG,OAAOF,IAAIC,GAAG,GAAG,MACzCD,IAAIC,GAAG;IAEX,aAAa;IACb,uBAAuB,GACvB,IAAIQ,MAAMwB,UAAU,EAAE;QACpBxB,MAAMwB,UAAU,CAACC,OAAO,GAAGT,YAAY1B,OAAOE;IAChD,OAAO;QACL,MAAMkC,UAAU/D,SAASgE,cAAc,CAACnC;QACxC,MAAMoC,aAAa5B,MAAM4B,UAAU;QAEnC,IAAIA,UAAU,CAACtC,MAAM,EAAE;YACrBU,MAAMe,WAAW,CAACa,UAAU,CAACtC,MAAM;QACrC;QAEA,IAAIsC,WAAWjD,MAAM,EAAE;YACrBqB,MAAM6B,YAAY,CAACH,SAASE,UAAU,CAACtC,MAAM;QAC/C,OAAO;YACLU,MAAMY,WAAW,CAACc;QACpB;IACF;AACF;AAEA,SAASI,WAAW9B,KAAU,EAAE+B,QAAa,EAAExC,GAAQ;IACrD,IAAIC,MAAMD,IAAIC,GAAG;IACjB,MAAMC,QAAQF,IAAIE,KAAK;IACvB,MAAMC,YAAYH,IAAIG,SAAS;IAE/B,IAAID,OAAO;QACTO,MAAMS,YAAY,CAAC,SAAShB;IAC9B,OAAO;QACLO,MAAMgC,eAAe,CAAC;IACxB;IAEA,IAAItC,aAAa,OAAOuC,SAAS,aAAa;QAC5CzC,OACE,yDACAyC,KAAKC,SAASC,mBAAmBC,KAAKC,SAAS,CAAC3C,gBAChD;IACJ;IAEA,aAAa;IACb,uBAAuB,GACvB,IAAIM,MAAMwB,UAAU,EAAE;QACpBxB,MAAMwB,UAAU,CAACC,OAAO,GAAGjC;IAC7B,OAAO;QACL,MAAOQ,MAAMsC,UAAU,CAAE;YACvBtC,MAAMe,WAAW,CAACf,MAAMsC,UAAU;QACpC;QAEAtC,MAAMY,WAAW,CAACjD,SAASgE,cAAc,CAACnC;IAC5C;AACF;AAEA,IAAI+C,YAAiB;AACrB,IAAIC,mBAAmB;AAEvB,SAAS1C,SAASP,GAAQ,EAAET,OAAY;IACtC,IAAIkB;IACJ,IAAIyC;IACJ,IAAIlB;IAEJ,IAAIzC,QAAQyD,SAAS,EAAE;QACrB,MAAMG,aAAaF;QAEnBxC,QAAQuC,aAAcA,CAAAA,YAAYxC,mBAAmBjB,QAAO;QAE5D2D,SAASnB,oBAAoBqB,IAAI,CAAC,MAAM3C,OAAO0C,YAAY;QAC3DnB,SAASD,oBAAoBqB,IAAI,CAAC,MAAM3C,OAAO0C,YAAY;IAC7D,OAAO;QACL1C,QAAQD,mBAAmBjB;QAE3B2D,SAASX,WAAWa,IAAI,CAAC,MAAM3C,OAAOlB;QACtCyC,SAAS;YACPV,mBAAmBb;QACrB;IACF;IAEAyC,OAAOlD;IAEP,OAAO,SAASqD,YAAYC,MAAW;QACrC,IAAIA,QAAQ;YACV,IACEA,OAAOrD,GAAG,KAAKD,IAAIC,GAAG,IACtBqD,OAAOpD,KAAK,KAAKF,IAAIE,KAAK,IAC1BoD,OAAOnD,SAAS,KAAKH,IAAIG,SAAS,EAClC;gBACA;YACF;YAEA+C,OAAQlD,MAAMsD;QAChB,OAAO;YACLtB;QACF;IACF;AACF;AAEAuB,OAAOC,OAAO,GAAG,SAAUlE,IAAS,EAAEC,OAAY;IAChDA,UAAUA,WAAW,CAAC;IAEtB,iFAAiF;IACjF,+BAA+B;IAC/B,IAAI,CAACA,QAAQyD,SAAS,IAAI,OAAOzD,QAAQyD,SAAS,KAAK,WAAW;QAChEzD,QAAQyD,SAAS,GAAGjF;IACtB;IAEAuB,OAAOA,QAAQ,EAAE;IAEjB,IAAImE,kBAAkBpE,aAAaC,MAAMC;IAEzC,OAAO,SAAS2D,OAAOQ,OAAY;QACjCA,UAAUA,WAAW,EAAE;QAEvB,IAAI5C,OAAO6C,SAAS,CAAC7D,QAAQ,CAAC8D,IAAI,CAACF,aAAa,kBAAkB;YAChE;QACF;QAEA,IAAK,IAAIvE,IAAI,GAAGA,IAAIsE,gBAAgBrE,MAAM,EAAED,IAAK;YAC/C,MAAMF,aAAawE,eAAe,CAACtE,EAAE;YACrC,MAAMY,QAAQf,qBAAqBC;YAEnCF,WAAW,CAACgB,MAAM,CAACK,UAAU;QAC/B;QAEA,MAAMyD,qBAAqBxE,aAAaqE,SAASnE;QAEjD,IAAK,IAAIJ,IAAI,GAAGA,IAAIsE,gBAAgBrE,MAAM,EAAED,IAAK;YAC/C,MAAMF,aAAawE,eAAe,CAACtE,EAAE;YACrC,MAAMY,QAAQf,qBAAqBC;YAEnC,IAAIF,WAAW,CAACgB,MAAM,CAACK,UAAU,KAAK,GAAG;gBACvCrB,WAAW,CAACgB,MAAM,CAACM,OAAO;gBAC1BtB,YAAY+E,MAAM,CAAC/D,OAAO;YAC5B;QACF;QAEA0D,kBAAkBI;IACpB;AACF"}