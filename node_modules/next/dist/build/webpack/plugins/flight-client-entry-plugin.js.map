{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-client-entry-plugin.ts"], "names": ["FlightClientEntryPlugin", "PLUGIN_NAME", "pluginState", "getProxiedPluginState", "serverActions", "edgeServerActions", "actionModServerId", "actionModEdgeServerId", "serverModuleIds", "edgeServerModuleIds", "ASYNC_CLIENT_MODULES", "injectedClientEntries", "deduplicateCSSImportsForEntry", "mergedCSSimports", "sortedCSSImports", "Object", "entries", "sort", "a", "b", "a<PERSON><PERSON>", "bPath", "a<PERSON><PERSON><PERSON>", "split", "length", "b<PERSON><PERSON><PERSON>", "aName", "path", "parse", "name", "bName", "indexA", "indexOf", "indexB", "dedupedCSSImports", "trackedCSSImports", "Set", "entryName", "cssImports", "cssImport", "has", "filename", "includes", "add", "push", "constructor", "options", "dev", "appDir", "isEdgeServer", "assetPrefix", "<PERSON><PERSON><PERSON>", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "webpack", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "finishMake", "tapPromise", "createClientEntries", "afterCompile", "recordModule", "modId", "mod", "modPath", "matchResource", "resourceResolveData", "mod<PERSON><PERSON><PERSON>", "query", "modResource", "startsWith", "BARREL_OPTIMIZATION_PREFIX", "formatBarrelOptimizedResource", "resource", "layer", "WEBPACK_LAYERS", "serverSideRendering", "ssrNamedModuleId", "relative", "context", "normalizePathSep", "replace", "traverseModules", "_chunk", "_chunkGroup", "isWebpackServerOnlyLayer", "moduleGraph", "isAsync", "make", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createActionAssets", "addClientEntryAndSSRModulesList", "createdSSRDependenciesForEntry", "addActionEntryList", "actionMapsPerEntry", "createdActions", "forEachEntryModule", "entryModule", "internalClientComponentEntryImports", "actionEntryImports", "Map", "clientEntriesToInject", "connection", "getModuleReferencesInOrder", "entryRequest", "dependency", "request", "clientComponentImports", "actionImports", "collectComponentInfoFromServerEntryDependency", "resolvedModule", "for<PERSON>ach", "dep", "names", "isAbsoluteRequest", "isAbsolute", "keys", "value", "relativeRequest", "bundlePath", "assign", "absolutePagePath", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "clientEntryToInject", "injected", "injectClientEntryAndSSRModules", "clientImports", "reduce", "res", "curr", "APP_CLIENT_INTERNALS", "size", "actionNames", "actionName", "injectActionEntry", "actions", "finishModules", "addedClientActionEntryList", "actionMapsPerClientEntry", "ssrEntryDependencies", "collectClientActionsFromDependencies", "remainingClientImportedActions", "remainingActionEntryImports", "remainingActionNames", "id", "fromClient", "Promise", "all", "invalidator", "getInvalidator", "outputPath", "some", "shouldInvalidate", "invalidate", "COMPILER_NAMES", "client", "map", "addClientEntryAndSSRModules", "collectedActions", "visitedModule", "visitedEntry", "collectActions", "collectActionsInDep", "modRequest", "getActions", "entryDependency", "ssrEntryModule", "getResolvedModule", "visited", "CSSImports", "filterClientComponents", "importedIdentifiers", "isCSS", "isCSSMod", "_identifier", "addClientImport", "webpackRuntime", "EDGE_RUNTIME_WEBPACK", "DEFAULT_RUNTIME_WEBPACK", "sideEffectFree", "factoryMeta", "unused", "getExportsInfo", "isModuleUsed", "isClientComponentEntryModule", "dependencyIds", "depModule", "ids", "Array", "from", "loaderOptions", "modules", "regexCSS", "test", "localeCompare", "clientImportPath", "server", "clientBrowserLoader", "stringify", "sep", "x", "JSON", "clientSSRLoader", "getEntries", "page<PERSON><PERSON>", "getEntry<PERSON>ey", "PAGE_TYPES", "APP", "type", "EntryTypes", "CHILD_ENTRY", "parentEntries", "absoluteEntryFilePath", "dispose", "lastActiveTime", "Date", "now", "entryData", "clientComponentEntryDep", "EntryPlugin", "createDependency", "addEntry", "actionsArray", "actionLoader", "__client_imported__", "currentCompilerServerActions", "p", "generateActionId", "workers", "<PERSON><PERSON><PERSON><PERSON>", "reactServerComponents", "actionEntryDep", "resolve", "reject", "entry", "get", "includeDependencies", "call", "addModuleTree", "contextInfo", "issuer<PERSON><PERSON>er", "err", "module", "failedEntry", "<PERSON><PERSON><PERSON><PERSON>", "chunkGroup", "mapping", "action", "json", "node", "edge", "undefined", "SERVER_REFERENCE_MANIFEST", "sources", "RawSource", "isFirstImport", "getModuleBuildInfo", "clientEntryType", "rsc", "isCjsModule", "assumedSourceType", "getAssumedSourceType", "clientImportsSet", "isAutoModuleSourceType", "isCjsDefaultImport"], "mappings": ";;;;+BAuKaA;;;eAAAA;;;yBAjKW;6BACE;6DACT;sCAOV;2BACwB;4BASxB;uBAOA;wBAMA;kCAC0B;8BACK;2BACX;wBACc;oCACN;kCACE;;;;;;AASrC,MAAMC,cAAc;AAqBpB,MAAMC,cAAcC,IAAAA,mCAAqB,EAAC;IACxC,gDAAgD;IAChDC,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,mBAAmB,CAAC;IAOpBC,uBAAuB,CAAC;IAQxB,gEAAgE;IAChEC,iBAAiB,CAAC;IAClBC,qBAAqB,CAAC;IAEtB,6DAA6D;IAC7D,wEAAwE;IACxE,qFAAqF;IACrF,uCAAuC;IACvCC,sBAAsB,CAAC;IAEvBC,uBAAuB,CAAC;AAC1B;AAEA,SAASC,8BAA8BC,gBAA4B;IACjE,uEAAuE;IACvE,oEAAoE;IACpE,wEAAwE;IACxE,+DAA+D;IAC/D,sEAAsE;IACtE,uEAAuE;IACvE,wEAAwE;IACxE,UAAU;IACV,qEAAqE;IACrE,qEAAqE;IACrE,mEAAmE;IACnE,yEAAyE;IACzE,uFAAuF;IAEvF,2CAA2C;IAC3C,MAAMC,mBAAmBC,OAAOC,OAAO,CAACH,kBAAkBI,IAAI,CAAC,CAACC,GAAGC;QACjE,MAAM,CAACC,MAAM,GAAGF;QAChB,MAAM,CAACG,MAAM,GAAGF;QAEhB,MAAMG,SAASF,MAAMG,KAAK,CAAC,KAAKC,MAAM;QACtC,MAAMC,SAASJ,MAAME,KAAK,CAAC,KAAKC,MAAM;QAEtC,IAAIF,WAAWG,QAAQ;YACrB,OAAOH,SAASG;QAClB;QAEA,MAAMC,QAAQC,aAAI,CAACC,KAAK,CAACR,OAAOS,IAAI;QACpC,MAAMC,QAAQH,aAAI,CAACC,KAAK,CAACP,OAAOQ,IAAI;QAEpC,MAAME,SAAS;YAAC;YAAY;SAAS,CAACC,OAAO,CAACN;QAC9C,MAAMO,SAAS;YAAC;YAAY;SAAS,CAACD,OAAO,CAACF;QAE9C,IAAIC,WAAW,CAAC,GAAG,OAAO;QAC1B,IAAIE,WAAW,CAAC,GAAG,OAAO,CAAC;QAC3B,OAAOF,SAASE;IAClB;IAEA,MAAMC,oBAAgC,CAAC;IACvC,MAAMC,oBAAoB,IAAIC;IAC9B,KAAK,MAAM,CAACC,WAAWC,WAAW,IAAIxB,iBAAkB;QACtD,KAAK,MAAMyB,aAAaD,WAAY;YAClC,IAAIH,kBAAkBK,GAAG,CAACD,YAAY;YAEtC,iEAAiE;YACjE,MAAME,WAAWd,aAAI,CAACC,KAAK,CAACS,WAAWR,IAAI;YAC3C,IAAI;gBAAC;gBAAY;aAAS,CAACa,QAAQ,CAACD,WAAW;gBAC7CN,kBAAkBQ,GAAG,CAACJ;YACxB;YAEA,IAAI,CAACL,iBAAiB,CAACG,UAAU,EAAE;gBACjCH,iBAAiB,CAACG,UAAU,GAAG,EAAE;YACnC;YACAH,iBAAiB,CAACG,UAAU,CAACO,IAAI,CAACL;QACpC;IACF;IAEA,OAAOL;AACT;AAEO,MAAMlC;IAOX6C,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,YAAY,GAAGH,QAAQG,YAAY;QACxC,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACH,GAAG,IAAI,CAAC,IAAI,CAACE,YAAY,GAAG,QAAQ;QAC7D,IAAI,CAACE,aAAa,GAAGL,QAAQK,aAAa;IAC5C;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5BvD,aACA,CAACsD,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrCL;YAEFF,YAAYQ,mBAAmB,CAACJ,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrC,IAAIF,gBAAO,CAACC,YAAY,CAACG,cAAc,CAACC,QAAQ;QAEpD;QAGFZ,SAASC,KAAK,CAACY,UAAU,CAACC,UAAU,CAAClE,aAAa,CAACsD,cACjD,IAAI,CAACa,mBAAmB,CAACf,UAAUE;QAGrCF,SAASC,KAAK,CAACe,YAAY,CAACb,GAAG,CAACvD,aAAa,CAACsD;YAC5C,MAAMe,eAAe,CAACC,OAAeC;oBAGEA,0BACpBA;gBAHjB,yFAAyF;gBACzF,2DAA2D;gBAC3D,MAAMC,UAAUD,IAAIE,aAAa,MAAIF,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyB7C,IAAI;gBAClE,MAAMiD,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;gBACnD,wCAAwC;gBACxC,gFAAgF;gBAChF,MAAMC,cAAcL,UAChBA,QAAQM,UAAU,CAACC,sCAA0B,IAC3CC,IAAAA,qCAA6B,EAACT,IAAIU,QAAQ,EAAET,WAC5CA,UAAUG,WACZJ,IAAIU,QAAQ;gBAEhB,IAAIV,IAAIW,KAAK,KAAKC,yBAAc,CAACC,mBAAmB,EAAE;oBACpD;gBACF;gBAEA,yHAAyH;gBACzH,IAAI,OAAOd,UAAU,eAAeO,aAAa;oBAC/C,4EAA4E;oBAC5E,6EAA6E;oBAC7E,sBAAsB;oBACtB,IAAIQ,mBAAmB3D,aAAI,CAAC4D,QAAQ,CAAClC,SAASmC,OAAO,EAAEV;oBAEvD,IAAI,CAACQ,iBAAiBP,UAAU,CAAC,MAAM;wBACrC,+BAA+B;wBAC/BO,mBAAmB,CAAC,EAAE,EAAEG,IAAAA,kCAAgB,EAACH,kBAAkB,CAAC;oBAC9D;oBAEA,IAAI,IAAI,CAACrC,YAAY,EAAE;wBACrB/C,YAAYO,mBAAmB,CAC7B6E,iBAAiBI,OAAO,CAAC,uBAAuB,eACjD,GAAGnB;oBACN,OAAO;wBACLrE,YAAYM,eAAe,CAAC8E,iBAAiB,GAAGf;oBAClD;gBACF;YACF;YAEAoB,IAAAA,uBAAe,EAACpC,aAAa,CAACiB,KAAKoB,QAAQC,aAAatB;gBACtD,IAAIC,OAAOA,IAAIU,QAAQ,IAAI,CAACY,IAAAA,gCAAwB,EAACtB,IAAIW,KAAK,GAAG;oBAC/D,IAAI5B,YAAYwC,WAAW,CAACC,OAAO,CAACxB,MAAM;wBACxC,0FAA0F;wBAC1F,4FAA4F;wBAC5FtE,YAAYQ,oBAAoB,CAAC8D,IAAIU,QAAQ,CAAC,GAAG;oBACnD;gBACF;gBAEA,IAAIX,OAAOD,aAAaC,OAAOC;YACjC;QACF;QAEAnB,SAASC,KAAK,CAAC2C,IAAI,CAACzC,GAAG,CAACvD,aAAa,CAACsD;YACpCA,YAAYD,KAAK,CAAC4C,aAAa,CAAC/B,UAAU,CACxC;gBACEtC,MAAM5B;gBACNkG,OAAOvC,gBAAO,CAACwC,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,kBAAkB,CAAChD,aAAa+C;QAErD;IACF;IAEA,MAAMlC,oBACJf,QAA0B,EAC1BE,WAAgC,EAChC;QACA,MAAMiD,kCAEF,EAAE;QACN,MAAMC,iCAGF,CAAC;QAEL,MAAMC,qBACJ,EAAE;QACJ,MAAMC,qBAA4D,CAAC;QACnE,MAAMC,iBAAiB,IAAIxE;QAE3B,4EAA4E;QAC5E,0BAA0B;QAC1ByE,IAAAA,0BAAkB,EAACtD,aAAa,CAAC,EAAE1B,IAAI,EAAEiF,WAAW,EAAE;YACpD,MAAMC,sCAA8D,CAAC;YACrE,MAAMC,qBAAqB,IAAIC;YAC/B,MAAMC,wBAAwB,EAAE;YAChC,MAAMrG,mBAA+B,CAAC;YAEtC,KAAK,MAAMsG,cAAcC,IAAAA,kCAA0B,EACjDN,aACAvD,YAAYwC,WAAW,EACtB;gBACD,uFAAuF;gBACvF,MAAMsB,eAAe,AACnBF,WAAWG,UAAU,CACrBC,OAAO;gBAET,MAAM,EAAEC,sBAAsB,EAAEC,aAAa,EAAEnF,UAAU,EAAE,GACzD,IAAI,CAACoF,6CAA6C,CAAC;oBACjDL;oBACA9D;oBACAoE,gBAAgBR,WAAWQ,cAAc;gBAC3C;gBAEFF,cAAcG,OAAO,CAAC,CAAC,CAACC,KAAKC,MAAM,GACjCd,mBAAmBrD,GAAG,CAACkE,KAAKC;gBAG9B,MAAMC,oBAAoBpG,aAAI,CAACqG,UAAU,CAACX;gBAE1C,mDAAmD;gBACnD,IAAI,CAACU,mBAAmB;oBACtBhH,OAAOkH,IAAI,CAACT,wBAAwBI,OAAO,CACzC,CAACM,QAAWnB,mCAAmC,CAACmB,MAAM,GAAG,IAAI9F;oBAE/D;gBACF;gBAEA,2HAA2H;gBAC3H,4DAA4D;gBAC5D,kEAAkE;gBAClE,aAAa;gBACb,IAAI;gBAEJ,MAAM+F,kBAAkBJ,oBACpBpG,aAAI,CAAC4D,QAAQ,CAAChC,YAAYT,OAAO,CAAC0C,OAAO,EAAG6B,gBAC5CA;gBAEJ,8CAA8C;gBAC9C,MAAMe,aAAa3C,IAAAA,kCAAgB,EACjC0C,gBAAgBzC,OAAO,CAAC,eAAe,IAAIA,OAAO,CAAC,aAAa;gBAGlE3E,OAAOsH,MAAM,CAACxH,kBAAkByB;gBAChC4E,sBAAsBtE,IAAI,CAAC;oBACzBS;oBACAE;oBACAlB,WAAWR;oBACX2F;oBACAY;oBACAE,kBAAkBjB;gBACpB;gBAEA,uKAAuK;gBACvK,wGAAwG;gBACxG,8IAA8I;gBAC9I,IACExF,SAAS,CAAC,GAAG,EAAE0G,4CAAgC,CAAC,CAAC,IACjDH,eAAe,iBACf;oBACAlB,sBAAsBtE,IAAI,CAAC;wBACzBS;wBACAE;wBACAlB,WAAWR;wBACX2F,wBAAwB,CAAC;wBACzBY,YAAY,CAAC,GAAG,EAAEG,4CAAgC,CAAC,CAAC;wBACpDD,kBAAkBjB;oBACpB;gBACF;YACF;YAEA,2EAA2E;YAC3E,mBAAmB;YACnB,MAAMnF,oBAAoBtB,8BAA8BC;YACxD,KAAK,MAAM2H,uBAAuBtB,sBAAuB;gBACvD,MAAMuB,WAAW,IAAI,CAACC,8BAA8B,CAAC;oBACnD,GAAGF,mBAAmB;oBACtBG,eAAe;wBACb,GAAGH,oBAAoBhB,sBAAsB;wBAC7C,GAAG,AACDtF,CAAAA,iBAAiB,CAACsG,oBAAoBF,gBAAgB,CAAC,IAAI,EAAE,AAAD,EAC5DM,MAAM,CAAC,CAACC,KAAKC;4BACbD,GAAG,CAACC,KAAK,GAAG,IAAI1G;4BAChB,OAAOyG;wBACT,GAAG,CAAC,EAA4B;oBAClC;gBACF;gBAEA,2EAA2E;gBAC3E,IAAI,CAACpC,8BAA8B,CAAC+B,oBAAoBnG,SAAS,CAAC,EAAE;oBAClEoE,8BAA8B,CAAC+B,oBAAoBnG,SAAS,CAAC,GAAG,EAAE;gBACpE;gBACAoE,8BAA8B,CAAC+B,oBAAoBnG,SAAS,CAAC,CAACO,IAAI,CAChE6F,QAAQ,CAAC,EAAE;gBAGbjC,gCAAgC5D,IAAI,CAAC6F;YACvC;YAEA,sBAAsB;YACtBjC,gCAAgC5D,IAAI,CAClC,IAAI,CAAC8F,8BAA8B,CAAC;gBAClCrF;gBACAE;gBACAlB,WAAWR;gBACX8G,eAAe;oBAAE,GAAG5B,mCAAmC;gBAAC;gBACxDqB,YAAYW,gCAAoB;YAClC;YAGF,IAAI/B,mBAAmBgC,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACrC,kBAAkB,CAAC9E,KAAK,EAAE;oBAC7B8E,kBAAkB,CAAC9E,KAAK,GAAG,IAAIoF;gBACjC;gBACAN,kBAAkB,CAAC9E,KAAK,GAAG,IAAIoF,IAAI;uBAC9BN,kBAAkB,CAAC9E,KAAK;uBACxBmF;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAACnF,MAAMmF,mBAAmB,IAAIjG,OAAOC,OAAO,CACrD2F,oBACC;YACD,KAAK,MAAM,CAACkB,KAAKoB,YAAY,IAAIjC,mBAAoB;gBACnD,KAAK,MAAMkC,cAAcD,YAAa;oBACpCrC,eAAejE,GAAG,CAACd,OAAO,MAAMgG,MAAM,MAAMqB;gBAC9C;YACF;YACAxC,mBAAmB9D,IAAI,CACrB,IAAI,CAACuG,iBAAiB,CAAC;gBACrB9F;gBACAE;gBACA6F,SAASpC;gBACT3E,WAAWR;gBACXuG,YAAYvG;YACd;QAEJ;QAEA0B,YAAYD,KAAK,CAAC+F,aAAa,CAAClF,UAAU,CAAClE,aAAa;YACtD,MAAMqJ,6BAA6C,EAAE;YACrD,MAAMC,2BAAkE,CAAC;YAEzE,mEAAmE;YACnE,gBAAgB;YAChB,yEAAyE;YACzE,KAAK,MAAM,CAAC1H,MAAM2H,qBAAqB,IAAIzI,OAAOC,OAAO,CACvDyF,gCACC;gBACD,qEAAqE;gBACrE,sBAAsB;gBACtB,MAAMO,qBAAqB,IAAI,CAACyC,oCAAoC,CAAC;oBACnElG;oBACAM,cAAc2F;gBAChB;gBAEA,IAAIxC,mBAAmBgC,IAAI,GAAG,GAAG;oBAC/B,IAAI,CAACO,wBAAwB,CAAC1H,KAAK,EAAE;wBACnC0H,wBAAwB,CAAC1H,KAAK,GAAG,IAAIoF;oBACvC;oBACAsC,wBAAwB,CAAC1H,KAAK,GAAG,IAAIoF,IAAI;2BACpCsC,wBAAwB,CAAC1H,KAAK;2BAC9BmF;qBACJ;gBACH;YACF;YAEA,KAAK,MAAM,CAACnF,MAAMmF,mBAAmB,IAAIjG,OAAOC,OAAO,CACrDuI,0BACC;gBACD,uEAAuE;gBACvE,+CAA+C;gBAC/C,uEAAuE;gBACvE,mBAAmB;gBACnB,IAAIG,iCAAiC;gBACrC,MAAMC,8BAA8B,IAAI1C;gBACxC,KAAK,MAAM,CAACY,KAAKoB,YAAY,IAAIjC,mBAAoB;oBACnD,MAAM4C,uBAAuB,EAAE;oBAC/B,KAAK,MAAMV,cAAcD,YAAa;wBACpC,MAAMY,KAAKhI,OAAO,MAAMgG,MAAM,MAAMqB;wBACpC,IAAI,CAACtC,eAAepE,GAAG,CAACqH,KAAK;4BAC3BD,qBAAqBhH,IAAI,CAACsG;wBAC5B;oBACF;oBACA,IAAIU,qBAAqBpI,MAAM,GAAG,GAAG;wBACnCmI,4BAA4BhG,GAAG,CAACkE,KAAK+B;wBACrCF,iCAAiC;oBACnC;gBACF;gBAEA,IAAIA,gCAAgC;oBAClCJ,2BAA2B1G,IAAI,CAC7B,IAAI,CAACuG,iBAAiB,CAAC;wBACrB9F;wBACAE;wBACA6F,SAASO;wBACTtH,WAAWR;wBACXuG,YAAYvG;wBACZiI,YAAY;oBACd;gBAEJ;YACF;YAEA,MAAMC,QAAQC,GAAG,CAACV;YAClB;QACF;QAEA,qDAAqD;QACrD,MAAMW,cAAcC,IAAAA,oCAAc,EAAC7G,SAAS8G,UAAU;QACtD,4DAA4D;QAC5D,IACEF,eACAzD,gCAAgC4D,IAAI,CAClC,CAAC,CAACC,iBAAiB,GAAKA,qBAAqB,OAE/C;YACAJ,YAAYK,UAAU,CAAC;gBAACC,0BAAc,CAACC,MAAM;aAAC;QAChD;QAEA,qGAAqG;QACrG,6EAA6E;QAC7E,MAAMT,QAAQC,GAAG,CACfxD,gCAAgCiE,GAAG,CACjC,CAACC,8BAAgCA,2BAA2B,CAAC,EAAE;QAInE,uCAAuC;QACvC,MAAMX,QAAQC,GAAG,CAACtD;IACpB;IAEA+C,qCAAqC,EACnClG,WAAW,EACXM,YAAY,EAIb,EAAE;QACD,mCAAmC;QACnC,MAAM8G,mBAAmB,IAAI1D;QAE7B,gFAAgF;QAChF,MAAM2D,gBAAgB,IAAIxI;QAC1B,MAAMyI,eAAe,IAAIzI;QAEzB,MAAM0I,iBAAiB,CAAC,EACtBzD,YAAY,EACZM,cAAc,EAIf;YACC,MAAMoD,sBAAsB,CAACvG;oBAGHA,0BAKXA,2BAMTA;gBAbJ,IAAI,CAACA,KAAK;gBAEV,MAAMC,UAAkBD,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyB7C,IAAI,KAAI;gBACzD,mEAAmE;gBACnE,yEAAyE;gBACzE,0EAA0E;gBAC1E,IAAIqJ,aACFvG,UAAWD,CAAAA,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI,EAAC;gBAEhD,yEAAyE;gBACzE,yEAAyE;gBACzE,0EAA0E;gBAC1E,wEAAwE;gBACxE,KAAIL,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBO,UAAU,CAACC,sCAA0B,GAAG;oBAC7DgG,aAAaxG,IAAIE,aAAa,GAAG,MAAMsG;gBACzC;gBAEA,IAAI,CAACA,cAAcJ,cAAcpI,GAAG,CAACwI,aAAa;gBAClDJ,cAAcjI,GAAG,CAACqI;gBAElB,MAAM5B,UAAU6B,IAAAA,iBAAU,EAACzG;gBAC3B,IAAI4E,SAAS;oBACXuB,iBAAiBhH,GAAG,CAACqH,YAAY5B;gBACnC;gBAEAhC,IAAAA,kCAA0B,EAAC5C,KAAKjB,YAAYwC,WAAW,EAAE6B,OAAO,CAC9D,CAACT;oBACC4D,oBACE5D,WAAWQ,cAAc;gBAE7B;YAEJ;YAEA,yEAAyE;YACzE,IACEN,gBACA,CAACA,aAAa3E,QAAQ,CAAC,oCACvB;gBACA,2DAA2D;gBAC3DqI,oBAAoBpD;YACtB;QACF;QAEA,KAAK,MAAMuD,mBAAmBrH,aAAc;YAC1C,MAAMsH,iBACJ5H,YAAYwC,WAAW,CAACqF,iBAAiB,CAACF;YAC5C,KAAK,MAAM/D,cAAcC,IAAAA,kCAA0B,EACjD+D,gBACA5H,YAAYwC,WAAW,EACtB;gBACD,MAAMuB,aAAaH,WAAWG,UAAU;gBACxC,MAAMC,UAAU,AAACD,WAA+CC,OAAO;gBAEvE,oEAAoE;gBACpE,oEAAoE;gBACpE,IAAIsD,aAAarI,GAAG,CAAC+E,UAAU;gBAC/BsD,aAAalI,GAAG,CAAC4E;gBAEjBuD,eAAe;oBACbzD,cAAcE;oBACdI,gBAAgBR,WAAWQ,cAAc;gBAC3C;YACF;QACF;QAEA,OAAOgD;IACT;IAEAjD,8CAA8C,EAC5CL,YAAY,EACZ9D,WAAW,EACXoE,cAAc,EAKf,EAIC;QACA,gFAAgF;QAChF,MAAM0D,UAAU,IAAIjJ;QAEpB,mBAAmB;QACnB,MAAMoF,yBAAiD,CAAC;QACxD,MAAMC,gBAAsC,EAAE;QAC9C,MAAM6D,aAAa,IAAIlJ;QAEvB,MAAMmJ,yBAAyB,CAC7B/G,KACAgH;gBAMwBhH,0BAKZA,2BAWRA;YApBJ,IAAI,CAACA,KAAK;YAEV,MAAMiH,QAAQC,IAAAA,eAAQ,EAAClH;YAEvB,MAAMC,UAAkBD,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyB7C,IAAI,KAAI;YACzD,mEAAmE;YACnE,yEAAyE;YACzE,0EAA0E;YAC1E,IAAIqJ,aACFvG,YAAUD,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK;YAE1C,6EAA6E;YAC7E,IAAIL,IAAI3B,WAAW,CAAChB,IAAI,KAAK,iBAAiB;gBAC5CmJ,aAAa,AAACxG,IAAYmH,WAAW;YACvC;YAEA,yEAAyE;YACzE,yEAAyE;YACzE,0EAA0E;YAC1E,wEAAwE;YACxE,KAAInH,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBO,UAAU,CAACC,sCAA0B,GAAG;gBAC7DgG,aAAaxG,IAAIE,aAAa,GAAG,MAAMsG;YACzC;YAEA,IAAI,CAACA,YAAY;YACjB,IAAIK,QAAQ7I,GAAG,CAACwI,aAAa;gBAC3B,IAAIxD,sBAAsB,CAACwD,WAAW,EAAE;oBACtCY,gBACEpH,KACAwG,YACAxD,wBACAgE,qBACA;gBAEJ;gBACA;YACF;YACAH,QAAQ1I,GAAG,CAACqI;YAEZ,MAAM5B,UAAU6B,IAAAA,iBAAU,EAACzG;YAC3B,IAAI4E,SAAS;gBACX3B,cAAc7E,IAAI,CAAC;oBAACoI;oBAAY5B;iBAAQ;YAC1C;YAEA,MAAMyC,iBAAiB,IAAI,CAAC5I,YAAY,GACpC6I,gCAAoB,GACpBC,mCAAuB;YAE3B,IAAIN,OAAO;gBACT,MAAMO,iBACJxH,IAAIyH,WAAW,IAAI,AAACzH,IAAIyH,WAAW,CAASD,cAAc;gBAE5D,IAAIA,gBAAgB;oBAClB,MAAME,SAAS,CAAC3I,YAAYwC,WAAW,CACpCoG,cAAc,CAAC3H,KACf4H,YAAY,CAACP;oBAEhB,IAAIK,QAAQ;gBACd;gBAEAZ,WAAW3I,GAAG,CAACqI;YACjB,OAAO,IAAIqB,IAAAA,mCAA4B,EAAC7H,MAAM;gBAC5C,IAAI,CAACgD,sBAAsB,CAACwD,WAAW,EAAE;oBACvCxD,sBAAsB,CAACwD,WAAW,GAAG,IAAI5I;gBAC3C;gBACAwJ,gBACEpH,KACAwG,YACAxD,wBACAgE,qBACA;gBAGF;YACF;YAEApE,IAAAA,kCAA0B,EAAC5C,KAAKjB,YAAYwC,WAAW,EAAE6B,OAAO,CAC9D,CAACT;oBAMKA;gBALJ,IAAImF,gBAA0B,EAAE;gBAChC,MAAMC,YAAYpF,WAAWQ,cAAc;gBAE3C,mEAAmE;gBACnE,6CAA6C;gBAC7C,KAAIR,yBAAAA,WAAWG,UAAU,qBAArBH,uBAAuBqF,GAAG,EAAE;oBAC9BF,cAAc1J,IAAI,IAAIuE,WAAWG,UAAU,CAACkF,GAAG;gBACjD,OAAO;oBACLF,gBAAgB;wBAAC;qBAAI;gBACvB;gBAEAf,uBAAuBgB,WAAWD;YACpC;QAEJ;QAEA,2DAA2D;QAC3Df,uBAAuB5D,gBAAgB,EAAE;QAEzC,OAAO;YACLH;YACAlF,YAAYgJ,WAAWtC,IAAI,GACvB;gBACE,CAAC3B,aAAa,EAAEoF,MAAMC,IAAI,CAACpB;YAC7B,IACA,CAAC;YACL7D;QACF;IACF;IAEAiB,+BAA+B,EAC7BrF,QAAQ,EACRE,WAAW,EACXlB,SAAS,EACTsG,aAAa,EACbP,UAAU,EACVE,gBAAgB,EAQjB,EAIC;QACA,IAAI+B,mBAAmB;QAEvB,MAAMsC,gBAGF;YACFC,SAAS7L,OAAOkH,IAAI,CAACU,eAClB1H,IAAI,CAAC,CAACC,GAAGC,IAAO0L,eAAQ,CAACC,IAAI,CAAC3L,KAAK,IAAID,EAAE6L,aAAa,CAAC5L,IACvDsJ,GAAG,CAAC,CAACuC,mBAAsB,CAAA;oBAC1BzF,SAASyF;oBACTR,KAAK;2BAAI7D,aAAa,CAACqE,iBAAiB;qBAAC;gBAC3C,CAAA;YACFC,QAAQ;QACV;QAEA,uEAAuE;QACvE,0EAA0E;QAC1E,gBAAgB;QAChB,MAAMC,sBAAsB,CAAC,gCAAgC,EAAEC,IAAAA,sBAAS,EAAC;YACvEP,SAAS,AAAC,CAAA,IAAI,CAAC3J,YAAY,GACvB0J,cAAcC,OAAO,CAACnC,GAAG,CAAC,CAAC,EAAElD,OAAO,EAAEiF,GAAG,EAAE,GAAM,CAAA;oBAC/CjF,SAASA,QAAQ7B,OAAO,CACtB,mCACA,cAAcA,OAAO,CAAC,OAAO/D,aAAI,CAACyL,GAAG;oBAEvCZ;gBACF,CAAA,KACAG,cAAcC,OAAO,AAAD,EACtBnC,GAAG,CAAC,CAAC4C,IAAMC,KAAKH,SAAS,CAACE;YAC5BJ,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,MAAMM,kBAAkB,CAAC,gCAAgC,EAAEJ,IAAAA,sBAAS,EAAC;YACnEP,SAASD,cAAcC,OAAO,CAACnC,GAAG,CAAC,CAAC4C,IAAMC,KAAKH,SAAS,CAACE;YACzDJ,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,iCAAiC;QACjC,2CAA2C;QAC3C,IAAI,IAAI,CAAClK,GAAG,EAAE;YACZ,MAAM/B,UAAUwM,IAAAA,gCAAU,EAACnK,SAAS8G,UAAU;YAC9C,MAAMsD,UAAUC,IAAAA,iCAAW,EACzBnD,0BAAc,CAACC,MAAM,EACrBmD,qBAAU,CAACC,GAAG,EACdxF;YAGF,IAAI,CAACpH,OAAO,CAACyM,QAAQ,EAAE;gBACrBzM,OAAO,CAACyM,QAAQ,GAAG;oBACjBI,MAAMC,gCAAU,CAACC,WAAW;oBAC5BC,eAAe,IAAI5L,IAAI;wBAACC;qBAAU;oBAClC4L,uBAAuB3F;oBACvBF;oBACAb,SAAS2F;oBACTgB,SAAS;oBACTC,gBAAgBC,KAAKC,GAAG;gBAC1B;gBACAhE,mBAAmB;YACrB,OAAO;gBACL,MAAMiE,YAAYtN,OAAO,CAACyM,QAAQ;gBAClC,mCAAmC;gBACnC,IAAIa,UAAU/G,OAAO,KAAK2F,qBAAqB;oBAC7CoB,UAAU/G,OAAO,GAAG2F;oBACpB7C,mBAAmB;gBACrB;gBACA,IAAIiE,UAAUT,IAAI,KAAKC,gCAAU,CAACC,WAAW,EAAE;oBAC7CO,UAAUN,aAAa,CAACrL,GAAG,CAACN;gBAC9B;gBACAiM,UAAUJ,OAAO,GAAG;gBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;YACrC;QACF,OAAO;YACLnO,YAAYS,qBAAqB,CAACyH,WAAW,GAAG8E;QAClD;QAEA,qDAAqD;QACrD,MAAMqB,0BAA0B3K,gBAAO,CAAC4K,WAAW,CAACC,gBAAgB,CAClElB,iBACA;YACE1L,MAAMuG;QACR;QAGF,OAAO;YACLiC;YACA,6CAA6C;YAC7C,gGAAgG;YAChG,qEAAqE;YACrE,IAAI,CAACqE,QAAQ,CACXnL,aACA,6BAA6B;YAC7BF,SAASmC,OAAO,EAChB+I,yBACA;gBACE,+BAA+B;gBAC/B1M,MAAMQ;gBACN,6CAA6C;gBAC7C,iEAAiE;gBACjE8C,OAAOC,yBAAc,CAACC,mBAAmB;YAC3C;YAEFkJ;SACD;IACH;IAEApF,kBAAkB,EAChB9F,QAAQ,EACRE,WAAW,EACX6F,OAAO,EACP/G,SAAS,EACT+F,UAAU,EACV0B,UAAU,EAQX,EAAE;QACD,MAAM6E,eAAelC,MAAMC,IAAI,CAACtD,QAAQpI,OAAO;QAE/C,MAAM4N,eAAe,CAAC,gCAAgC,EAAEzB,IAAAA,sBAAS,EAAC;YAChE/D,SAASkE,KAAKH,SAAS,CAACwB;YACxBE,qBAAqB/E;QACvB,GAAG,CAAC,CAAC;QAEL,MAAMgF,+BAA+B,IAAI,CAAC7L,YAAY,GAClD/C,YAAYG,iBAAiB,GAC7BH,YAAYE,aAAa;QAC7B,KAAK,MAAM,CAAC2O,GAAGjH,MAAM,IAAI6G,aAAc;YACrC,KAAK,MAAM9M,QAAQiG,MAAO;gBACxB,MAAM+B,KAAKmF,IAAAA,uBAAgB,EAACD,GAAGlN;gBAC/B,IAAI,OAAOiN,4BAA4B,CAACjF,GAAG,KAAK,aAAa;oBAC3DiF,4BAA4B,CAACjF,GAAG,GAAG;wBACjCoF,SAAS,CAAC;wBACV9J,OAAO,CAAC;oBACV;gBACF;gBACA2J,4BAA4B,CAACjF,GAAG,CAACoF,OAAO,CAAC7G,WAAW,GAAG;gBACvD0G,4BAA4B,CAACjF,GAAG,CAAC1E,KAAK,CAACiD,WAAW,GAAG0B,aACjD1E,yBAAc,CAAC8J,aAAa,GAC5B9J,yBAAc,CAAC+J,qBAAqB;YAC1C;QACF;QAEA,0CAA0C;QAC1C,MAAMC,iBAAiBxL,gBAAO,CAAC4K,WAAW,CAACC,gBAAgB,CAACG,cAAc;YACxE/M,MAAMuG;QACR;QAEA,OAAO,IAAI,CAACsG,QAAQ,CAClBnL,aACA,6BAA6B;QAC7BF,SAASmC,OAAO,EAChB4J,gBACA;YACEvN,MAAMQ;YACN8C,OAAO2E,aACH1E,yBAAc,CAAC8J,aAAa,GAC5B9J,yBAAc,CAAC+J,qBAAqB;QAC1C;IAEJ;IAEAT,SACEnL,WAAgB,EAChBiC,OAAe,EACf8B,UAA8B,EAC9BxE,OAA6B,EACf,mBAAmB,GAAG;QACpC,OAAO,IAAIiH,QAAQ,CAACsF,SAASC;YAC3B,MAAMC,QAAQhM,YAAYvC,OAAO,CAACwO,GAAG,CAAC1M,QAAQjB,IAAI;YAClD0N,MAAME,mBAAmB,CAAC7M,IAAI,CAAC0E;YAC/B/D,YAAYD,KAAK,CAACoL,QAAQ,CAACgB,IAAI,CAACH,OAAOzM;YACvCS,YAAYoM,aAAa,CACvB;gBACEnK;gBACA8B;gBACAsI,aAAa;oBAAEC,aAAa/M,QAAQqC,KAAK;gBAAC;YAC5C,GACA,CAAC2K,KAAwBC;gBACvB,IAAID,KAAK;oBACPvM,YAAYD,KAAK,CAAC0M,WAAW,CAACN,IAAI,CAACpI,YAAYxE,SAASgN;oBACxD,OAAOR,OAAOQ;gBAChB;gBAEAvM,YAAYD,KAAK,CAAC2M,YAAY,CAACP,IAAI,CAACpI,YAAYxE,SAASiN;gBACzD,OAAOV,QAAQU;YACjB;QAEJ;IACF;IAEA,MAAMxJ,mBACJhD,WAAgC,EAChC+C,MAAqC,EACrC;QACA,MAAMlG,gBAAwC,CAAC;QAC/C,MAAMC,oBAA4C,CAAC;QAEnDsF,IAAAA,uBAAe,EAACpC,aAAa,CAACiB,KAAKoB,QAAQsK,YAAY3L;YACrD,yEAAyE;YACzE,IACE2L,WAAWrO,IAAI,IACf2C,IAAI+C,OAAO,IACXhD,SACA,kCAAkCuI,IAAI,CAACtI,IAAI+C,OAAO,GAClD;gBACA,MAAMuC,aAAa,4BAA4BgD,IAAI,CAACtI,IAAI+C,OAAO;gBAE/D,MAAM4I,UAAU,IAAI,CAAClN,YAAY,GAC7B/C,YAAYK,qBAAqB,GACjCL,YAAYI,iBAAiB;gBAEjC,IAAI,CAAC6P,OAAO,CAACD,WAAWrO,IAAI,CAAC,EAAE;oBAC7BsO,OAAO,CAACD,WAAWrO,IAAI,CAAC,GAAG,CAAC;gBAC9B;gBACAsO,OAAO,CAACD,WAAWrO,IAAI,CAAC,CAACiI,aAAa,WAAW,SAAS,GAAGvF;YAC/D;QACF;QAEA,IAAK,IAAIsF,MAAM3J,YAAYE,aAAa,CAAE;YACxC,MAAMgQ,SAASlQ,YAAYE,aAAa,CAACyJ,GAAG;YAC5C,IAAK,IAAIhI,QAAQuO,OAAOnB,OAAO,CAAE;gBAC/B,MAAM1K,QACJrE,YAAYI,iBAAiB,CAACuB,KAAK,CACjCuO,OAAOjL,KAAK,CAACtD,KAAK,KAAKuD,yBAAc,CAAC8J,aAAa,GAC/C,WACA,SACL;gBACHkB,OAAOnB,OAAO,CAACpN,KAAK,GAAG0C;YACzB;YACAnE,aAAa,CAACyJ,GAAG,GAAGuG;QACtB;QAEA,IAAK,IAAIvG,MAAM3J,YAAYG,iBAAiB,CAAE;YAC5C,MAAM+P,SAASlQ,YAAYG,iBAAiB,CAACwJ,GAAG;YAChD,IAAK,IAAIhI,QAAQuO,OAAOnB,OAAO,CAAE;gBAC/B,MAAM1K,QACJrE,YAAYK,qBAAqB,CAACsB,KAAK,CACrCuO,OAAOjL,KAAK,CAACtD,KAAK,KAAKuD,yBAAc,CAAC8J,aAAa,GAC/C,WACA,SACL;gBACHkB,OAAOnB,OAAO,CAACpN,KAAK,GAAG0C;YACzB;YACAlE,iBAAiB,CAACwJ,GAAG,GAAGuG;QAC1B;QAEA,MAAMC,OAAO/C,KAAKH,SAAS,CACzB;YACEmD,MAAMlQ;YACNmQ,MAAMlQ;YACN8C,eAAe,IAAI,CAACA,aAAa;QACnC,GACA,MACA,IAAI,CAACJ,GAAG,GAAG,IAAIyN;QAGjBlK,MAAM,CAAC,CAAC,EAAE,IAAI,CAACpD,WAAW,CAAC,EAAEuN,qCAAyB,CAAC,GAAG,CAAC,CAAC,GAC1D,IAAIC,gBAAO,CAACC,SAAS,CACnB,CAAC,2BAA2B,EAAErD,KAAKH,SAAS,CAACkD,MAAM,CAAC;QAExD/J,MAAM,CAAC,CAAC,EAAE,IAAI,CAACpD,WAAW,CAAC,EAAEuN,qCAAyB,CAAC,KAAK,CAAC,CAAC,GAC5D,IAAIC,gBAAO,CAACC,SAAS,CAACN;IAC1B;AACF;AAEA,SAASzE,gBACPpH,GAAyB,EACzBwG,UAAkB,EAClBxD,sBAA8C,EAC9CgE,mBAA6B,EAC7BoF,aAAsB;QAEEC;IAAxB,MAAMC,mBAAkBD,0BAAAA,IAAAA,sCAAkB,EAACrM,KAAKuM,GAAG,qBAA3BF,wBAA6BC,eAAe;IACpE,MAAME,cAAcF,oBAAoB;IACxC,MAAMG,oBAAoBC,IAAAA,sCAAoB,EAC5C1M,KACAwM,cAAc,aAAa;IAG7B,MAAMG,mBAAmB3J,sBAAsB,CAACwD,WAAW;IAE3D,IAAIQ,mBAAmB,CAAC,EAAE,KAAK,KAAK;QAClC,kEAAkE;QAClE,qDAAqD;QACrD,sCAAsC;QACtC,IAAI,CAACoF,iBAAiB;eAAIO;SAAiB,CAAC,EAAE,KAAK,KAAK;YACtD3J,sBAAsB,CAACwD,WAAW,GAAG,IAAI5I,IAAI;gBAAC;aAAI;QACpD;IACF,OAAO;QACL,MAAMgP,yBAAyBH,sBAAsB;QACrD,IAAIG,wBAAwB;YAC1B5J,sBAAsB,CAACwD,WAAW,GAAG,IAAI5I,IAAI;gBAAC;aAAI;QACpD,OAAO;YACL,gGAAgG;YAChG,oEAAoE;YACpE,KAAK,MAAMP,QAAQ2J,oBAAqB;gBACtC,mEAAmE;gBACnE,MAAM6F,qBAAqBL,eAAenP,SAAS;gBAEnD,kEAAkE;gBAClE,4DAA4D;gBAC5D,IAAIwP,oBAAoB;oBACtB7J,sBAAsB,CAACwD,WAAW,CAACrI,GAAG,CAAC;gBACzC;gBAEA6E,sBAAsB,CAACwD,WAAW,CAACrI,GAAG,CAACd;YACzC;QACF;IACF;AACF"}