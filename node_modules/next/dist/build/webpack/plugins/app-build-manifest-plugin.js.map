{"version": 3, "sources": ["../../../../src/build/webpack/plugins/app-build-manifest-plugin.ts"], "names": ["AppBuildManifestPlugin", "PLUGIN_NAME", "constructor", "options", "dev", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "webpack", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "make", "processAssets", "name", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "assets", "createAsset", "manifest", "pages", "mainFiles", "Set", "getEntrypointFiles", "entrypoints", "get", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "entrypoint", "values", "SYSTEM_ENTRYPOINTS", "has", "pagePath", "getAppRouteFromEntrypoint", "filesForPage", "json", "JSON", "stringify", "APP_BUILD_MANIFEST", "sources", "RawSource"], "mappings": ";;;;+BAmBaA;;;eAAAA;;;yBAnBoB;2BAK1B;qCAC4B;kFACG;;;;;;AAUtC,MAAMC,cAAc;AAEb,MAAMD;IAGXE,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;IACxB;IAEOC,MAAMC,QAAa,EAAE;QAC1BA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5BR,aACA,CAACO,aAAkB,EAAEE,mBAAmB,EAAO;YAC7CF,YAAYG,mBAAmB,CAACC,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrCL;YAEFF,YAAYQ,mBAAmB,CAACJ,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrC,IAAIF,gBAAO,CAACC,YAAY,CAACG,cAAc,CAACC,QAAQ;QAEpD;QAGFZ,SAASC,KAAK,CAACY,IAAI,CAACV,GAAG,CAACR,aAAa,CAACO;YACpCA,YAAYD,KAAK,CAACa,aAAa,CAACX,GAAG,CACjC;gBACEY,MAAMpB;gBACNqB,OAAOT,gBAAO,CAACU,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACC,SAAgB,IAAI,CAACC,WAAW,CAACD,QAAQjB;QAE9C;IACF;IAEQkB,YAAYD,MAAW,EAAEjB,WAAgC,EAAE;QACjE,MAAMmB,WAA6B;YACjCC,OAAO,CAAC;QACV;QAEA,MAAMC,YAAY,IAAIC,IACpBC,IAAAA,uCAAkB,EAChBvB,YAAYwB,WAAW,CAACC,GAAG,CAACC,+CAAoC;QAIpE,KAAK,MAAMC,cAAc3B,YAAYwB,WAAW,CAACI,MAAM,GAAI;YACzD,IAAI,CAACD,WAAWd,IAAI,EAAE;gBACpB;YACF;YAEA,IAAIgB,6BAAkB,CAACC,GAAG,CAACH,WAAWd,IAAI,GAAG;gBAC3C;YACF;YAEA,MAAMkB,WAAWC,IAAAA,kCAAyB,EAACL,WAAWd,IAAI;YAC1D,IAAI,CAACkB,UAAU;gBACb;YACF;YAEA,MAAME,eAAeV,IAAAA,uCAAkB,EAACI;YACxCR,SAASC,KAAK,CAACW,SAAS,GAAG;mBAAI,IAAIT,IAAI;uBAAID;uBAAcY;iBAAa;aAAE;QAC1E;QAEA,MAAMC,OAAOC,KAAKC,SAAS,CAACjB,UAAU,MAAM;QAE5CF,MAAM,CAACoB,6BAAkB,CAAC,GAAG,IAAIC,gBAAO,CAACC,SAAS,CAACL;IACrD;AACF"}