{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["getDefineEnv", "getDefineEnvPlugin", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "getNextPublicEnvironmentVariables", "defineEnv", "process", "env", "startsWith", "value", "getNextConfigEnv", "serializeDefineEnv", "defineEnvStringified", "JSON", "stringify", "getImageConfig", "dev", "deviceSizes", "images", "imageSizes", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "output", "isTurbopack", "clientRouterFilters", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "__NEXT_DEFINE_ENV", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "experimental", "ppr", "deploymentId", "manualClientBasePath", "isNaN", "Number", "staleTimes", "dynamic", "static", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "linkNoTouchStart", "assetPrefix", "undefined", "needsExperimentalReact", "options", "webpack", "DefinePlugin"], "mappings": ";;;;;;;;;;;;;;;IA8HgBA,YAAY;eAAZA;;IAuIAC,kBAAkB;eAAlBA;;;yBAhQQ;wCACe;AAEvC,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AAuCA;;CAEC,GACD,SAASC;IACP,MAAMC,YAAuB,CAAC;IAC9B,IAAK,MAAMP,OAAOQ,QAAQC,GAAG,CAAE;QAC7B,IAAIT,IAAIU,UAAU,CAAC,iBAAiB;YAClC,MAAMC,QAAQH,QAAQC,GAAG,CAACT,IAAI;YAC9B,IAAIW,OAAO;gBACTJ,SAAS,CAAC,CAAC,YAAY,EAAEP,IAAI,CAAC,CAAC,GAAGW;YACpC;QACF;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,SAASK,iBAAiBb,MAA0B;IAClD,sCAAsC;IACtC,MAAMQ,YAAuB,CAAC;IAC9B,MAAME,MAAMV,OAAOU,GAAG;IACtB,IAAK,MAAMT,OAAOS,IAAK;QACrB,MAAME,QAAQF,GAAG,CAACT,IAAI;QACtB,IAAIW,OAAO;YACTb,qBAAqBC,QAAQC;YAC7BO,SAAS,CAAC,CAAC,YAAY,EAAEP,IAAI,CAAC,CAAC,GAAGW;QACpC;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,SAASM,mBAAmBN,SAAoB;IAC9C,MAAMO,uBAA4C,CAAC;IACnD,IAAK,MAAMd,OAAOO,UAAW;QAC3B,MAAMI,QAAQJ,SAAS,CAACP,IAAI;QAC5Bc,oBAAoB,CAACd,IAAI,GAAGe,KAAKC,SAAS,CAACL;IAC7C;IAEA,OAAOG;AACT;AAEA,SAASG,eACPlB,MAA0B,EAC1BmB,GAAY;QASKnB,gBAKSA;IAZ1B,OAAO;QACL,iCAAiC;YAC/BoB,aAAapB,OAAOqB,MAAM,CAACD,WAAW;YACtCE,YAAYtB,OAAOqB,MAAM,CAACC,UAAU;YACpCC,MAAMvB,OAAOqB,MAAM,CAACE,IAAI;YACxBC,QAAQxB,OAAOqB,MAAM,CAACG,MAAM;YAC5BC,qBAAqBzB,OAAOqB,MAAM,CAACI,mBAAmB;YACtDC,WAAW,EAAE1B,2BAAAA,iBAAAA,OAAQqB,MAAM,qBAAdrB,eAAgB0B,WAAW;YACxC,GAAIP,MACA;gBACE,gEAAgE;gBAChEQ,SAAS3B,OAAOqB,MAAM,CAACM,OAAO;gBAC9BC,cAAc,GAAE5B,kBAAAA,OAAOqB,MAAM,qBAAbrB,gBAAe4B,cAAc;gBAC7CC,QAAQ7B,OAAO6B,MAAM;YACvB,IACA,CAAC,CAAC;QACR;IACF;AACF;AAEO,SAAShC,aAAa,EAC3BiC,WAAW,EACXC,mBAAmB,EACnB/B,MAAM,EACNmB,GAAG,EACHa,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EACK;QAmCNvC,iCAETA,kCAGSA,kCAETA,kCA6C6BA;IAtFrC,MAAMQ,YAAuB;QAC3B,+CAA+C;QAC/CgC,mBAAmB;QAEnB,GAAGjC,mCAAmC;QACtC,GAAGM,iBAAiBb,OAAO;QAC3B,GAAI,CAACoC,eACD,CAAC,IACD;YACEK,aACE;;;;aAIC,GACDhC,QAAQC,GAAG,CAACgC,0BAA0B,IAAI;QAC9C,CAAC;QACL,qBAAqBZ;QACrB,yBAAyBA;QACzB,6DAA6D;QAC7D,wBAAwBX,MAAM,gBAAgB;QAC9C,4BAA4BiB,eACxB,SACAE,eACA,WACA;QACJ,4BAA4B;QAC5B,0BAA0BtC,OAAO2C,YAAY,CAACC,GAAG,KAAK;QACtD,kCAAkC5C,OAAO6C,YAAY,IAAI;QACzD,6CAA6CZ,uBAAuB;QACpE,0CAA0CM,sBAAsB,EAAE;QAClE,8CACEvC,OAAO2C,YAAY,CAACG,oBAAoB,IAAI;QAC9C,sDAAsD9B,KAAKC,SAAS,CAClE8B,MAAMC,QAAOhD,kCAAAA,OAAO2C,YAAY,CAACM,UAAU,qBAA9BjD,gCAAgCkD,OAAO,KAChD,GAAG,aAAa;YAChBlD,mCAAAA,OAAO2C,YAAY,CAACM,UAAU,qBAA9BjD,iCAAgCkD,OAAO;QAE7C,qDAAqDlC,KAAKC,SAAS,CACjE8B,MAAMC,QAAOhD,mCAAAA,OAAO2C,YAAY,CAACM,UAAU,qBAA9BjD,iCAAgCmD,MAAM,KAC/C,IAAI,GAAG,YAAY;YACnBnD,mCAAAA,OAAO2C,YAAY,CAACM,UAAU,qBAA9BjD,iCAAgCmD,MAAM;QAE5C,mDACEnD,OAAO2C,YAAY,CAACS,kBAAkB,IAAI;QAC5C,6CACErB,CAAAA,uCAAAA,oBAAqBsB,YAAY,KAAI;QACvC,6CACEtB,CAAAA,uCAAAA,oBAAqBuB,aAAa,KAAI;QACxC,8CACEtD,OAAO2C,YAAY,CAACY,qBAAqB,IAAI;QAC/C,0CACEvD,OAAO2C,YAAY,CAACa,kBAAkB,IAAI;QAC5C,mCAAmCxD,OAAOyD,WAAW;QACrD,mBAAmBtB;QACnB,gCAAgC1B,QAAQC,GAAG,CAACgD,gBAAgB,IAAI;QAChE,2FAA2F;QAC3F,GAAIvC,OAAQgB,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+BJ;QACjC,IACA,CAAC,CAAC;QACN,qCAAqChC,OAAO2D,aAAa;QACzD,sCACE3D,OAAO4D,aAAa,CAACC,aAAa,IAAI;QACxC,+CACE7D,OAAO4D,aAAa,CAACE,qBAAqB,IAAI;QAChD,kCACE9D,OAAO+D,eAAe,KAAK,OAAO,QAAQ/D,OAAO+D,eAAe;QAClE,sCACE,6EAA6E;QAC7E/D,OAAO+D,eAAe,KAAK,OAAO,OAAO/D,OAAO+D,eAAe;QACjE,qCAAqC,CAAC5C,OAAOnB,OAAOgE,aAAa;QACjE,mCACE,AAAChE,CAAAA,OAAO2C,YAAY,CAACsB,WAAW,IAAI,CAAC9C,GAAE,KAAM;QAC/C,qCACE,AAACnB,CAAAA,OAAO2C,YAAY,CAACuB,iBAAiB,IAAI,CAAC/C,GAAE,KAAM;QACrD,yCACEnB,OAAO2C,YAAY,CAACwB,iBAAiB,IAAI;QAC3C,GAAGjD,eAAelB,QAAQmB,IAAI;QAC9B,sCAAsCnB,OAAOoE,QAAQ;QACrD,uCACEpE,OAAO2C,YAAY,CAAC0B,cAAc,IAAI;QACxC,mCAAmCnC;QACnC,oCAAoClC,OAAO6B,MAAM;QACjD,mCAAmC,CAAC,CAAC7B,OAAOsE,IAAI;QAChD,mCAAmCtE,EAAAA,eAAAA,OAAOsE,IAAI,qBAAXtE,aAAa2B,OAAO,KAAI;QAC3D,mCAAmC3B,OAAOuE,WAAW;QACrD,kDACEvE,OAAOwE,0BAA0B;QACnC,0DACExE,OAAO2C,YAAY,CAAC8B,iCAAiC,IAAI;QAC3D,4CACEzE,OAAO0E,yBAAyB;QAClC,iDACE,AAAC1E,CAAAA,OAAO2C,YAAY,CAACgC,oBAAoB,IACvC3E,OAAO2C,YAAY,CAACgC,oBAAoB,CAACC,MAAM,GAAG,CAAA,KACpD;QACF,6CACE5E,OAAO2C,YAAY,CAACgC,oBAAoB,IAAI;QAC9C,0CACE3E,OAAO2C,YAAY,CAACkC,gBAAgB,IAAI;QAC1C,mCAAmC7E,OAAO8E,WAAW;QACrD,GAAIzC,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiB;QACnB,IACA0C,SAAS;QACb,GAAI1C,0BACA;YACE,yCACE2C,IAAAA,8CAAsB,EAAChF;QAC3B,IACA+E,SAAS;IACf;IACA,OAAOjE,mBAAmBN;AAC5B;AAEO,SAASV,mBAAmBmF,OAA+B;IAChE,OAAO,IAAIC,gBAAO,CAACC,YAAY,CAACtF,aAAaoF;AAC/C"}