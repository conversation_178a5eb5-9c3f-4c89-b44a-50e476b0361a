/*
 React
 scheduler-unstable_mock.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';function f(a,b){var c=a.length;a.push(b);a:for(;0<c;){var d=c-1>>>1,e=a[d];if(0<g(e,b))a[d]=b,a[c]=e,c=d;else break a}}function h(a){return 0===a.length?null:a[0]}function k(a){if(0===a.length)return null;var b=a[0],c=a.pop();if(c!==b){a[0]=c;a:for(var d=0,e=a.length,z=e>>>1;d<z;){var u=2*(d+1)-1,A=a[u],v=u+1,F=a[v];if(0>g(A,c))v<e&&0>g(F,A)?(a[d]=F,a[v]=c,d=v):(a[d]=A,a[u]=c,d=u);else if(v<e&&0>g(F,c))a[d]=F,a[v]=c,d=v;else break a}}return b}
function g(a,b){var c=a.sortIndex-b.sortIndex;return 0!==c?c:a.id-b.id}var l=[],m=[],n=1,p=null,q=3,r=!1,t=!1,w=!1,x=0,y=null,B=null,C=-1,D=null,E=-1,G=!1,H=!1,I=!1,J=!1,K=!1;function L(a){for(var b=h(m);null!==b;){if(null===b.callback)k(m);else if(b.startTime<=a)k(m),b.sortIndex=b.expirationTime,f(l,b);else break;b=h(m)}}function M(a){w=!1;L(a);if(!t)if(null!==h(l))t=!0,y=N;else{var b=h(m);null!==b&&(a=b.startTime-a,B=M,C=x+a)}}
function N(a,b){t=!1;w&&(w=!1,B=null,C=-1);r=!0;var c=q;try{a:{L(b);for(p=h(l);null!==p&&(!(p.expirationTime>b)||a&&!O());){var d=p.callback;if("function"===typeof d){p.callback=null;q=p.priorityLevel;var e=d(p.expirationTime<=b);b=x;if("function"===typeof e){if(p.callback=e,L(b),J){var z=I=!0;break a}}else p===h(l)&&k(l),L(b)}else k(l);p=h(l)}if(null!==p)z=!0;else{var u=h(m);if(null!==u){var A=u.startTime-b;B=M;C=x+A}z=!1}}return z}finally{p=null,q=c,r=!1}}
function O(){return 0===E&&null===D||-1!==E&&null!==D&&D.length>=E||J&&I?G=!0:!1}function P(){if(H)throw Error("Already flushing work.");if(null!==y){var a=y;H=!0;try{var b=!0;do b=a(!0,x);while(b);b||(y=null);return!0}finally{H=!1}}else return!1}exports.log=function(a){"disabledLog"===console.log.name||K||(null===D?D=[a]:D.push(a))};exports.reset=function(){if(H)throw Error("Cannot reset while already flushing work.");x=0;B=y=null;C=-1;D=null;E=-1;I=H=G=!1};exports.unstable_IdlePriority=5;
exports.unstable_ImmediatePriority=1;exports.unstable_LowPriority=4;exports.unstable_NormalPriority=3;exports.unstable_Profiling=null;exports.unstable_UserBlockingPriority=2;exports.unstable_advanceTime=function(a){"disabledLog"===console.log.name||K||(x+=a,null!==B&&C<=x&&(B(x),C=-1,B=null))};exports.unstable_cancelCallback=function(a){a.callback=null};exports.unstable_clearLog=function(){if(null===D)return[];var a=D;D=null;return a};exports.unstable_continueExecution=function(){t||r||(t=!0,y=N)};
exports.unstable_flushAll=function(){if(null!==D)throw Error("Log is not empty. Assert on the log of yielded values before flushing additional work.");P();if(null!==D)throw Error("While flushing work, something yielded a value. Use an assertion helper to assert on the log of yielded values, e.g. expect(Scheduler).toFlushAndYield([...])");};exports.unstable_flushAllWithoutAsserting=P;
exports.unstable_flushExpired=function(){if(H)throw Error("Already flushing work.");if(null!==y){H=!0;try{y(!1,x)||(y=null)}finally{H=!1}}};exports.unstable_flushNumberOfYields=function(a){if(H)throw Error("Already flushing work.");if(null!==y){var b=y;E=a;H=!0;try{a=!0;do a=b(!0,x);while(a&&!G);a||(y=null)}finally{E=-1,H=G=!1}}};
exports.unstable_flushUntilNextPaint=function(){if(H)throw Error("Already flushing work.");if(null!==y){var a=y;J=!0;I=!1;H=!0;try{var b=!0;do b=a(!0,x);while(b&&!G);b||(y=null)}finally{H=G=J=!1}}return!1};exports.unstable_forceFrameRate=function(){};exports.unstable_getCurrentPriorityLevel=function(){return q};exports.unstable_getFirstCallbackNode=function(){return h(l)};exports.unstable_hasPendingWork=function(){return null!==y};
exports.unstable_next=function(a){switch(q){case 1:case 2:case 3:var b=3;break;default:b=q}var c=q;q=b;try{return a()}finally{q=c}};exports.unstable_now=function(){return x};exports.unstable_pauseExecution=function(){};exports.unstable_requestPaint=function(){I=!0};exports.unstable_runWithPriority=function(a,b){switch(a){case 1:case 2:case 3:case 4:case 5:break;default:a=3}var c=q;q=a;try{return b()}finally{q=c}};
exports.unstable_scheduleCallback=function(a,b,c){var d=x;"object"===typeof c&&null!==c?(c=c.delay,c="number"===typeof c&&0<c?d+c:d):c=d;switch(a){case 1:var e=-1;break;case 2:e=250;break;case 5:e=1073741823;break;case 4:e=1E4;break;default:e=5E3}e=c+e;a={id:n++,callback:b,priorityLevel:a,startTime:c,expirationTime:e,sortIndex:-1};c>d?(a.sortIndex=c,f(m,a),null===h(l)&&a===h(m)&&(w?(B=null,C=-1):w=!0,B=M,C=x+(c-d))):(a.sortIndex=e,f(l,a),t||r||(t=!0,y=N));return a};
exports.unstable_setDisableYieldValue=function(a){K=a};exports.unstable_shouldYield=O;exports.unstable_wrapCallback=function(a){var b=q;return function(){var c=q;q=b;try{return a.apply(this,arguments)}finally{q=c}}};

//# sourceMappingURL=scheduler-unstable_mock.production.min.js.map
