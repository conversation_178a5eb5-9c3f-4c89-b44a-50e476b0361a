/*
 React
 react-server-dom-turbopack-server.browser.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("react"),ba=require("react-dom"),l=null,p=0;function q(a,b){if(0!==b.byteLength)if(2048<b.byteLength)0<p&&(a.enqueue(new Uint8Array(l.buffer,0,p)),l=new Uint8Array(2048),p=0),a.enqueue(b);else{var c=l.length-p;c<b.byteLength&&(0===c?a.enqueue(l):(l.set(b.subarray(0,c),p),a.enqueue(l),b=b.subarray(c)),l=new Uint8Array(2048),p=0);l.set(b,p);p+=b.byteLength}return!0}var r=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var t=Symbol.for("react.client.reference"),u=Symbol.for("react.server.reference");function v(a,b,c){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:b},$$async:{value:c}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function ha(){var a=da.apply(this,arguments);if(this.$$typeof===u){var b=ea.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:u},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:ha}})}return a}
var ia=Promise.prototype,ja={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ka(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "__esModule":var c=a.$$id;a.default=v(function(){throw Error("Attempted to call the default export of "+c+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var e=v({},a.$$id,!0),d=new Proxy(e,la);a.status="fulfilled";a.value=d;return a.then=v(function(g){return Promise.resolve(g(d))},a.$$id+"#then",!1)}if("symbol"===typeof b)throw Error("Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.");e=a[b];e||(e=v(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(e,"name",{value:b}),e=a[b]=new Proxy(e,ja));return e}
var la={get:function(a,b){return ka(a,b)},getOwnPropertyDescriptor:function(a,b){var c=Object.getOwnPropertyDescriptor(a,b);c||(c={value:ka(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,c));return c},getPrototypeOf:function(){return ia},set:function(){throw Error("Cannot assign to a client module from a server module.");}},ta={prefetchDNS:ma,preconnect:na,preload:oa,preloadModule:pa,preinitStyle:qa,preinitScript:ra,preinitModuleScript:sa};
function ma(a){if("string"===typeof a&&a){var b=w?w:null;if(b){var c=b.hints,e="D|"+a;c.has(e)||(c.add(e),x(b,"D",a))}}}function na(a,b){if("string"===typeof a){var c=w?w:null;if(c){var e=c.hints,d="C|"+(null==b?"null":b)+"|"+a;e.has(d)||(e.add(d),"string"===typeof b?x(c,"C",[a,b]):x(c,"C",a))}}}
function oa(a,b,c){if("string"===typeof a){var e=w?w:null;if(e){var d=e.hints,g="L";if("image"===b&&c){var f=c.imageSrcSet,k=c.imageSizes,h="";"string"===typeof f&&""!==f?(h+="["+f+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;g+="[image]"+h}else g+="["+b+"]"+a;d.has(g)||(d.add(g),(c=z(c))?x(e,"L",[a,b,c]):x(e,"L",[a,b]))}}}function pa(a,b){if("string"===typeof a){var c=w?w:null;if(c){var e=c.hints,d="m|"+a;if(!e.has(d))return e.add(d),(b=z(b))?x(c,"m",[a,b]):x(c,"m",a)}}}
function qa(a,b,c){if("string"===typeof a){var e=w?w:null;if(e){var d=e.hints,g="S|"+a;if(!d.has(g))return d.add(g),(c=z(c))?x(e,"S",[a,"string"===typeof b?b:0,c]):"string"===typeof b?x(e,"S",[a,b]):x(e,"S",a)}}}function ra(a,b){if("string"===typeof a){var c=w?w:null;if(c){var e=c.hints,d="X|"+a;if(!e.has(d))return e.add(d),(b=z(b))?x(c,"X",[a,b]):x(c,"X",a)}}}
function sa(a,b){if("string"===typeof a){var c=w?w:null;if(c){var e=c.hints,d="M|"+a;if(!e.has(d))return e.add(d),(b=z(b))?x(c,"M",[a,b]):x(c,"M",a)}}}function z(a){if(null==a)return null;var b=!1,c={},e;for(e in a)null!=a[e]&&(b=!0,c[e]=a[e]);return b?c:null}
var ua=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,A=Symbol.for("react.element"),va=Symbol.for("react.fragment"),wa=Symbol.for("react.context"),xa=Symbol.for("react.forward_ref"),ya=Symbol.for("react.suspense"),za=Symbol.for("react.suspense_list"),Aa=Symbol.for("react.memo"),B=Symbol.for("react.lazy"),Ba=Symbol.for("react.memo_cache_sentinel"),D=Symbol.for("react.postpone"),Ca=Symbol.iterator,Da=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Ea(){}function Fa(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Ea,Ea),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(e){if("pending"===b.status){var d=b;d.status="fulfilled";d.value=e}},function(e){if("pending"===b.status){var d=b;d.status="rejected";d.reason=e}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Ga=b;throw Da;}}var Ga=null;
function Ha(){if(null===Ga)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Ga;Ga=null;return a}var E=null,Ia=0,F=null;function Ja(){var a=F||[];F=null;return a}
var Oa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:G,useTransition:G,readContext:Ka,useContext:Ka,useReducer:G,useRef:G,useState:G,useInsertionEffect:G,useLayoutEffect:G,useImperativeHandle:G,useEffect:G,useId:La,useSyncExternalStore:G,useCacheRefresh:function(){return Ma},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=Ba;return b},use:Na};
function G(){throw Error("This Hook is not supported in Server Components.");}function Ma(){throw Error("Refreshing the cache is not supported in Server Components.");}function Ka(){throw Error("Cannot read a Client Context from a Server Component.");}function La(){if(null===E)throw Error("useId can only be used while React is rendering");var a=E.identifierCount++;return":"+E.identifierPrefix+"S"+a.toString(32)+":"}
function Na(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Ia;Ia+=1;null===F&&(F=[]);return Fa(F,a,b)}a.$$typeof===wa&&Ka()}if(a.$$typeof===t){if(null!=a.value&&a.value.$$typeof===wa)throw Error("Cannot read a Client Context from a Server Component.");throw Error("Cannot use() an already resolved Client Reference.");}throw Error("An unsupported type was passed to use(): "+String(a));}function Pa(){return(new AbortController).signal}
function Qa(){var a=w?w:null;return a?a.cache:new Map}var Ra={getCacheSignal:function(){var a=Qa(),b=a.get(Pa);void 0===b&&(b=Pa(),a.set(Pa,b));return b},getCacheForType:function(a){var b=Qa(),c=b.get(a);void 0===c&&(c=a(),b.set(a,c));return c}},Sa=Array.isArray,Ta=Object.getPrototypeOf;function Ua(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,c){return c})}
function Va(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Sa(a))return"[...]";if(null!==a&&a.$$typeof===Wa)return"client";a=Ua(a);return"Object"===a?"{...}":a;case "function":return a.$$typeof===Wa?"client":(a=a.displayName||a.name)?"function "+a:"function";default:return String(a)}}
function Xa(a){if("string"===typeof a)return a;switch(a){case ya:return"Suspense";case za:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case xa:return Xa(a.render);case Aa:return Xa(a.type);case B:var b=a._payload;a=a._init;try{return Xa(a(b))}catch(c){}}return""}var Wa=Symbol.for("react.client.reference");
function H(a,b){var c=Ua(a);if("Object"!==c&&"Array"!==c)return c;c=-1;var e=0;if(Sa(a)){var d="[";for(var g=0;g<a.length;g++){0<g&&(d+=", ");var f=a[g];f="object"===typeof f&&null!==f?H(f):Va(f);""+g===b?(c=d.length,e=f.length,d+=f):d=10>f.length&&40>d.length+f.length?d+f:d+"..."}d+="]"}else if(a.$$typeof===A)d="<"+Xa(a.type)+"/>";else{if(a.$$typeof===Wa)return"client";d="{";g=Object.keys(a);for(f=0;f<g.length;f++){0<f&&(d+=", ");var k=g[f],h=JSON.stringify(k);d+=('"'+k+'"'===h?k:h)+": ";h=a[k];
h="object"===typeof h&&null!==h?H(h):Va(h);k===b?(c=d.length,e=h.length,d+=h):d=10>h.length&&40>d.length+h.length?d+h:d+"..."}d+="}"}return void 0===b?d:-1<c&&0<e?(a=" ".repeat(c)+"^".repeat(e),"\n  "+d+"\n  "+a):"\n  "+d}var Ya=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,I=aa.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!I)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var Za=Object.prototype,J=JSON.stringify,$a=I.TaintRegistryObjects,K=I.TaintRegistryValues,ab=I.TaintRegistryByteLengths,bb=I.TaintRegistryPendingRequests,cb=I.ReactCurrentCache,db=Ya.ReactCurrentDispatcher;function L(a){throw Error(a);}
function eb(a){a=a.taintCleanupQueue;bb.delete(a);for(var b=0;b<a.length;b++){var c=a[b],e=K.get(c);void 0!==e&&(1===e.count?K.delete(c):e.count--)}a.length=0}function fb(a){console.error(a)}function gb(){}
function hb(a,b,c,e,d){if(null!==cb.current&&cb.current!==Ra)throw Error("Currently React only supports one RSC renderer at a time.");ua.current=ta;cb.current=Ra;var g=new Set,f=[],k=[];bb.add(k);var h=new Set;b={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:h,abortableTasks:g,pingedTasks:f,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:k,onError:void 0===c?fb:c,onPostpone:void 0===d?gb:d};a=ib(b,a,null,!1,g);f.push(a);return b}var w=null;
function jb(a,b,c){var e=ib(a,null,b.keyPath,b.implicitSlot,a.abortableTasks);switch(c.status){case "fulfilled":return e.model=c.value,kb(a,e),e.id;case "rejected":return b=c.reason,"object"===typeof b&&null!==b&&b.$$typeof===D?(M(a,b.message),N(a,e.id)):(b=O(a,b),P(a,e.id,b)),e.id;default:"string"!==typeof c.status&&(c.status="pending",c.then(function(d){"pending"===c.status&&(c.status="fulfilled",c.value=d)},function(d){"pending"===c.status&&(c.status="rejected",c.reason=d)}))}c.then(function(d){e.model=
d;kb(a,e)},function(d){"object"===typeof d&&null!==d&&d.$$typeof===D?(M(a,d.message),N(a,e.id)):(e.status=4,d=O(a,d),P(a,e.id,d));a.abortableTasks.delete(e);null!==a.destination&&Q(a,a.destination)});return e.id}function x(a,b,c){c=J(c);var e=a.nextChunkId++;b="H"+b;b=e.toString(16)+":"+b;c=r.encode(b+c+"\n");a.completedHintChunks.push(c);!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(c=a.destination,a.flushScheduled=!0,Q(a,c))}
function lb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}function mb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:B,_payload:a,_init:lb}}
function nb(a,b,c,e,d){var g=b.thenableState;b.thenableState=null;Ia=0;F=g;e=e(d,void 0);if("object"===typeof e&&null!==e&&"function"===typeof e.then){d=e;if("fulfilled"===d.status)return d.value;e=mb(e)}d=b.keyPath;g=b.implicitSlot;null!==c?b.keyPath=null===d?c:d+","+c:null===d&&(b.implicitSlot=!0);a=R(a,b,ob,"",e);b.keyPath=d;b.implicitSlot=g;return a}function pb(a,b,c){return null!==b.keyPath?(a=[A,va,b.keyPath,{children:c}],b.implicitSlot?[a]:a):c}
function qb(a,b,c,e){var d=a.keyPath;null===c?c=d:null!==d&&(c=d+","+c);b=[A,b,c,e];return a.implicitSlot&&null!==c?[b]:b}
function rb(a,b,c,e,d,g){if(null!==d&&void 0!==d)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof c)return c.$$typeof===t?qb(b,c,e,g):nb(a,b,e,c,g);if("string"===typeof c)return qb(b,c,e,g);if("symbol"===typeof c)return c===va&&null===e?(e=b.implicitSlot,null===b.keyPath&&(b.implicitSlot=!0),a=R(a,b,ob,"",g.children),b.implicitSlot=e,a):qb(b,c,e,g);if(null!=c&&"object"===typeof c){if(c.$$typeof===t)return qb(b,c,e,g);switch(c.$$typeof){case B:var f=
c._init;c=f(c._payload);return rb(a,b,c,e,d,g);case xa:return nb(a,b,e,c.render,g);case Aa:return rb(a,b,c.type,e,d,g)}}throw Error("Unsupported Server Component type: "+Va(c));}function kb(a,b){var c=a.pingedTasks;c.push(b);1===c.length&&(a.flushScheduled=null!==a.destination,sb(a))}
function ib(a,b,c,e,d){a.pendingChunks++;var g=a.nextChunkId++;"object"!==typeof b||null===b||null!==c||e||a.writtenObjects.set(b,g);var f={id:g,status:0,model:b,keyPath:c,implicitSlot:e,ping:function(){return kb(a,f)},toJSON:function(k,h){a:{var m=f.keyPath,y=f.implicitSlot;try{var n=R(a,f,this,k,h)}catch(fa){k=fa===Da?Ha():fa;h=f.model;h="object"===typeof h&&null!==h&&(h.$$typeof===A||h.$$typeof===B);if("object"===typeof k&&null!==k){if("function"===typeof k.then){n=ib(a,f.model,f.keyPath,f.implicitSlot,
a.abortableTasks);var C=n.ping;k.then(C,C);n.thenableState=Ja();f.keyPath=m;f.implicitSlot=y;n=h?"$L"+n.id.toString(16):S(n.id);break a}if(k.$$typeof===D){a.pendingChunks++;n=a.nextChunkId++;M(a,k.message);N(a,n);f.keyPath=m;f.implicitSlot=y;n=h?"$L"+n.toString(16):S(n);break a}}f.keyPath=m;f.implicitSlot=y;if(h)a.pendingChunks++,m=a.nextChunkId++,y=O(a,k),P(a,m,y),n="$L"+m.toString(16);else throw k;}}return n},thenableState:null};d.add(f);return f}function S(a){return"$"+a.toString(16)}
function tb(a,b,c){a=J(c);b=b.toString(16)+":"+a+"\n";return r.encode(b)}
function ub(a,b,c,e){var d=e.$$async?e.$$id+"#async":e.$$id,g=a.writtenClientReferences,f=g.get(d);if(void 0!==f)return b[0]===A&&"1"===c?"$L"+f.toString(16):S(f);try{var k=a.bundlerConfig,h=e.$$id;f="";var m=k[h];if(m)f=m.name;else{var y=h.lastIndexOf("#");-1!==y&&(f=h.slice(y+1),m=k[h.slice(0,y)]);if(!m)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var n=!0===e.$$async?[m.id,m.chunks,f,1]:[m.id,m.chunks,
f];a.pendingChunks++;var C=a.nextChunkId++,fa=J(n),Mb=C.toString(16)+":I"+fa+"\n",Nb=r.encode(Mb);a.completedImportChunks.push(Nb);g.set(d,C);return b[0]===A&&"1"===c?"$L"+C.toString(16):S(C)}catch(Ob){return a.pendingChunks++,b=a.nextChunkId++,c=O(a,Ob),P(a,b,c),S(b)}}function T(a,b){b=ib(a,b,null,!1,a.abortableTasks);vb(a,b);return b.id}
function U(a,b,c){if(ab.has(c.byteLength)){var e=K.get(String.fromCharCode.apply(String,new Uint8Array(c.buffer,c.byteOffset,c.byteLength)));void 0!==e&&L(e.message)}a.pendingChunks+=2;e=a.nextChunkId++;var d=new Uint8Array(c.buffer,c.byteOffset,c.byteLength);c=2048<c.byteLength?d.slice():d;d=c.byteLength;b=e.toString(16)+":"+b+d.toString(16)+",";b=r.encode(b);a.completedRegularChunks.push(b,c);return S(e)}var V=!1;
function R(a,b,c,e,d){b.model=d;if(d===A)return"$";if(null===d)return null;if("object"===typeof d){switch(d.$$typeof){case A:c=a.writtenObjects;e=c.get(d);if(void 0!==e){if(null===b.keyPath&&!b.implicitSlot)if(V===d)V=null;else return-1===e?(a=T(a,d),S(a)):S(e)}else c.set(d,-1);c=d.props;e=c.ref;return rb(a,b,d.type,d.key,void 0!==e?e:null,c);case B:return b.thenableState=null,c=d._init,d=c(d._payload),R(a,b,ob,"",d)}if(d.$$typeof===t)return ub(a,c,e,d);c=$a.get(d);void 0!==c&&L(c);c=a.writtenObjects;
e=c.get(d);if("function"===typeof d.then){if(void 0!==e){if(null!==b.keyPath||b.implicitSlot)return"$@"+jb(a,b,d).toString(16);if(V===d)V=null;else return"$@"+e.toString(16)}a=jb(a,b,d);c.set(d,a);return"$@"+a.toString(16)}if(void 0!==e)if(V===d)V=null;else return-1===e?(a=T(a,d),S(a)):S(e);else c.set(d,-1);if(Sa(d))return pb(a,b,d);if(d instanceof Map){d=Array.from(d);for(b=0;b<d.length;b++)c=d[b][0],"object"===typeof c&&null!==c&&(e=a.writtenObjects,void 0===e.get(c)&&e.set(c,-1));return"$Q"+T(a,
d).toString(16)}if(d instanceof Set){d=Array.from(d);for(b=0;b<d.length;b++)c=d[b],"object"===typeof c&&null!==c&&(e=a.writtenObjects,void 0===e.get(c)&&e.set(c,-1));return"$W"+T(a,d).toString(16)}if(d instanceof ArrayBuffer)return U(a,"A",new Uint8Array(d));if(d instanceof Int8Array)return U(a,"C",d);if(d instanceof Uint8Array)return U(a,"c",d);if(d instanceof Uint8ClampedArray)return U(a,"U",d);if(d instanceof Int16Array)return U(a,"S",d);if(d instanceof Uint16Array)return U(a,"s",d);if(d instanceof
Int32Array)return U(a,"L",d);if(d instanceof Uint32Array)return U(a,"l",d);if(d instanceof Float32Array)return U(a,"F",d);if(d instanceof Float64Array)return U(a,"d",d);if(d instanceof BigInt64Array)return U(a,"N",d);if(d instanceof BigUint64Array)return U(a,"m",d);if(d instanceof DataView)return U(a,"V",d);null===d||"object"!==typeof d?c=null:(c=Ca&&d[Ca]||d["@@iterator"],c="function"===typeof c?c:null);if(c)return pb(a,b,Array.from(d));a=Ta(d);if(a!==Za&&(null===a||null!==Ta(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");
return d}if("string"===typeof d){b=K.get(d);void 0!==b&&L(b.message);if("Z"===d[d.length-1]&&c[e]instanceof Date)return"$D"+d;if(1024<=d.length)return a.pendingChunks+=2,b=a.nextChunkId++,d=r.encode(d),c=d.byteLength,c=b.toString(16)+":T"+c.toString(16)+",",c=r.encode(c),a.completedRegularChunks.push(c,d),S(b);a="$"===d[0]?"$"+d:d;return a}if("boolean"===typeof d)return d;if("number"===typeof d)return Number.isFinite(d)?0===d&&-Infinity===1/d?"$-0":d:Infinity===d?"$Infinity":-Infinity===d?"$-Infinity":
"$NaN";if("undefined"===typeof d)return"$undefined";if("function"===typeof d){if(d.$$typeof===t)return ub(a,c,e,d);if(d.$$typeof===u)return b=a.writtenServerReferences,c=b.get(d),void 0!==c?a="$F"+c.toString(16):(c=d.$$bound,c={id:d.$$id,bound:c?Promise.resolve(c):null},a=T(a,c),b.set(d,a),a="$F"+a.toString(16)),a;a=$a.get(d);void 0!==a&&L(a);if(/^on[A-Z]/.test(e))throw Error("Event handlers cannot be passed to Client Component props."+H(c,e)+"\nIf you need interactivity, consider converting part of this to a Client Component.");
throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server". Or maybe you meant to call this function rather than return it.'+H(c,e));}if("symbol"===typeof d){b=a.writtenSymbols;var g=b.get(d);if(void 0!==g)return S(g);g=d.description;if(Symbol.for(g)!==d)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(d.description+") cannot be found among global symbols.")+
H(c,e));a.pendingChunks++;c=a.nextChunkId++;e=tb(a,c,"$S"+g);a.completedImportChunks.push(e);b.set(d,c);return S(c)}if("bigint"===typeof d)return a=K.get(d),void 0!==a&&L(a.message),"$n"+d.toString(10);throw Error("Type "+typeof d+" is not supported in Client Component props."+H(c,e));}function M(a,b){var c=w;w=null;try{var e=a.onPostpone;e(b)}finally{w=c}}
function O(a,b){var c=w;w=null;try{var e=a.onError;var d=e(b)}finally{w=c}if(null!=d&&"string"!==typeof d)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof d+'" instead');return d||""}function wb(a,b){eb(a);null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}
function N(a,b){b=b.toString(16)+":P\n";b=r.encode(b);a.completedErrorChunks.push(b)}function P(a,b,c){c={digest:c};b=b.toString(16)+":E"+J(c)+"\n";b=r.encode(b);a.completedErrorChunks.push(b)}var ob={};
function vb(a,b){if(0===b.status)try{V=b.model;var c=R(a,b,ob,"",b.model);V=c;b.keyPath=null;b.implicitSlot=!1;var e="object"===typeof c&&null!==c?J(c,b.toJSON):J(c),d=b.id.toString(16)+":"+e+"\n",g=r.encode(d);a.completedRegularChunks.push(g);a.abortableTasks.delete(b);b.status=1}catch(m){var f=m===Da?Ha():m;if("object"===typeof f&&null!==f){if("function"===typeof f.then){var k=b.ping;f.then(k,k);b.thenableState=Ja();return}if(f.$$typeof===D){a.abortableTasks.delete(b);b.status=4;M(a,f.message);
N(a,b.id);return}}a.abortableTasks.delete(b);b.status=4;var h=O(a,f);P(a,b.id,h)}finally{}}function sb(a){var b=db.current;db.current=Oa;var c=w;E=w=a;try{var e=a.pingedTasks;a.pingedTasks=[];for(var d=0;d<e.length;d++)vb(a,e[d]);null!==a.destination&&Q(a,a.destination)}catch(g){O(a,g),wb(a,g)}finally{db.current=b,E=null,w=c}}
function Q(a,b){l=new Uint8Array(2048);p=0;try{for(var c=a.completedImportChunks,e=0;e<c.length;e++)a.pendingChunks--,q(b,c[e]);c.splice(0,e);var d=a.completedHintChunks;for(e=0;e<d.length;e++)q(b,d[e]);d.splice(0,e);var g=a.completedRegularChunks;for(e=0;e<g.length;e++)a.pendingChunks--,q(b,g[e]);g.splice(0,e);var f=a.completedErrorChunks;for(e=0;e<f.length;e++)a.pendingChunks--,q(b,f[e]);f.splice(0,e)}finally{a.flushScheduled=!1,l&&0<p&&(b.enqueue(new Uint8Array(l.buffer,0,p)),l=null,p=0)}0===a.pendingChunks&&
(eb(a),b.close())}function xb(a,b){try{var c=a.abortableTasks;if(0<c.size){a.pendingChunks++;var e=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===D)M(a,b.message),N(a,e,b);else{var d=void 0===b?Error("The render was aborted by the server without a reason."):b,g=O(a,d);P(a,e,g,d)}c.forEach(function(f){f.status=3;var k=S(e);f=tb(a,f.id,k);a.completedErrorChunks.push(f)});c.clear()}null!==a.destination&&Q(a,a.destination)}catch(f){O(a,f),wb(a,f)}}
function yb(a,b){var c="",e=a[b];if(e)c=e.name;else{var d=b.lastIndexOf("#");-1!==d&&(c=b.slice(d+1),e=a[b.slice(0,d)]);if(!e)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[e.id,e.chunks,c]}var zb=new Map;
function Ab(a){var b=__turbopack_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function Bb(){}
function Cb(a){for(var b=a[1],c=[],e=0;e<b.length;e++){var d=b[e],g=zb.get(d);if(void 0===g){g=__turbopack_load__(d);c.push(g);var f=zb.set.bind(zb,d,null);g.then(f,Bb);zb.set(d,g)}else null!==g&&c.push(g)}return 4===a.length?0===c.length?Ab(a[0]):Promise.all(c).then(function(){return Ab(a[0])}):0<c.length?Promise.all(c):null}
function W(a){var b=__turbopack_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function Db(a,b,c,e){this.status=a;this.value=b;this.reason=c;this._response=e}Db.prototype=Object.create(Promise.prototype);
Db.prototype.then=function(a,b){switch(this.status){case "resolved_model":Eb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Fb(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}
function Gb(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&Fb(c,b)}}function Hb(a,b,c,e,d,g){var f=yb(a._bundlerConfig,b);a=Cb(f);if(c)c=Promise.all([c,a]).then(function(k){k=k[0];var h=W(f);return h.bind.apply(h,[null].concat(k))});else if(a)c=Promise.resolve(a).then(function(){return W(f)});else return W(f);c.then(Ib(e,d,g),Jb(e));return null}var X=null,Y=null;
function Eb(a){var b=X,c=Y;X=a;Y=null;try{var e=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=e,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=e)}catch(d){a.status="rejected",a.reason=d}finally{X=b,Y=c}}function Kb(a,b){a._chunks.forEach(function(c){"pending"===c.status&&Gb(c,b)})}
function Z(a,b){var c=a._chunks,e=c.get(b);e||(e=a._formData.get(a._prefix+b),e=null!=e?new Db("resolved_model",e,null,a):new Db("pending",null,null,a),c.set(b,e));return e}function Ib(a,b,c){if(Y){var e=Y;e.deps++}else e=Y={deps:1,value:null};return function(d){b[c]=d;e.deps--;0===e.deps&&"blocked"===a.status&&(d=a.value,a.status="fulfilled",a.value=e.value,null!==d&&Fb(d,e.value))}}function Jb(a){return function(b){return Gb(a,b)}}
function Lb(a,b){a=Z(a,b);"resolved_model"===a.status&&Eb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Pb(a,b,c,e){if("$"===e[0])switch(e[1]){case "$":return e.slice(1);case "@":return b=parseInt(e.slice(2),16),Z(a,b);case "S":return Symbol.for(e.slice(2));case "F":return e=parseInt(e.slice(2),16),e=Lb(a,e),Hb(a,e.id,e.bound,X,b,c);case "Q":return b=parseInt(e.slice(2),16),a=Lb(a,b),new Map(a);case "W":return b=parseInt(e.slice(2),16),a=Lb(a,b),new Set(a);case "K":b=e.slice(2);var d=a._prefix+b+"_",g=new FormData;a._formData.forEach(function(f,k){k.startsWith(d)&&g.append(k.slice(d.length),
f)});return g;case "I":return Infinity;case "-":return"$-0"===e?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(e.slice(2)));case "n":return BigInt(e.slice(2));default:e=parseInt(e.slice(1),16);a=Z(a,e);switch(a.status){case "resolved_model":Eb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return e=X,a.then(Ib(e,b,c),Jb(e)),null;default:throw a.reason;}}return e}
function Qb(a,b){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,e=new Map,d={_bundlerConfig:a,_prefix:b,_formData:c,_chunks:e,_fromJSON:function(g,f){return"string"===typeof f?Pb(d,this,g,f):f}};return d}function Rb(a){Kb(a,Error("Connection closed."))}function Sb(a,b,c){var e=yb(a,b);a=Cb(e);return c?Promise.all([c,a]).then(function(d){d=d[0];var g=W(e);return g.bind.apply(g,[null].concat(d))}):a?Promise.resolve(a).then(function(){return W(e)}):Promise.resolve(W(e))}
function Tb(a,b,c){a=Qb(b,c,a);Rb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=v({},a,!1);return new Proxy(a,la)};
exports.decodeAction=function(a,b){var c=new FormData,e=null;a.forEach(function(d,g){g.startsWith("$ACTION_")?g.startsWith("$ACTION_REF_")?(d="$ACTION_"+g.slice(12)+":",d=Tb(a,b,d),e=Sb(b,d.id,d.bound)):g.startsWith("$ACTION_ID_")&&(d=g.slice(11),e=Sb(b,d,null)):c.append(g,d)});return null===e?null:e.then(function(d){return d.bind(null,c)})};exports.decodeReply=function(a,b){if("string"===typeof a){var c=new FormData;c.append("0",a);a=c}a=Qb(b,"",a);b=Z(a,0);Rb(a);return b};
exports.registerClientReference=function(a,b,c){return v(a,b+"#"+c,!1)};exports.registerServerReference=function(a,b,c){return Object.defineProperties(a,{$$typeof:{value:u},$$id:{value:null===c?b:b+"#"+c,configurable:!0},$$bound:{value:null,configurable:!0},bind:{value:ha,configurable:!0}})};
exports.renderToReadableStream=function(a,b,c){var e=hb(a,b,c?c.onError:void 0,c?c.identifierPrefix:void 0,c?c.onPostpone:void 0);if(c&&c.signal){var d=c.signal;if(d.aborted)xb(e,d.reason);else{var g=function(){xb(e,d.reason);d.removeEventListener("abort",g)};d.addEventListener("abort",g)}}return new ReadableStream({type:"bytes",start:function(){e.flushScheduled=null!==e.destination;sb(e)},pull:function(f){if(1===e.status)e.status=2,ca(f,e.fatalError);else if(2!==e.status&&null===e.destination){e.destination=
f;try{Q(e,f)}catch(k){O(e,k),wb(e,k)}}},cancel:function(){}},{highWaterMark:0})};

//# sourceMappingURL=react-server-dom-turbopack-server.browser.production.min.js.map
