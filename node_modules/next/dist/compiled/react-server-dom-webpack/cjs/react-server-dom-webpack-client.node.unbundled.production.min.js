/*
 React
 react-server-dom-webpack-client.node.unbundled.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var p=require("util"),r=require("react-dom"),t={stream:!0};function v(a,c){var d=a[c[0]];if(a=d[c[2]])d=a.name;else{a=d["*"];if(!a)throw Error('Could not find the module "'+c[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');d=c[2]}return{specifier:a.specifier,name:d,async:4===c.length}}var w=new Map;
function x(a){var c=w.get(a.specifier);if(c)return"fulfilled"===c.status?null:c;var d=import(a.specifier);a.async&&(d=d.then(function(b){return b.default}));d.then(function(b){var g=d;g.status="fulfilled";g.value=b},function(b){var g=d;g.status="rejected";g.reason=b});w.set(a.specifier,d);return d}
function y(a,c,d){if(null!==a)for(var b=1;b<c.length;b+=2){var g=d,h=z.current;if(h){var l=h.preinitScript,k=a.prefix+c[b];var e=a.crossOrigin;e="string"===typeof e?"use-credentials"===e?e:"":void 0;l.call(h,k,{crossOrigin:e,nonce:g})}}}var z=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,A=Symbol.for("react.element"),B=Symbol.for("react.lazy"),C=Symbol.iterator;
function E(a){if(null===a||"object"!==typeof a)return null;a=C&&a[C]||a["@@iterator"];return"function"===typeof a?a:null}var F=Array.isArray,G=Object.getPrototypeOf,H=Object.prototype,I=new WeakMap;function aa(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function ba(a,c,d,b){function g(e,f){if(null===f)return null;if("object"===typeof f){if("function"===typeof f.then){null===k&&(k=new FormData);l++;var u=h++;f.then(function(n){n=JSON.stringify(n,g);var q=k;q.append(c+u,n);l--;0===l&&d(q)},function(n){b(n)});return"$@"+u.toString(16)}if(F(f))return f;if(f instanceof FormData){null===k&&(k=new FormData);var D=k;e=h++;var m=c+e+"_";f.forEach(function(n,q){D.append(m+q,n)});return"$K"+e.toString(16)}if(f instanceof Map)return f=JSON.stringify(Array.from(f),
g),null===k&&(k=new FormData),e=h++,k.append(c+e,f),"$Q"+e.toString(16);if(f instanceof Set)return f=JSON.stringify(Array.from(f),g),null===k&&(k=new FormData),e=h++,k.append(c+e,f),"$W"+e.toString(16);if(E(f))return Array.from(f);e=G(f);if(e!==H&&(null===e||null!==G(e)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return f}if("string"===typeof f){if("Z"===f[f.length-1]&&this[e]instanceof Date)return"$D"+f;f=
"$"===f[0]?"$"+f:f;return f}if("boolean"===typeof f)return f;if("number"===typeof f)return aa(f);if("undefined"===typeof f)return"$undefined";if("function"===typeof f){f=I.get(f);if(void 0!==f)return f=JSON.stringify(f,g),null===k&&(k=new FormData),e=h++,k.set(c+e,f),"$F"+e.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof f){e=f.description;if(Symbol.for(e)!==f)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+
(f.description+") cannot be found among global symbols."));return"$S"+e}if("bigint"===typeof f)return"$n"+f.toString(10);throw Error("Type "+typeof f+" is not supported as an argument to a Server Function.");}var h=1,l=0,k=null;a=JSON.stringify(a,g);null===k?d(a):(k.set(c+"0",a),0===l&&d(k))}var J=new WeakMap;
function ca(a){var c,d,b=new Promise(function(g,h){c=g;d=h});ba(a,"",function(g){if("string"===typeof g){var h=new FormData;h.append("0",g);g=h}b.status="fulfilled";b.value=g;c(g)},function(g){b.status="rejected";b.reason=g;d(g)});return b}
function da(a){var c=I.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var d=null;if(null!==c.bound){d=J.get(c);d||(d=ca(c),J.set(c,d));if("rejected"===d.status)throw d.reason;if("fulfilled"!==d.status)throw d;c=d.value;var b=new FormData;c.forEach(function(g,h){b.append("$ACTION_"+a+":"+h,g)});d=b;c="$ACTION_REF_"+a}else c="$ACTION_ID_"+c.id;return{name:c,method:"POST",encType:"multipart/form-data",data:d}}
function K(a,c){var d=I.get(this);if(!d)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(d.id!==a)return!1;var b=d.bound;if(null===b)return 0===c;switch(b.status){case "fulfilled":return b.value.length===c;case "pending":throw b;case "rejected":throw b.reason;default:throw"string"!==typeof b.status&&(b.status="pending",b.then(function(g){b.status="fulfilled";b.value=g},function(g){b.status="rejected";b.reason=g})),b;}}
function L(a,c,d){Object.defineProperties(a,{$$FORM_ACTION:{value:void 0===d?da:function(){var b=I.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var g=b.bound;null===g&&(g=Promise.resolve([]));return d(b.id,g)}},$$IS_SIGNATURE_EQUAL:{value:K},bind:{value:M}});I.set(a,c)}var ea=Function.prototype.bind,fa=Array.prototype.slice;
function M(){var a=ea.apply(this,arguments),c=I.get(this);if(c){var d=fa.call(arguments,1),b=null;b=null!==c.bound?Promise.resolve(c.bound).then(function(g){return g.concat(d)}):Promise.resolve(d);Object.defineProperties(a,{$$FORM_ACTION:{value:this.$$FORM_ACTION},$$IS_SIGNATURE_EQUAL:{value:K},bind:{value:M}});I.set(a,{id:c.id,bound:b})}return a}function ha(a,c,d){function b(){var g=Array.prototype.slice.call(arguments);return c(a,g)}L(b,{id:a,bound:null},d);return b}
function N(a,c,d,b){this.status=a;this.value=c;this.reason=d;this._response=b}N.prototype=Object.create(Promise.prototype);N.prototype.then=function(a,c){switch(this.status){case "resolved_model":O(this);break;case "resolved_module":P(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":case "cyclic":a&&(null===this.value&&(this.value=[]),this.value.push(a));c&&(null===this.reason&&(this.reason=[]),this.reason.push(c));break;default:c(this.reason)}};
function ia(a){switch(a.status){case "resolved_model":O(a);break;case "resolved_module":P(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":throw a;default:throw a.reason;}}function Q(a,c){for(var d=0;d<a.length;d++)(0,a[d])(c)}function R(a,c,d){switch(a.status){case "fulfilled":Q(c,a.value);break;case "pending":case "blocked":case "cyclic":a.value=c;a.reason=d;break;case "rejected":d&&Q(d,a.reason)}}
function S(a,c){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=c;null!==d&&Q(d,c)}}function T(a,c){if("pending"===a.status||"blocked"===a.status){var d=a.value,b=a.reason;a.status="resolved_module";a.value=c;null!==d&&(P(a),R(a,d,b))}}var U=null,V=null;
function O(a){var c=U,d=V;U=a;V=null;var b=a.value;a.status="cyclic";a.value=null;a.reason=null;try{var g=JSON.parse(b,a._response._fromJSON);if(null!==V&&0<V.deps)V.value=g,a.status="blocked",a.value=null,a.reason=null;else{var h=a.value;a.status="fulfilled";a.value=g;null!==h&&Q(h,g)}}catch(l){a.status="rejected",a.reason=l}finally{U=c,V=d}}
function P(a){try{var c=a.value,d=w.get(c.specifier);if("fulfilled"===d.status)var b=d.value;else throw d.reason;var g="*"===c.name?b:""===c.name?b.default:b[c.name];a.status="fulfilled";a.value=g}catch(h){a.status="rejected",a.reason=h}}function W(a,c){a._chunks.forEach(function(d){"pending"===d.status&&S(d,c)})}function X(a,c){var d=a._chunks,b=d.get(c);b||(b=new N("pending",null,null,a),d.set(c,b));return b}
function ja(a,c,d,b){if(V){var g=V;b||g.deps++}else g=V={deps:b?0:1,value:null};return function(h){c[d]=h;g.deps--;0===g.deps&&"blocked"===a.status&&(h=a.value,a.status="fulfilled",a.value=g.value,null!==h&&Q(h,g.value))}}function ka(a){return function(c){return S(a,c)}}
function la(a,c){function d(){var g=Array.prototype.slice.call(arguments),h=c.bound;return h?"fulfilled"===h.status?b(c.id,h.value.concat(g)):Promise.resolve(h).then(function(l){return b(c.id,l.concat(g))}):b(c.id,g)}var b=a._callServer;L(d,c,a._encodeFormAction);return d}function Y(a,c){a=X(a,c);switch(a.status){case "resolved_model":O(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ma(a,c,d,b){if("$"===b[0]){if("$"===b)return A;switch(b[1]){case "$":return b.slice(1);case "L":return c=parseInt(b.slice(2),16),a=X(a,c),{$$typeof:B,_payload:a,_init:ia};case "@":if(2===b.length)return new Promise(function(){});c=parseInt(b.slice(2),16);return X(a,c);case "S":return Symbol.for(b.slice(2));case "F":return c=parseInt(b.slice(2),16),c=Y(a,c),la(a,c);case "Q":return c=parseInt(b.slice(2),16),a=Y(a,c),new Map(a);case "W":return c=parseInt(b.slice(2),16),a=Y(a,c),new Set(a);case "I":return Infinity;
case "-":return"$-0"===b?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(b.slice(2)));case "n":return BigInt(b.slice(2));default:b=parseInt(b.slice(1),16);a=X(a,b);switch(a.status){case "resolved_model":O(a);break;case "resolved_module":P(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":return b=U,a.then(ja(b,c,d,"cyclic"===a.status),ka(b)),null;default:throw a.reason;}}}return b}
function na(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function oa(a,c,d,b,g){var h=new Map;a={_bundlerConfig:a,_moduleLoading:c,_callServer:void 0!==d?d:na,_encodeFormAction:b,_nonce:g,_chunks:h,_stringDecoder:new p.TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=pa(a);return a}
function qa(a,c,d){var b=a._chunks,g=b.get(c);d=JSON.parse(d,a._fromJSON);var h=v(a._bundlerConfig,d);y(a._moduleLoading,d[1],a._nonce);if(d=x(h)){if(g){var l=g;l.status="blocked"}else l=new N("blocked",null,null,a),b.set(c,l);d.then(function(){return T(l,h)},function(k){return S(l,k)})}else g?T(g,h):b.set(c,new N("resolved_module",h,null,a))}
function pa(a){return function(c,d){return"string"===typeof d?ma(a,this,c,d):"object"===typeof d&&null!==d?(c=d[0]===A?{$$typeof:A,type:d[1],key:d[2],ref:null,props:d[3],_owner:null}:d,c):d}}function Z(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
exports.createFromNodeStream=function(a,c,d){var b=oa(c.moduleMap,c.moduleLoading,Z,d?d.encodeFormAction:void 0,d&&"string"===typeof d.nonce?d.nonce:void 0);a.on("data",function(g){for(var h=0,l=b._rowState,k=b._rowID,e=b._rowTag,f=b._rowLength,u=b._buffer,D=g.length;h<D;){var m=-1;switch(l){case 0:m=g[h++];58===m?l=1:k=k<<4|(96<m?m-87:m-48);continue;case 1:l=g[h];84===l?(e=l,l=2,h++):64<l&&91>l?(e=l,l=3,h++):(e=0,l=3);continue;case 2:m=g[h++];44===m?l=4:f=f<<4|(96<m?m-87:m-48);continue;case 3:m=
g.indexOf(10,h);break;case 4:m=h+f,m>g.length&&(m=-1)}var n=g.byteOffset+h;if(-1<m){f=new Uint8Array(g.buffer,n,m-h);h=e;n=b._stringDecoder;e="";for(var q=0;q<u.length;q++)e+=n.decode(u[q],t);e+=n.decode(f);switch(h){case 73:qa(b,k,e);break;case 72:k=e[0];e=e.slice(1);e=JSON.parse(e,b._fromJSON);if(f=z.current)switch(k){case "D":f.prefetchDNS(e);break;case "C":"string"===typeof e?f.preconnect(e):f.preconnect(e[0],e[1]);break;case "L":k=e[0];h=e[1];3===e.length?f.preload(k,h,e[2]):f.preload(k,h);break;
case "m":"string"===typeof e?f.preloadModule(e):f.preloadModule(e[0],e[1]);break;case "S":"string"===typeof e?f.preinitStyle(e):f.preinitStyle(e[0],0===e[1]?void 0:e[1],3===e.length?e[2]:void 0);break;case "X":"string"===typeof e?f.preinitScript(e):f.preinitScript(e[0],e[1]);break;case "M":"string"===typeof e?f.preinitModuleScript(e):f.preinitModuleScript(e[0],e[1])}break;case 69:e=JSON.parse(e);f=e.digest;e=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
e.stack="Error: "+e.message;e.digest=f;f=b._chunks;(h=f.get(k))?S(h,e):f.set(k,new N("rejected",null,e,b));break;case 84:b._chunks.set(k,new N("fulfilled",e,null,b));break;case 68:case 87:throw Error("Failed to read a RSC payload created by a development version of React on the server while using a production version on the client. Always use matching versions on the server and the client.");default:f=b._chunks,(h=f.get(k))?(k=h,"pending"===k.status&&(f=k.value,h=k.reason,k.status="resolved_model",
k.value=e,null!==f&&(O(k),R(k,f,h)))):f.set(k,new N("resolved_model",e,null,b))}h=m;3===l&&h++;f=k=e=l=0;u.length=0}else{g=new Uint8Array(g.buffer,n,g.byteLength-h);u.push(g);f-=g.byteLength;break}}b._rowState=l;b._rowID=k;b._rowTag=e;b._rowLength=f});a.on("error",function(g){W(b,g)});a.on("end",function(){W(b,Error("Connection closed."))});return X(b,0)};exports.createServerReference=function(a){return ha(a,Z)};

//# sourceMappingURL=react-server-dom-webpack-client.node.unbundled.production.min.js.map
