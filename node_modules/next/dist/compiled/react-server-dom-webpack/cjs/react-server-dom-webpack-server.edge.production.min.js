/*
 React
 react-server-dom-webpack-server.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("react"),ba=require("react-dom"),m=null,n=0;function p(a,b){if(0!==b.byteLength)if(2048<b.byteLength)0<n&&(a.enqueue(new Uint8Array(m.buffer,0,n)),m=new Uint8Array(2048),n=0),a.enqueue(b);else{var c=m.length-n;c<b.byteLength&&(0===c?a.enqueue(m):(m.set(b.subarray(0,c),n),a.enqueue(m),b=b.subarray(c)),m=new Uint8Array(2048),n=0);m.set(b,n);n+=b.byteLength}return!0}var q=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),t=Symbol.for("react.server.reference");function v(a,b,c){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:c}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===t){var b=ea.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:fa}})}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ja(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "__esModule":var c=a.$$id;a.default=v(function(){throw Error("Attempted to call the default export of "+c+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var d=v({},a.$$id,!0),e=new Proxy(d,ka);a.status="fulfilled";a.value=e;return a.then=v(function(g){return Promise.resolve(g(e))},a.$$id+"#then",!1)}if("symbol"===typeof b)throw Error("Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.");d=a[b];d||(d=v(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(d,"name",{value:b}),d=a[b]=new Proxy(d,ia));return d}
var ka={get:function(a,b){return ja(a,b)},getOwnPropertyDescriptor:function(a,b){var c=Object.getOwnPropertyDescriptor(a,b);c||(c={value:ja(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,c));return c},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");}},sa={prefetchDNS:la,preconnect:ma,preload:na,preloadModule:oa,preinitStyle:pa,preinitScript:qa,preinitModuleScript:ra};
function la(a){if("string"===typeof a&&a){var b=w();if(b){var c=b.hints,d="D|"+a;c.has(d)||(c.add(d),x(b,"D",a))}}}function ma(a,b){if("string"===typeof a){var c=w();if(c){var d=c.hints,e="C|"+(null==b?"null":b)+"|"+a;d.has(e)||(d.add(e),"string"===typeof b?x(c,"C",[a,b]):x(c,"C",a))}}}
function na(a,b,c){if("string"===typeof a){var d=w();if(d){var e=d.hints,g="L";if("image"===b&&c){var f=c.imageSrcSet,h=c.imageSizes,k="";"string"===typeof f&&""!==f?(k+="["+f+"]","string"===typeof h&&(k+="["+h+"]")):k+="[][]"+a;g+="[image]"+k}else g+="["+b+"]"+a;e.has(g)||(e.add(g),(c=y(c))?x(d,"L",[a,b,c]):x(d,"L",[a,b]))}}}function oa(a,b){if("string"===typeof a){var c=w();if(c){var d=c.hints,e="m|"+a;if(!d.has(e))return d.add(e),(b=y(b))?x(c,"m",[a,b]):x(c,"m",a)}}}
function pa(a,b,c){if("string"===typeof a){var d=w();if(d){var e=d.hints,g="S|"+a;if(!e.has(g))return e.add(g),(c=y(c))?x(d,"S",[a,"string"===typeof b?b:0,c]):"string"===typeof b?x(d,"S",[a,b]):x(d,"S",a)}}}function qa(a,b){if("string"===typeof a){var c=w();if(c){var d=c.hints,e="X|"+a;if(!d.has(e))return d.add(e),(b=y(b))?x(c,"X",[a,b]):x(c,"X",a)}}}function ra(a,b){if("string"===typeof a){var c=w();if(c){var d=c.hints,e="M|"+a;if(!d.has(e))return d.add(e),(b=y(b))?x(c,"M",[a,b]):x(c,"M",a)}}}
function y(a){if(null==a)return null;var b=!1,c={},d;for(d in a)null!=a[d]&&(b=!0,c[d]=a[d]);return b?c:null}var ta=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,A="function"===typeof AsyncLocalStorage,ua=A?new AsyncLocalStorage:null;"object"===typeof async_hooks?async_hooks.createHook:function(){return{enable:function(){},disable:function(){}}};"object"===typeof async_hooks?async_hooks.executionAsyncId:null;
var B=Symbol.for("react.element"),va=Symbol.for("react.fragment"),wa=Symbol.for("react.context"),xa=Symbol.for("react.forward_ref"),ya=Symbol.for("react.suspense"),za=Symbol.for("react.suspense_list"),Aa=Symbol.for("react.memo"),D=Symbol.for("react.lazy"),Ba=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.postpone");var Ca=Symbol.iterator,Da=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Ea(){}function Fa(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Ea,Ea),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}E=b;throw Da;}}var E=null;
function Ga(){if(null===E)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=E;E=null;return a}var F=null,Ha=0,G=null;function Ia(){var a=G||[];G=null;return a}
var Na={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:H,useTransition:H,readContext:Ja,useContext:Ja,useReducer:H,useRef:H,useState:H,useInsertionEffect:H,useLayoutEffect:H,useImperativeHandle:H,useEffect:H,useId:Ka,useSyncExternalStore:H,useCacheRefresh:function(){return La},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=Ba;return b},use:Ma};
function H(){throw Error("This Hook is not supported in Server Components.");}function La(){throw Error("Refreshing the cache is not supported in Server Components.");}function Ja(){throw Error("Cannot read a Client Context from a Server Component.");}function Ka(){if(null===F)throw Error("useId can only be used while React is rendering");var a=F.identifierCount++;return":"+F.identifierPrefix+"S"+a.toString(32)+":"}
function Ma(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Ha;Ha+=1;null===G&&(G=[]);return Fa(G,a,b)}a.$$typeof===wa&&Ja()}if(a.$$typeof===r){if(null!=a.value&&a.value.$$typeof===wa)throw Error("Cannot read a Client Context from a Server Component.");throw Error("Cannot use() an already resolved Client Reference.");}throw Error("An unsupported type was passed to use(): "+String(a));}function Oa(){return(new AbortController).signal}
function Pa(){var a=w();return a?a.cache:new Map}var Qa={getCacheSignal:function(){var a=Pa(),b=a.get(Oa);void 0===b&&(b=Oa(),a.set(Oa,b));return b},getCacheForType:function(a){var b=Pa(),c=b.get(a);void 0===c&&(c=a(),b.set(a,c));return c}},Ra=Array.isArray,Sa=Object.getPrototypeOf;function Ta(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,c){return c})}
function Ua(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Ra(a))return"[...]";if(null!==a&&a.$$typeof===Va)return"client";a=Ta(a);return"Object"===a?"{...}":a;case "function":return a.$$typeof===Va?"client":(a=a.displayName||a.name)?"function "+a:"function";default:return String(a)}}
function I(a){if("string"===typeof a)return a;switch(a){case ya:return"Suspense";case za:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case xa:return I(a.render);case Aa:return I(a.type);case D:var b=a._payload;a=a._init;try{return I(a(b))}catch(c){}}return""}var Va=Symbol.for("react.client.reference");
function J(a,b){var c=Ta(a);if("Object"!==c&&"Array"!==c)return c;c=-1;var d=0;if(Ra(a)){var e="[";for(var g=0;g<a.length;g++){0<g&&(e+=", ");var f=a[g];f="object"===typeof f&&null!==f?J(f):Ua(f);""+g===b?(c=e.length,d=f.length,e+=f):e=10>f.length&&40>e.length+f.length?e+f:e+"..."}e+="]"}else if(a.$$typeof===B)e="<"+I(a.type)+"/>";else{if(a.$$typeof===Va)return"client";e="{";g=Object.keys(a);for(f=0;f<g.length;f++){0<f&&(e+=", ");var h=g[f],k=JSON.stringify(h);e+=('"'+h+'"'===k?h:k)+": ";k=a[h];k=
"object"===typeof k&&null!==k?J(k):Ua(k);h===b?(c=e.length,d=k.length,e+=k):e=10>k.length&&40>e.length+k.length?e+k:e+"..."}e+="}"}return void 0===b?e:-1<c&&0<d?(a=" ".repeat(c)+"^".repeat(d),"\n  "+e+"\n  "+a):"\n  "+e}var Wa=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Xa=aa.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!Xa)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var Ya=Object.prototype,K=JSON.stringify,Za=Xa.ReactCurrentCache,$a=Wa.ReactCurrentDispatcher;function ab(a){console.error(a)}function bb(){}
function cb(a,b,c,d,e){if(null!==Za.current&&Za.current!==Qa)throw Error("Currently React only supports one RSC renderer at a time.");ta.current=sa;Za.current=Qa;var g=new Set,f=[],h=new Set;b={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:h,abortableTasks:g,pingedTasks:f,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenObjects:new WeakMap,identifierPrefix:d||"",identifierCount:1,taintCleanupQueue:[],onError:void 0===c?ab:c,onPostpone:void 0===e?bb:e};a=L(b,a,null,!1,g);f.push(a);return b}var M=null;function w(){if(M)return M;if(A){var a=ua.getStore();if(a)return a}return null}
function db(a,b,c){var d=L(a,null,b.keyPath,b.implicitSlot,a.abortableTasks);switch(c.status){case "fulfilled":return d.model=c.value,eb(a,d),d.id;case "rejected":return b=N(a,c.reason),O(a,d.id,b),d.id;default:"string"!==typeof c.status&&(c.status="pending",c.then(function(e){"pending"===c.status&&(c.status="fulfilled",c.value=e)},function(e){"pending"===c.status&&(c.status="rejected",c.reason=e)}))}c.then(function(e){d.model=e;eb(a,d)},function(e){d.status=4;e=N(a,e);O(a,d.id,e);a.abortableTasks.delete(d);
null!==a.destination&&P(a,a.destination)});return d.id}function x(a,b,c){c=K(c);var d=a.nextChunkId++;b="H"+b;b=d.toString(16)+":"+b;c=q.encode(b+c+"\n");a.completedHintChunks.push(c);fb(a)}function gb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function hb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:D,_payload:a,_init:gb}}
function ib(a,b,c,d,e){var g=b.thenableState;b.thenableState=null;Ha=0;G=g;d=d(e,void 0);if("object"===typeof d&&null!==d&&"function"===typeof d.then){e=d;if("fulfilled"===e.status)return e.value;d=hb(d)}e=b.keyPath;g=b.implicitSlot;null!==c?b.keyPath=null===e?c:e+","+c:null===e&&(b.implicitSlot=!0);a=Q(a,b,R,"",d);b.keyPath=e;b.implicitSlot=g;return a}
function jb(a,b,c,d,e,g){if(null!==e&&void 0!==e)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof c)return c.$$typeof===r?[B,c,d,g]:ib(a,b,d,c,g);if("string"===typeof c)return[B,c,d,g];if("symbol"===typeof c)return c===va&&null===d?(d=b.implicitSlot,null===b.keyPath&&(b.implicitSlot=!0),a=Q(a,b,R,"",g.children),b.implicitSlot=d,a):[B,c,d,g];if(null!=c&&"object"===typeof c){if(c.$$typeof===r)return[B,c,d,g];switch(c.$$typeof){case D:var f=
c._init;c=f(c._payload);return jb(a,b,c,d,e,g);case xa:return ib(a,b,d,c.render,g);case Aa:return jb(a,b,c.type,d,e,g)}}throw Error("Unsupported Server Component type: "+Ua(c));}function eb(a,b){var c=a.pingedTasks;c.push(b);1===c.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return kb(a)},0))}
function L(a,b,c,d,e){a.pendingChunks++;var g=a.nextChunkId++;"object"===typeof b&&null!==b&&a.writtenObjects.set(b,g);var f={id:g,status:0,model:b,keyPath:c,implicitSlot:d,ping:function(){return eb(a,f)},toJSON:function(h,k){var l=f.keyPath,z=f.implicitSlot;try{var u=Q(a,f,this,h,k)}catch(Z){if(h=Z===Da?Ga():Z,k=f.model,k="object"===typeof k&&null!==k&&(k.$$typeof===B||k.$$typeof===D),"object"===typeof h&&null!==h&&"function"===typeof h.then){u=L(a,f.model,f.keyPath,f.implicitSlot,a.abortableTasks);
var C=u.ping;h.then(C,C);u.thenableState=Ia();f.keyPath=l;f.implicitSlot=z;u=k?"$L"+u.id.toString(16):S(u.id)}else if(f.keyPath=l,f.implicitSlot=z,k)a.pendingChunks++,l=a.nextChunkId++,z=N(a,h),O(a,l,z),u="$L"+l.toString(16);else throw h;}return u},thenableState:null};e.add(f);return f}function S(a){return"$"+a.toString(16)}function lb(a,b,c){a=K(c);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function mb(a,b,c,d){var e=d.$$async?d.$$id+"#async":d.$$id,g=a.writtenClientReferences,f=g.get(e);if(void 0!==f)return b[0]===B&&"1"===c?"$L"+f.toString(16):S(f);try{var h=a.bundlerConfig,k=d.$$id;f="";var l=h[k];if(l)f=l.name;else{var z=k.lastIndexOf("#");-1!==z&&(f=k.slice(z+1),l=h[k.slice(0,z)]);if(!l)throw Error('Could not find the module "'+k+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var u=!0===d.$$async?[l.id,l.chunks,f,1]:[l.id,l.chunks,
f];a.pendingChunks++;var C=a.nextChunkId++,Z=K(u),Fb=C.toString(16)+":I"+Z+"\n",Gb=q.encode(Fb);a.completedImportChunks.push(Gb);g.set(e,C);return b[0]===B&&"1"===c?"$L"+C.toString(16):S(C)}catch(Hb){return a.pendingChunks++,b=a.nextChunkId++,c=N(a,Hb),O(a,b,c),S(b)}}function T(a,b){b=L(a,b,null,!1,a.abortableTasks);nb(a,b);return b.id}var U=!1;
function Q(a,b,c,d,e){b.model=e;if(e===B)return"$";if(null===e)return null;if("object"===typeof e){switch(e.$$typeof){case B:c=a.writtenObjects;d=c.get(e);if(void 0!==d)if(U===e)U=null;else return-1===d?(a=T(a,e),S(a)):S(d);else c.set(e,-1);return jb(a,b,e.type,e.key,e.ref,e.props);case D:return b.thenableState=null,c=e._init,e=c(e._payload),Q(a,b,R,"",e)}if(e.$$typeof===r)return mb(a,c,d,e);c=a.writtenObjects;d=c.get(e);if("function"===typeof e.then){if(void 0!==d)if(U===e)U=null;else return"$@"+
d.toString(16);a=db(a,b,e);c.set(e,a);return"$@"+a.toString(16)}if(void 0!==d)if(U===e)U=null;else return-1===d?(a=T(a,e),S(a)):S(d);else c.set(e,-1);if(Ra(e))return e;if(e instanceof Map){e=Array.from(e);for(b=0;b<e.length;b++)c=e[b][0],"object"===typeof c&&null!==c&&(d=a.writtenObjects,void 0===d.get(c)&&d.set(c,-1));return"$Q"+T(a,e).toString(16)}if(e instanceof Set){e=Array.from(e);for(b=0;b<e.length;b++)c=e[b],"object"===typeof c&&null!==c&&(d=a.writtenObjects,void 0===d.get(c)&&d.set(c,-1));
return"$W"+T(a,e).toString(16)}null===e||"object"!==typeof e?a=null:(a=Ca&&e[Ca]||e["@@iterator"],a="function"===typeof a?a:null);if(a)return a=Array.from(e),a;a=Sa(e);if(a!==Ya&&(null===a||null!==Sa(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return e}if("string"===typeof e){if("Z"===e[e.length-1]&&c[d]instanceof Date)return"$D"+e;if(1024<=e.length)return a.pendingChunks+=2,b=
a.nextChunkId++,e=q.encode(e),c=e.byteLength,c=b.toString(16)+":T"+c.toString(16)+",",c=q.encode(c),a.completedRegularChunks.push(c,e),S(b);a="$"===e[0]?"$"+e:e;return a}if("boolean"===typeof e)return e;if("number"===typeof e)return Number.isFinite(e)?0===e&&-Infinity===1/e?"$-0":e:Infinity===e?"$Infinity":-Infinity===e?"$-Infinity":"$NaN";if("undefined"===typeof e)return"$undefined";if("function"===typeof e){if(e.$$typeof===r)return mb(a,c,d,e);if(e.$$typeof===t)return b=a.writtenServerReferences,
c=b.get(e),void 0!==c?a="$F"+c.toString(16):(c=e.$$bound,c={id:e.$$id,bound:c?Promise.resolve(c):null},a=T(a,c),b.set(e,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+J(c,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server". Or maybe you meant to call this function rather than return it.'+
J(c,d));}if("symbol"===typeof e){b=a.writtenSymbols;var g=b.get(e);if(void 0!==g)return S(g);g=e.description;if(Symbol.for(g)!==e)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(e.description+") cannot be found among global symbols.")+J(c,d));a.pendingChunks++;c=a.nextChunkId++;d=lb(a,c,"$S"+g);a.completedImportChunks.push(d);b.set(e,c);return S(c)}if("bigint"===typeof e)return"$n"+e.toString(10);throw Error("Type "+typeof e+
" is not supported in Client Component props."+J(c,d));}function N(a,b){var c=M;M=null;try{var d=a.onError;var e=A?ua.run(void 0,d,b):d(b)}finally{M=c}if(null!=e&&"string"!==typeof e)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof e+'" instead');return e||""}
function ob(a,b){null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}function O(a,b,c){c={digest:c};b=b.toString(16)+":E"+K(c)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}var R={};
function nb(a,b){if(0===b.status)try{U=b.model;var c=Q(a,b,R,"",b.model);U=c;b.keyPath=null;b.implicitSlot=!1;var d="object"===typeof c&&null!==c?K(c,b.toJSON):K(c),e=b.id.toString(16)+":"+d+"\n",g=q.encode(e);a.completedRegularChunks.push(g);a.abortableTasks.delete(b);b.status=1}catch(l){var f=l===Da?Ga():l;if("object"===typeof f&&null!==f&&"function"===typeof f.then){var h=b.ping;f.then(h,h);b.thenableState=Ia()}else{a.abortableTasks.delete(b);b.status=4;var k=N(a,f);O(a,b.id,k)}}finally{}}
function kb(a){var b=$a.current;$a.current=Na;var c=M;F=M=a;try{var d=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<d.length;e++)nb(a,d[e]);null!==a.destination&&P(a,a.destination)}catch(g){N(a,g),ob(a,g)}finally{$a.current=b,F=null,M=c}}
function P(a,b){m=new Uint8Array(2048);n=0;try{for(var c=a.completedImportChunks,d=0;d<c.length;d++)a.pendingChunks--,p(b,c[d]);c.splice(0,d);var e=a.completedHintChunks;for(d=0;d<e.length;d++)p(b,e[d]);e.splice(0,d);var g=a.completedRegularChunks;for(d=0;d<g.length;d++)a.pendingChunks--,p(b,g[d]);g.splice(0,d);var f=a.completedErrorChunks;for(d=0;d<f.length;d++)a.pendingChunks--,p(b,f[d]);f.splice(0,d)}finally{a.flushScheduled=!1,m&&0<n&&(b.enqueue(new Uint8Array(m.buffer,0,n)),m=null,n=0)}0===a.pendingChunks&&
b.close()}function pb(a){a.flushScheduled=null!==a.destination;A?setTimeout(function(){return ua.run(a,kb,a)},0):setTimeout(function(){return kb(a)},0)}function fb(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setTimeout(function(){return P(a,b)},0)}}
function qb(a,b){try{var c=a.abortableTasks;if(0<c.size){a.pendingChunks++;var d=a.nextChunkId++,e=void 0===b?Error("The render was aborted by the server without a reason."):b,g=N(a,e);O(a,d,g,e);c.forEach(function(f){f.status=3;var h=S(d);f=lb(a,f.id,h);a.completedErrorChunks.push(f)});c.clear()}null!==a.destination&&P(a,a.destination)}catch(f){N(a,f),ob(a,f)}}
function rb(a,b){var c="",d=a[b];if(d)c=d.name;else{var e=b.lastIndexOf("#");-1!==e&&(c=b.slice(e+1),d=a[b.slice(0,e)]);if(!d)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[d.id,d.chunks,c]}var sb=new Map;
function tb(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function ub(){}
function vb(a){for(var b=a[1],c=[],d=0;d<b.length;){var e=b[d++];b[d++];var g=sb.get(e);if(void 0===g){g=__webpack_chunk_load__(e);c.push(g);var f=sb.set.bind(sb,e,null);g.then(f,ub);sb.set(e,g)}else null!==g&&c.push(g)}return 4===a.length?0===c.length?tb(a[0]):Promise.all(c).then(function(){return tb(a[0])}):0<c.length?Promise.all(c):null}
function V(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function wb(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}wb.prototype=Object.create(Promise.prototype);
wb.prototype.then=function(a,b){switch(this.status){case "resolved_model":xb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function yb(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}
function zb(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&yb(c,b)}}function Ab(a,b,c,d,e,g){var f=rb(a._bundlerConfig,b);a=vb(f);if(c)c=Promise.all([c,a]).then(function(h){h=h[0];var k=V(f);return k.bind.apply(k,[null].concat(h))});else if(a)c=Promise.resolve(a).then(function(){return V(f)});else return V(f);c.then(Bb(d,e,g),Cb(d));return null}var W=null,X=null;
function xb(a){var b=W,c=X;W=a;X=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==X&&0<X.deps?(X.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(e){a.status="rejected",a.reason=e}finally{W=b,X=c}}function Db(a,b){a._chunks.forEach(function(c){"pending"===c.status&&zb(c,b)})}
function Y(a,b){var c=a._chunks,d=c.get(b);d||(d=a._formData.get(a._prefix+b),d=null!=d?new wb("resolved_model",d,null,a):new wb("pending",null,null,a),c.set(b,d));return d}function Bb(a,b,c){if(X){var d=X;d.deps++}else d=X={deps:1,value:null};return function(e){b[c]=e;d.deps--;0===d.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=d.value,null!==e&&yb(e,d.value))}}function Cb(a){return function(b){return zb(a,b)}}
function Eb(a,b){a=Y(a,b);"resolved_model"===a.status&&xb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Ib(a,b,c,d){if("$"===d[0])switch(d[1]){case "$":return d.slice(1);case "@":return b=parseInt(d.slice(2),16),Y(a,b);case "S":return Symbol.for(d.slice(2));case "F":return d=parseInt(d.slice(2),16),d=Eb(a,d),Ab(a,d.id,d.bound,W,b,c);case "Q":return b=parseInt(d.slice(2),16),a=Eb(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=Eb(a,b),new Set(a);case "K":b=d.slice(2);var e=a._prefix+b+"_",g=new FormData;a._formData.forEach(function(f,h){h.startsWith(e)&&g.append(h.slice(e.length),
f)});return g;case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=Y(a,d);switch(a.status){case "resolved_model":xb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return d=W,a.then(Bb(d,b,c),Cb(d)),null;default:throw a.reason;}}return d}
function Jb(a,b){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,d=new Map,e={_bundlerConfig:a,_prefix:b,_formData:c,_chunks:d,_fromJSON:function(g,f){return"string"===typeof f?Ib(e,this,g,f):f}};return e}function Kb(a){Db(a,Error("Connection closed."))}function Lb(a,b,c){var d=rb(a,b);a=vb(d);return c?Promise.all([c,a]).then(function(e){e=e[0];var g=V(d);return g.bind.apply(g,[null].concat(e))}):a?Promise.resolve(a).then(function(){return V(d)}):Promise.resolve(V(d))}
function Mb(a,b,c){a=Jb(b,c,a);Kb(a);a=Y(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=v({},a,!1);return new Proxy(a,ka)};
exports.decodeAction=function(a,b){var c=new FormData,d=null;a.forEach(function(e,g){g.startsWith("$ACTION_")?g.startsWith("$ACTION_REF_")?(e="$ACTION_"+g.slice(12)+":",e=Mb(a,b,e),d=Lb(b,e.id,e.bound)):g.startsWith("$ACTION_ID_")&&(e=g.slice(11),d=Lb(b,e,null)):c.append(g,e)});return null===d?null:d.then(function(e){return e.bind(null,c)})};
exports.decodeFormState=function(a,b,c){var d=b.get("$ACTION_KEY");if("string"!==typeof d)return Promise.resolve(null);var e=null;b.forEach(function(f,h){h.startsWith("$ACTION_REF_")&&(f="$ACTION_"+h.slice(12)+":",e=Mb(b,c,f))});if(null===e)return Promise.resolve(null);var g=e.id;return Promise.resolve(e.bound).then(function(f){return null===f?null:[a,d,g,f.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var c=new FormData;c.append("0",a);a=c}a=Jb(b,"",a);b=Y(a,0);Kb(a);return b};
exports.registerClientReference=function(a,b,c){return v(a,b+"#"+c,!1)};exports.registerServerReference=function(a,b,c){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:null===c?b:b+"#"+c,configurable:!0},$$bound:{value:null,configurable:!0},bind:{value:fa,configurable:!0}})};
exports.renderToReadableStream=function(a,b,c){var d=cb(a,b,c?c.onError:void 0,c?c.identifierPrefix:void 0,c?c.onPostpone:void 0);if(c&&c.signal){var e=c.signal;if(e.aborted)qb(d,e.reason);else{var g=function(){qb(d,e.reason);e.removeEventListener("abort",g)};e.addEventListener("abort",g)}}return new ReadableStream({type:"bytes",start:function(){pb(d)},pull:function(f){if(1===d.status)d.status=2,ca(f,d.fatalError);else if(2!==d.status&&null===d.destination){d.destination=f;try{P(d,f)}catch(h){N(d,
h),ob(d,h)}}},cancel:function(f){d.destination=null;qb(d,f)}},{highWaterMark:0})};

//# sourceMappingURL=react-server-dom-webpack-server.edge.production.min.js.map
