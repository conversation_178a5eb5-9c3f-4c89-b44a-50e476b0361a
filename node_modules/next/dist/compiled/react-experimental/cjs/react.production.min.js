/*
 React
 react.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var k=Symbol.for("react.element"),m=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),p=Symbol.for("react.strict_mode"),q=Symbol.for("react.profiler"),r=Symbol.for("react.provider"),t=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),w=Symbol.for("react.suspense_list"),x=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),z=Symbol.for("react.debug_trace_mode"),aa=Symbol.for("react.offscreen"),ba=Symbol.for("react.cache"),ca=Symbol.for("react.postpone"),
A=Symbol.iterator;function da(a){if(null===a||"object"!==typeof a)return null;a=A&&a[A]||a["@@iterator"];return"function"===typeof a?a:null}var B={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,D={};function E(a,b,c){this.props=a;this.context=b;this.refs=D;this.updater=c||B}E.prototype.isReactComponent={};
E.prototype.setState=function(a,b){if("object"!==typeof a&&"function"!==typeof a&&null!=a)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,a,b,"setState")};E.prototype.forceUpdate=function(a){this.updater.enqueueForceUpdate(this,a,"forceUpdate")};function F(){}F.prototype=E.prototype;function G(a,b,c){this.props=a;this.context=b;this.refs=D;this.updater=c||B}var H=G.prototype=new F;
H.constructor=G;C(H,E.prototype);H.isPureReactComponent=!0;var I=Array.isArray,J={current:null},K={current:null},L={transition:null},M={ReactCurrentDispatcher:J,ReactCurrentCache:K,ReactCurrentBatchConfig:L,ReactCurrentOwner:{current:null}},N=Object.prototype.hasOwnProperty,O=M.ReactCurrentOwner;function P(a,b,c,e,d,g,f){c=f.ref;return{$$typeof:k,type:a,key:b,ref:void 0!==c?c:null,props:f,_owner:g}}
function Q(a,b,c){var e,d={},g=null;if(null!=b)for(e in void 0!==b.key&&(g=""+b.key),b)N.call(b,e)&&"key"!==e&&"__self"!==e&&"__source"!==e&&(d[e]=b[e]);var f=arguments.length-2;if(1===f)d.children=c;else if(1<f){for(var h=Array(f),l=0;l<f;l++)h[l]=arguments[l+2];d.children=h}if(a&&a.defaultProps)for(e in f=a.defaultProps,f)void 0===d[e]&&(d[e]=f[e]);return P(a,g,null,void 0,void 0,O.current,d)}function ea(a,b){return P(a.type,b,null,void 0,void 0,a._owner,a.props)}
function R(a){return"object"===typeof a&&null!==a&&a.$$typeof===k}function escape(a){var b={"=":"=0",":":"=2"};return"$"+a.replace(/[=:]/g,function(c){return b[c]})}var S=/\/+/g;function T(a,b){return"object"===typeof a&&null!==a&&null!=a.key?escape(""+a.key):b.toString(36)}function U(){}
function fa(a){switch(a.status){case "fulfilled":return a.value;case "rejected":throw a.reason;default:switch("string"===typeof a.status?a.then(U,U):(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)})),a.status){case "fulfilled":return a.value;case "rejected":throw a.reason;}}throw a;}
function V(a,b,c,e,d){var g=typeof a;if("undefined"===g||"boolean"===g)a=null;var f=!1;if(null===a)f=!0;else switch(g){case "string":case "number":f=!0;break;case "object":switch(a.$$typeof){case k:case m:f=!0;break;case y:return f=a._init,V(f(a._payload),b,c,e,d)}}if(f)return d=d(a),f=""===e?"."+T(a,0):e,I(d)?(c="",null!=f&&(c=f.replace(S,"$&/")+"/"),V(d,b,c,"",function(ha){return ha})):null!=d&&(R(d)&&(d=ea(d,c+(!d.key||a&&a.key===d.key?"":(""+d.key).replace(S,"$&/")+"/")+f)),b.push(d)),1;f=0;var h=
""===e?".":e+":";if(I(a))for(var l=0;l<a.length;l++)e=a[l],g=h+T(e,l),f+=V(e,b,c,g,d);else if(l=da(a),"function"===typeof l)for(a=l.call(a),l=0;!(e=a.next()).done;)e=e.value,g=h+T(e,l++),f+=V(e,b,c,g,d);else if("object"===g){if("function"===typeof a.then)return V(fa(a),b,c,e,d);b=String(a);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===b?"object with keys {"+Object.keys(a).join(", ")+"}":b)+"). If you meant to render a collection of children, use an array instead.");
}return f}function W(a,b,c){if(null==a)return a;var e=[],d=0;V(a,e,"","",function(g){return b.call(c,g,d++)});return e}function ia(a){if(-1===a._status){var b=a._result;b=b();b.then(function(c){if(0===a._status||-1===a._status)a._status=1,a._result=c},function(c){if(0===a._status||-1===a._status)a._status=2,a._result=c});-1===a._status&&(a._status=0,a._result=b)}if(1===a._status)return a._result.default;throw a._result;}function ja(){return new WeakMap}
function X(){return{s:0,v:void 0,o:null,p:null}}function Y(a,b){return J.current.useOptimistic(a,b)}function ka(){}var Z="function"===typeof reportError?reportError:function(a){console.error(a)};
exports.Children={map:W,forEach:function(a,b,c){W(a,function(){b.apply(this,arguments)},c)},count:function(a){var b=0;W(a,function(){b++});return b},toArray:function(a){return W(a,function(b){return b})||[]},only:function(a){if(!R(a))throw Error("React.Children.only expected to receive a single React element child.");return a}};exports.Component=E;exports.Fragment=n;exports.Profiler=q;exports.PureComponent=G;exports.StrictMode=p;exports.Suspense=v;
exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M;exports.act=function(){throw Error("act(...) is not supported in production builds of React.");};
exports.cache=function(a){return function(){var b=K.current;if(!b)return a.apply(null,arguments);var c=b.getCacheForType(ja);b=c.get(a);void 0===b&&(b=X(),c.set(a,b));c=0;for(var e=arguments.length;c<e;c++){var d=arguments[c];if("function"===typeof d||"object"===typeof d&&null!==d){var g=b.o;null===g&&(b.o=g=new WeakMap);b=g.get(d);void 0===b&&(b=X(),g.set(d,b))}else g=b.p,null===g&&(b.p=g=new Map),b=g.get(d),void 0===b&&(b=X(),g.set(d,b))}if(1===b.s)return b.v;if(2===b.s)throw b.v;try{var f=a.apply(null,
arguments);c=b;c.s=1;return c.v=f}catch(h){throw f=b,f.s=2,f.v=h,h;}}};
exports.cloneElement=function(a,b,c){if(null===a||void 0===a)throw Error("The argument must be a React element, but you passed "+a+".");var e=C({},a.props),d=a.key,g=a._owner;if(null!=b){void 0!==b.ref&&(g=O.current);void 0!==b.key&&(d=""+b.key);if(a.type&&a.type.defaultProps)var f=a.type.defaultProps;for(h in b)!N.call(b,h)||"key"===h||"__self"===h||"__source"===h||"ref"===h&&void 0===b.ref||(e[h]=void 0===b[h]&&void 0!==f?f[h]:b[h])}var h=arguments.length-2;if(1===h)e.children=c;else if(1<h){f=
Array(h);for(var l=0;l<h;l++)f[l]=arguments[l+2];e.children=f}return P(a.type,d,null,void 0,void 0,g,e)};exports.createContext=function(a){a={$$typeof:t,_currentValue:a,_currentValue2:a,_threadCount:0,Provider:null,Consumer:null};a.Provider={$$typeof:r,_context:a};return a.Consumer=a};exports.createElement=Q;exports.createFactory=function(a){var b=Q.bind(null,a);b.type=a;return b};exports.createRef=function(){return{current:null}};exports.experimental_useEffectEvent=function(a){return J.current.useEffectEvent(a)};
exports.experimental_useOptimistic=function(a,b){return Y(a,b)};exports.forwardRef=function(a){return{$$typeof:u,render:a}};exports.isValidElement=R;exports.lazy=function(a){return{$$typeof:y,_payload:{_status:-1,_result:a},_init:ia}};exports.memo=function(a,b){return{$$typeof:x,type:a,compare:void 0===b?null:b}};
exports.startTransition=function(a){var b=L.transition,c=new Set;L.transition={_callbacks:c};var e=L.transition;try{var d=a();"object"===typeof d&&null!==d&&"function"===typeof d.then&&(c.forEach(function(g){return g(e,d)}),d.then(ka,Z))}catch(g){Z(g)}finally{L.transition=b}};exports.unstable_Activity=aa;exports.unstable_Cache=ba;exports.unstable_DebugTracingMode=z;exports.unstable_SuspenseList=w;exports.unstable_getCacheForType=function(a){var b=K.current;return b?b.getCacheForType(a):a()};
exports.unstable_getCacheSignal=function(){var a=K.current;return a?a.getCacheSignal():(a=new AbortController,a.abort(Error("This CacheSignal was requested outside React which means that it is immediately aborted.")),a.signal)};exports.unstable_postpone=function(a){a=Error(a);a.$$typeof=ca;throw a;};exports.unstable_useCacheRefresh=function(){return J.current.useCacheRefresh()};exports.unstable_useMemoCache=function(a){return J.current.useMemoCache(a)};exports.use=function(a){return J.current.use(a)};
exports.useCallback=function(a,b){return J.current.useCallback(a,b)};exports.useContext=function(a){return J.current.useContext(a)};exports.useDebugValue=function(){};exports.useDeferredValue=function(a,b){return J.current.useDeferredValue(a,b)};exports.useEffect=function(a,b){return J.current.useEffect(a,b)};exports.useId=function(){return J.current.useId()};exports.useImperativeHandle=function(a,b,c){return J.current.useImperativeHandle(a,b,c)};
exports.useInsertionEffect=function(a,b){return J.current.useInsertionEffect(a,b)};exports.useLayoutEffect=function(a,b){return J.current.useLayoutEffect(a,b)};exports.useMemo=function(a,b){return J.current.useMemo(a,b)};exports.useOptimistic=Y;exports.useReducer=function(a,b,c){return J.current.useReducer(a,b,c)};exports.useRef=function(a){return J.current.useRef(a)};exports.useState=function(a){return J.current.useState(a)};
exports.useSyncExternalStore=function(a,b,c){return J.current.useSyncExternalStore(a,b,c)};exports.useTransition=function(){return J.current.useTransition()};exports.version="18.3.0-experimental-14898b6a9-20240318";

//# sourceMappingURL=react.production.min.js.map
