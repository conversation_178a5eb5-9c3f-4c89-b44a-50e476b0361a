import e from"./useConfig.js";function o(){for(var o=arguments.length,n=new Array(o),t=0;t<o;t++)n[t]=arguments[t];let[r]=n;null!=(null==r?void 0:r.updateInterval)&&console.error("`useNow` doesn't support the `updateInterval` option in Server Components, the value will be ignored. If you need the value to update, you can convert the component to a Client Component.");return e("useNow").now}export{o as default};
