import{extends as e}from"../_virtual/_rollupPluginBabelHelpers.js";import r from"react";import t from"../shared/NextIntlClientProvider.js";import o from"../server/react-server/getLocale.js";import l from"../server/react-server/getNow.js";import a from"../server/react-server/getTimeZone.js";async function i(i){let{locale:n,now:s,timeZone:m,...c}=i;return r.createElement(t,e({locale:null!=n?n:await o(),now:null!=s?s:await l(),timeZone:null!=m?m:await a()},c))}export{i as default};
