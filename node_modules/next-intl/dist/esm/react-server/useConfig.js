import{use as e}from"react";import n from"../server/react-server/getConfig.js";function r(r){return function(n,r){try{return e(r)}catch(e){throw e instanceof TypeError&&e.message.includes("Cannot read properties of null (reading 'use')")?new Error("`".concat(n,"` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components"),{cause:e}):e}}(r,n())}export{r as default};
