import{useTranslations as e,useFormatter as t}from"use-intl";export*from"use-intl";function n(e,t){return function(){try{return t(...arguments)}catch(t){throw new Error("Failed to call `".concat(e,"` because the context from `NextIntlClientProvider` was not found.\n\nThis can happen because:\n1) You intended to render this component as a Server Component, the render\n   failed, and therefore <PERSON>act attempted to render the component on the client\n   instead. If this is the case, check the console for server errors.\n2) You intended to render this component on the client side, but no context was found.\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context"))}}}const o=n("useTranslations",e),r=n("useFormatter",t);export{r as useFormatter,o as useTranslations};
