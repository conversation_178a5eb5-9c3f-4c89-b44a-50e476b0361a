import{useParams as e}from"next/navigation";import{useLocale as t}from"use-intl/_useLocale";import{LOCALE_SEGMENT_NAME as n}from"../shared/constants.js";let o=!1;function r(){const r=e();let a;try{a=t()}catch(e){if("string"!=typeof(null==r?void 0:r[n]))throw e;o||(console.warn("Deprecation warning: `useLocale` has returned a default from `useParams().locale` since no `NextIntlClientProvider` ancestor was found for the calling component. This behavior will be removed in the next major version. Please ensure all Client Components that use `next-intl` are wrapped in a `NextIntlClientProvider`."),o=!0),a=r[n]}return a}export{r as default};
