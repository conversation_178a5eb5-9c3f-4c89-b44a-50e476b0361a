import{useRouter as e,usePathname as t}from"next/navigation";import{useMemo as o}from"react";import n from"../../react-client/useLocale.js";import r from"../shared/createSharedNavigationFns.js";import a from"../shared/syncLocaleCookie.js";import{getRoute as s}from"../shared/utils.js";import c from"./useBasePathname.js";function i(i){function u(){return n()}const{Link:m,config:f,getPathname:h,...p}=r(u,i);return{...p,Link:m,usePathname:function(){const e=c(f),t=u();return o((()=>e&&f.pathnames?s(t,e,f.pathnames):e),[t,e])},useRouter:function(){const n=e(),r=u(),s=t();return o((()=>{function e(e){return function(t,o){const{locale:n,...c}=o||{},i=[h({href:t,locale:n||r,domain:window.location.host})];Object.keys(c).length>0&&i.push(c),e(...i),a(f.localeCookie,s,r,n)}}return{...n,push:e(n.push),replace:e(n.replace),prefetch:e(n.prefetch)}}),[r,s,n])},getPathname:h}}export{i as default};
