import{usePathname as e}from"next/navigation";import{useMemo as r}from"react";import o from"../../react-client/useLocale.js";import{hasPathnamePrefixed as t,unprefixPathname as i,getLocalePrefix as f,getLocaleAsPrefix as l}from"../../shared/utils.js";function a(a){const n=e(),c=o();return r((()=>{if(!n)return n;let e=n;const r=f(c,a.localePrefix);if(t(r,n))e=i(n,r);else if("as-needed"===a.localePrefix.mode&&a.localePrefix.prefixes){const r=l(c);t(r,n)&&(e=i(n,r))}return e}),[a.localePrefix,c,n])}export{a as default};
