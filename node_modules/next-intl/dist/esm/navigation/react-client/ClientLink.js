import{extends as e}from"../../_virtual/_rollupPluginBabelHelpers.js";import r,{forwardRef as l}from"react";import o from"../../react-client/useLocale.js";import{getLocalePrefix as t}from"../../shared/utils.js";import a from"../shared/LegacyBaseLink.js";function i(l,i){let{locale:s,localePrefix:c,...m}=l;const n=o(),f=s||n,p=t(f,c);return r.createElement(a,e({ref:i,locale:f,localePrefixMode:c.mode,prefix:p},m))}const s=l(i);s.displayName="ClientLink";export{s as default};
