import r from"../../react-client/useLocale.js";import{baseRedirect as e,basePermanentRedirect as n}from"../shared/redirects.js";function t(e){return function(n){let t;try{t=r()}catch(r){throw new Error("`redirect()` and `permanentRedirect()` can only be called during render. To redirect in an event handler or similar, you can use `useRouter()` instead.")}for(var o=arguments.length,a=new Array(o>1?o-1:0),c=1;c<o;c++)a[c-1]=arguments[c];return e({...n,locale:t},...a)}}const o=t(e),a=t(n);export{a as clientPermanentRedirect,o as clientRedirect};
