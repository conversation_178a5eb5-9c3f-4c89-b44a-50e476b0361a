import{extends as e}from"../../_virtual/_rollupPluginBabelHelpers.js";import r,{forwardRef as o,useMemo as t}from"react";import n from"../../react-client/useLocale.js";import{receiveRoutingConfig as a,receiveLocaleCookie as l}from"../../routing/config.js";import{getRoute as c,compileLocalizedPathname as i,normalizeNameOrNameWithParams as s}from"../shared/utils.js";import f from"./ClientLink.js";import{clientRedirect as u,clientPermanentRedirect as m}from"./redirects.js";import h from"./useBasePathname.js";import p from"./useBaseRouter.js";function d(d){const v=a(d),P=l(d.localeCookie);function g(){const e=n();if(!v.locales.includes(e))throw new Error('Unknown locale encountered: "'.concat(e,'". Make sure to validate the locale in `i18n.ts`.'));return e}function j(o,t){let{href:n,locale:a,...l}=o;const c=g(),s=a||c;return r.createElement(f,e({ref:t,href:i({locale:s,pathname:n,params:"object"==typeof n?n.params:void 0,pathnames:v.pathnames}),locale:a,localeCookie:P,localePrefix:v.localePrefix},l))}const x=o(j);function w(e){let{href:r,locale:o}=e;return i({...s(r),locale:o,pathnames:v.pathnames})}return x.displayName="Link",{Link:x,redirect:function(e){const r=w({href:e,locale:g()});for(var o=arguments.length,t=new Array(o>1?o-1:0),n=1;n<o;n++)t[n-1]=arguments[n];return u({pathname:r,localePrefix:v.localePrefix},...t)},permanentRedirect:function(e){const r=w({href:e,locale:g()});for(var o=arguments.length,t=new Array(o>1?o-1:0),n=1;n<o;n++)t[n-1]=arguments[n];return m({pathname:r,localePrefix:v.localePrefix},...t)},usePathname:function(){const e=h(v),r=g();return t((()=>e?c(r,e,v.pathnames):e),[r,e])},useRouter:function(){const e=p(v.localePrefix,P),r=g();return t((()=>({...e,push(o){for(var t,n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];const c=w({href:o,locale:(null===(t=a[0])||void 0===t?void 0:t.locale)||r});return e.push(c,...a)},replace(o){for(var t,n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];const c=w({href:o,locale:(null===(t=a[0])||void 0===t?void 0:t.locale)||r});return e.replace(c,...a)},prefetch(o){for(var t,n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];const c=w({href:o,locale:(null===(t=a[0])||void 0===t?void 0:t.locale)||r});return e.prefetch(c,...a)}})),[e,r])},getPathname:w}}export{d as default};
