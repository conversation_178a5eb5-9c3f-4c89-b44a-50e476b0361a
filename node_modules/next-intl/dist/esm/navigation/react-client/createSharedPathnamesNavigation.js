import{extends as e}from"../../_virtual/_rollupPluginBabelHelpers.js";import r,{forwardRef as o}from"react";import{receiveLocalePrefixConfig as t,receiveLocaleCookie as n}from"../../routing/config.js";import l from"./ClientLink.js";import{clientRedirect as a,clientPermanentRedirect as i}from"./redirects.js";import u from"./useBasePathname.js";import f from"./useBaseRouter.js";function c(c){const m=t(null==c?void 0:c.localePrefix),s=n(null==c?void 0:c.localeCookie);function p(o,t){return r.createElement(l,e({ref:t,localeCookie:s,localePrefix:m},o))}const d=o(p);return d.displayName="Link",{Link:d,redirect:function(e){for(var r=arguments.length,o=new Array(r>1?r-1:0),t=1;t<r;t++)o[t-1]=arguments[t];return a({pathname:e,localePrefix:m},...o)},permanentRedirect:function(e){for(var r=arguments.length,o=new Array(r>1?r-1:0),t=1;t<r;t++)o[t-1]=arguments[t];return i({pathname:e,localePrefix:m},...o)},usePathname:function(){return u({localePrefix:m,defaultLocale:null==c?void 0:c.defaultLocale})},useRouter:function(){return f(m,s)}}}export{c as default};
