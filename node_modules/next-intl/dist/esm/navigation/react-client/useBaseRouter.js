import{use<PERSON><PERSON>er as t,usePathname as e}from"next/navigation";import{useMemo as r}from"react";import o from"../../react-client/useLocale.js";import{getLocalePrefix as n,localizeHref as c}from"../../shared/utils.js";import s from"../shared/syncLocaleCookie.js";import{getBasePath as a}from"../shared/utils.js";function i(i,p){const u=t(),l=o(),f=e();return r((()=>{function t(t,e){let r=window.location.pathname;const o=a(f);o&&(r=r.replace(o,""));const s=e||l,p=n(s,i);return c(t,s,l,r,p)}function e(e){return function(r,o){const{locale:n,...c}=o||{};s(p,f,l,n);const a=[t(r,n)];return Object.keys(c).length>0&&a.push(c),e(...a)}}return{...u,push:e(u.push),replace:e(u.replace),prefetch:e(u.prefetch)}}),[l,p,i,f,u])}export{i as default};
