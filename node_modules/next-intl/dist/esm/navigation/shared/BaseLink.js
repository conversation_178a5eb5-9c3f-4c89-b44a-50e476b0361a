"use client";
import{extends as e}from"../../_virtual/_rollupPluginBabelHelpers.js";import o from"next/link";import{usePathname as t}from"next/navigation";import r,{forwardRef as n,useState as l,useEffect as i}from"react";import c from"../../react-client/useLocale.js";import a from"./syncLocaleCookie.js";function s(n,s){let{defaultLocale:p,href:f,locale:u,localeCookie:m,onClick:h,prefetch:d,unprefixed:k,...x}=n;const L=c(),g=null!=u&&u!==L,j=u||L,v=function(){const[e,o]=l();return i((()=>{o(window.location.host)}),[]),e}(),w=v&&k&&(k.domains[v]===j||!Object.keys(k.domains).includes(v)&&L===p&&!u)?k.pathname:f,C=t();return g&&(d&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),d=!1),r.createElement(o,e({ref:s,href:w,hrefLang:g?u:void 0,onClick:function(e){a(m,C,L,u),h&&h(e)},prefetch:d},x))}var p=n(s);export{p as default};
