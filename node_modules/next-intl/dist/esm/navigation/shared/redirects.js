import{redirect as e,permanentRedirect as n}from"next/navigation";import{getLocalePrefix as r,isLocalizableHref as t,prefixPathname as o}from"../../shared/utils.js";function a(e){return function(n){const a=r(n.locale,n.localePrefix),i="never"!==n.localePrefix.mode&&t(n.pathname)?o(a,n.pathname):n.pathname;for(var l=arguments.length,m=new Array(l>1?l-1:0),c=1;c<l;c++)m[c-1]=arguments[c];return e(i,...m)}}const i=a(e),l=a(n);export{l as basePermanentRedirect,i as baseRedirect};
