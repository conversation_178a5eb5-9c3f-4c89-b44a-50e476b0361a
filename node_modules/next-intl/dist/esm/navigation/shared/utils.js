import{getSortedPathnames as e,matchesPathname as n,isLocalizableHref as t,prefixPathname as r,getLocalePrefix as o,normalizeTrailingSlash as a}from"../../shared/utils.js";function i(e){return"string"==typeof e?{pathname:e}:e}function c(e){function n(e){return String(e)}const t=new URLSearchParams;for(const[r,o]of Object.entries(e))Array.isArray(o)?o.forEach((e=>{t.append(r,n(e))})):t.set(r,n(o));return"?"+t.toString()}function f(e){let{pathname:n,locale:t,params:r,pathnames:o,query:i}=e;function f(e){let n=o[e];return n||(n=e),n}function s(e){const n="string"==typeof e?e:e[t];let o=n;if(r&&Object.entries(r).forEach((e=>{let n,t,[r,a]=e;Array.isArray(a)?(n="(\\[)?\\[...".concat(r,"\\](\\])?"),t=a.map((e=>String(e))).join("/")):(n="\\[".concat(r,"\\]"),t=String(a)),o=o.replace(new RegExp(n,"g"),t)})),o=o.replace(/\[\[\.\.\..+\]\]/g,""),o=a(o),o.includes("["))throw new Error("Insufficient params provided for localized pathname.\nTemplate: ".concat(n,"\nParams: ").concat(JSON.stringify(r)));return i&&(o+=c(i)),o}if("string"==typeof n){return s(f(n))}{const{pathname:e,...t}=n;return{...t,pathname:s(f(e))}}}function s(t,r,o){const a=e(Object.keys(o)),i=decodeURI(r);for(const e of a){const r=o[e];if("string"==typeof r){if(n(r,i))return e}else if(n(r[t],i))return e}return r}function l(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?n:n.replace(e,"")}function d(e,n,a,i,c){const{mode:f}=a.localePrefix;let s;if(void 0!==c)s=c;else if(t(e))if("always"===f)s=!0;else if("as-needed"===f){let e=a.defaultLocale;if(a.domains){const n=a.domains.find((e=>e.domain===i));n?e=n.defaultLocale:i||console.error("You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\n\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded")}s=e!==n}return s?r(o(n,a.localePrefix),e):e}function u(e){var n;if("as-needed"===(null===(n=e.localePrefix)||void 0===n?void 0:n.mode)&&!("defaultLocale"in e))throw new Error("`localePrefix: 'as-needed' requires a `defaultLocale`.")}export{d as applyPathnamePrefix,f as compileLocalizedPathname,l as getBasePath,s as getRoute,i as normalizeNameOrNameWithParams,c as serializeSearchParams,u as validateReceivedConfig};
