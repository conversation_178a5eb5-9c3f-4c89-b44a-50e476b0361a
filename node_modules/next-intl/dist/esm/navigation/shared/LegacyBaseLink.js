"use client";
import{extends as e}from"../../_virtual/_rollupPluginBabelHelpers.js";import{usePathname as o}from"next/navigation";import r,{forwardRef as l,useState as t,useEffect as i}from"react";import a from"../../react-client/useLocale.js";import{isLocalizableHref as n,prefixHref as s,localizeHref as m}from"../../shared/utils.js";import c from"./BaseLink.js";function f(l,f){let{href:p,locale:u,localeCookie:d,localePrefixMode:x,prefix:j,...k}=l;const h=o(),v=a(),C=u!==v,[L,g]=t((()=>n(p)&&("never"!==x||C)?s(p,j):p));return i((()=>{h&&g(m(p,u,v,h,j))}),[v,p,u,h,j]),r.createElement(c,e({ref:f,href:L,locale:u,localeCookie:d},k))}const p=l(f);p.displayName="ClientLink";export{p as default};
