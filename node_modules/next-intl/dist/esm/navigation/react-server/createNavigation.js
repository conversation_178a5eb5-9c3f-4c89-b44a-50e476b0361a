import e from"../shared/createSharedNavigationFns.js";import o from"./getServerLocale.js";function t(t){const{config:n,...r}=e((function(){return o()}),t);function u(e){return()=>{throw new Error("`".concat(e,"` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component."))}}return{...r,usePathname:u("usePathname"),useRouter:u("useRouter")}}export{t as default};
