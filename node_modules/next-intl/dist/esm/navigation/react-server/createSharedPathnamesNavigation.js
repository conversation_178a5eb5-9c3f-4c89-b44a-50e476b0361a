import{extends as e}from"../../_virtual/_rollupPluginBabelHelpers.js";import r from"react";import{receiveLocalePrefixConfig as o,receiveLocaleCookie as n}from"../../routing/config.js";import t from"./ServerLink.js";import{serverRedirect as i,serverPermanentRedirect as a}from"./redirects.js";function l(l){const u=o(null==l?void 0:l.localePrefix),c=n(null==l?void 0:l.localeCookie);function f(e){return()=>{throw new Error("`".concat(e,"` is not supported in Server Components. You can use this hook if you convert the component to a Client Component."))}}return{Link:function(o){return r.createElement(t,e({localeCookie:c,localePrefix:u},o))},redirect:function(e){for(var r=arguments.length,o=new Array(r>1?r-1:0),n=1;n<r;n++)o[n-1]=arguments[n];return i({pathname:e,localePrefix:u},...o)},permanentRedirect:function(e){for(var r=arguments.length,o=new Array(r>1?r-1:0),n=1;n<r;n++)o[n-1]=arguments[n];return a({pathname:e,localePrefix:u},...o)},usePathname:f("usePathname"),useRouter:f("useRouter")}}export{l as default};
