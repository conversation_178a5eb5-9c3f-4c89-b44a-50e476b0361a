import{extends as e}from"../../_virtual/_rollupPluginBabelHelpers.js";import r from"react";import{receiveRoutingConfig as o}from"../../routing/config.js";import{getRequestLocale as t}from"../../server/react-server/RequestLocaleLegacy.js";import{compileLocalizedPathname as a,normalizeNameOrNameWithParams as n}from"../shared/utils.js";import l from"./ServerLink.js";import{serverRedirect as c,serverPermanentRedirect as i}from"./redirects.js";function s(s){const m=o(s);function f(e){let{href:r,locale:o}=e;return a({...n(r),locale:o,pathnames:m.pathnames})}function u(e){return()=>{throw new Error("`".concat(e,"` is not supported in Server Components. You can use this hook if you convert the component to a Client Component."))}}return{Link:function(o){let{href:n,locale:c,...i}=o;const s=t(),f=c||s;return r.createElement(l,e({href:a({locale:f,pathname:n,params:"object"==typeof n?n.params:void 0,pathnames:m.pathnames}),locale:c,localeCookie:m.localeCookie,localePrefix:m.localePrefix},i))},redirect:function(e){const r=f({href:e,locale:t()});for(var o=arguments.length,a=new Array(o>1?o-1:0),n=1;n<o;n++)a[n-1]=arguments[n];return c({localePrefix:m.localePrefix,pathname:r},...a)},permanentRedirect:function(e){const r=f({href:e,locale:t()});for(var o=arguments.length,a=new Array(o>1?o-1:0),n=1;n<o;n++)a[n-1]=arguments[n];return i({localePrefix:m.localePrefix,pathname:r},...a)},getPathname:f,usePathname:u("usePathname"),useRouter:u("useRouter")}}export{s as default};
