import{extends as e}from"../../_virtual/_rollupPluginBabelHelpers.js";import r from"react";import{getLocalePrefix as o}from"../../shared/utils.js";import a from"../shared/LegacyBaseLink.js";import t from"../../server/react-server/getLocale.js";async function l(l){let{locale:s,localePrefix:i,...c}=l;const m=s||await t(),f=o(m,i);return r.createElement(a,e({locale:m,localePrefixMode:i.mode,prefix:f},c))}export{l as default};
