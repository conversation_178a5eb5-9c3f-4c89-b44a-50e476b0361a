"use client";
import{extends as e}from"../_virtual/_rollupPluginBabelHelpers.js";import l from"react";import{IntlProvider as t}from"use-intl/_IntlProvider";function r(r){let{locale:o,...i}=r;if(!o)throw new Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale");return l.createElement(t,e({locale:o},i))}export{r as default};
