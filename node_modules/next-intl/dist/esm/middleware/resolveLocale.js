import{match as e}from"@formatjs/intl-localematcher";import o from"negotiator";import{getPathnameMatch as l,isLocaleSupportedOnDomain as t,getHost as a}from"./utils.js";function n(l,t,a){let n;const c=new o({headers:{"accept-language":l.get("accept-language")||void 0}}).languages();try{const o=function(e){return e.slice().sort(((e,o)=>o.length-e.length))}(t);n=e(c,o,a)}catch(e){}return n}function c(e,o){if(e.localeCookie&&o.has(e.localeCookie.name)){var l;const t=null===(l=o.get(e.localeCookie.name))||void 0===l?void 0:l.value;if(t&&e.locales.includes(t))return t}}function i(e,o,t,a){let i;var r;a&&(i=null===(r=l(a,e.locales,e.localePrefix))||void 0===r?void 0:r.locale);return!i&&e.localeDetection&&(i=c(e,t)),!i&&e.localeDetection&&(i=n(o,e.locales,e.defaultLocale)),i||(i=e.defaultLocale),i}function r(e,o,r,u){const f=function(e,o){const l=a(e);if(l)return o.find((e=>e.domain===l))}(o,e.domains);if(!f)return{locale:i(e,o,r,u)};let s;if(u){var d;const o=null===(d=l(u,e.locales,e.localePrefix))||void 0===d?void 0:d.locale;if(o){if(!t(o,f))return{locale:o,domain:f};s=o}}if(!s&&e.localeDetection){const o=c(e,r);o&&t(o,f)&&(s=o)}if(!s&&e.localeDetection){const l=n(o,f.locales||e.locales,f.defaultLocale);l&&(s=l)}return s||(s=f.defaultLocale),{locale:s,domain:f}}function u(e,o,l,t){return e.domains?r(e,o,l,t):{locale:i(e,o,l,t)}}export{u as default,n as getAcceptLanguageLocale};
