import{NextResponse as e}from"next/server";import{receiveRoutingConfig as o}from"../routing/config.js";import{HEADER_LOCALE_NAME as l}from"../shared/constants.js";import{matchesPathname as t,normalizeTrailingSlash as a,getLocalePrefix as r}from"../shared/utils.js";import n from"./getAlternateLinksHeaderValue.js";import i from"./resolveLocale.js";import s from"./syncCookie.js";import{sanitizePathname as c,isLocaleSupportedOnDomain as d,getNormalizedPathname as f,getPathnameMatch as m,getInternalTemplate as h,formatTemplatePathname as u,formatPathname as x,getBestMatchingDomain as p,applyBasePath as v,getLocaleAsPrefix as U}from"./utils.js";function P(P,k){var L,g,j;const w=o({...P,alternateLinks:null!==(L=null==k?void 0:k.alternateLinks)&&void 0!==L?L:P.alternateLinks,localeDetection:null!==(g=null==k?void 0:k.localeDetection)&&void 0!==g?g:P.localeDetection,localeCookie:null!==(j=null==k?void 0:k.localeCookie)&&void 0!==j?j:P.localeCookie});return function(o){var P;let k;try{k=decodeURI(o.nextUrl.pathname)}catch(o){return e.next()}const L=c(k),{domain:g,locale:j}=i(w,o.headers,o.cookies,L),C=g?g.defaultLocale===j:j===w.defaultLocale,b=(null===(P=w.domains)||void 0===P?void 0:P.filter((e=>d(j,e))))||[],y=null!=w.domains&&!g;function D(t){const a=new URL(t,o.url);o.nextUrl.basePath&&(a.pathname=v(a.pathname,o.nextUrl.basePath));const r=new Headers(o.headers);return r.set(l,j),e.rewrite(a,{request:{headers:r}})}function R(l,t){const r=new URL(l,o.url);if(r.pathname=a(r.pathname),b.length>0&&!t&&g){const e=p(g,j,b);e&&(t=e.domain,e.defaultLocale===j&&"as-needed"===w.localePrefix.mode&&(r.pathname=f(r.pathname,w.locales,w.localePrefix)))}var n,i;t&&(r.host=t,o.headers.get("x-forwarded-host")&&(r.protocol=null!==(n=o.headers.get("x-forwarded-proto"))&&void 0!==n?n:o.nextUrl.protocol,r.port=null!==(i=o.headers.get("x-forwarded-port"))&&void 0!==i?i:""));return o.nextUrl.basePath&&(r.pathname=v(r.pathname,o.nextUrl.basePath)),e.redirect(r.toString())}const q=f(L,w.locales,w.localePrefix),H=m(L,w.locales,w.localePrefix),z=null!=H,A="never"===w.localePrefix.mode||C&&"as-needed"===w.localePrefix.mode;let I,S,V=q;const B=w.pathnames;if(B){let e;if([e,S]=h(B,q,j),S){const l=B[S],a="string"==typeof l?l:l[j];if(t(a,q))V=u(q,a,S);else{let t;t=e?"string"==typeof l?l:l[e]:S;const n=A?void 0:r(j,w.localePrefix),i=u(q,t,a);I=R(x(i,n,o.nextUrl.search))}}}if(!I)if("/"!==V||z){const e=x(V,U(j),o.nextUrl.search);if(z){const l=x(q,H.prefix,o.nextUrl.search);if("never"===w.localePrefix.mode)I=R(x(q,void 0,o.nextUrl.search));else if(H.exact)if(C&&A)I=R(x(q,void 0,o.nextUrl.search));else if(w.domains){const o=p(g,H.locale,b);I=(null==g?void 0:g.domain)===(null==o?void 0:o.domain)||y?D(e):R(l,null==o?void 0:o.domain)}else I=D(e);else I=R(l)}else I=A?D(e):R(x(q,r(j,w.localePrefix),o.nextUrl.search))}else I=A?D(x(V,U(j),o.nextUrl.search)):R(x(q,r(j,w.localePrefix),o.nextUrl.search));return w.localeDetection&&w.localeCookie&&s(o,I,j,w.localeCookie),"never"!==w.localePrefix.mode&&w.alternateLinks&&w.locales.length>1&&I.headers.set("Link",n({routing:w,localizedPathnames:null!=S&&B?B[S]:void 0,request:o,resolvedLocale:j})),I}}export{P as default};
