import{getSortedPathnames as t,matchesPathname as e,normalizeTrailingSlash as n,getLocalePrefix as r,templateToRegex as o,prefixPathname as c}from"../shared/utils.js";function l(n,r,o){const c=t(Object.keys(n));for(const t of c){const c=n[t];if("string"==typeof c){if(e(c,r))return[void 0,t]}else{const n=Object.entries(c),l=n.findIndex((t=>{let[e]=t;return e===o}));l>0&&n.unshift(n.splice(l,1)[0]);for(const[o,c]of n)if(e(c,r))return[o,t]}}for(const t of Object.keys(n))if(e(t,r))return[void 0,t];return[void 0,void 0]}function i(t,e,r,o){let c="";return c+=d(r,a(e,t)),c=n(c),c}function u(t,e,r){t.endsWith("/")||(t+="/");const o=f(e,r),c=new RegExp("^(".concat(o.map((t=>{let[,e]=t;return e.replaceAll("/","\\/")})).join("|"),")/(.*)"),"i"),l=t.match(c);let i=l?"/"+l[2]:t;return"/"!==i&&(i=n(i)),i}function f(t,e){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const o=t.map((t=>[t,r(t,e)]));return n&&o.sort(((t,e)=>e[1].length-t[1].length)),o}function s(t,e,n){const r=f(e,n);for(const[e,n]of r){let r,o;if(t===n||t.startsWith(n+"/"))r=o=!0;else{const e=t.toLowerCase(),c=n.toLowerCase();(e===c||e.startsWith(c+"/"))&&(r=!1,o=!0)}if(o)return{locale:e,prefix:n,matchedPrefix:t.slice(0,n.length),exact:r}}}function a(t,e){const r=n(e),c=n(t),l=o(c).exec(r);if(!l)return;const i={};for(let t=1;t<l.length;t++){var u;const e=null===(u=c.match(/\[([^\]]+)\]/g))||void 0===u?void 0:u[t-1].replace(/[[\]]/g,"");e&&(i[e]=l[t])}return i}function d(t,e){if(!e)return t;let n=t=t.replace(/\[\[/g,"[").replace(/\]\]/g,"]");return Object.entries(e).forEach((t=>{let[e,r]=t;n=n.replace("[".concat(e,"]"),r)})),n}function h(t,e,n){let r=t;return e&&(r=c(e,r)),n&&(r+=n),r}function v(t){var e,n;return null!==(e=null!==(n=t.get("x-forwarded-host"))&&void 0!==n?n:t.get("host"))&&void 0!==e?e:void 0}function g(t,e){return e.defaultLocale===t||!e.locales||e.locales.includes(t)}function p(t,e,n){let r;return t&&g(e,t)&&(r=t),r||(r=n.find((t=>t.defaultLocale===e))),r||(r=n.find((t=>{var n;return null===(n=t.locales)||void 0===n?void 0:n.includes(e)}))),r||null!=(null==t?void 0:t.locales)||(r=t),r||(r=n.find((t=>!t.locales))),r}function x(t,e){return n(e+t)}function m(t){return"/".concat(t)}function j(t){return t.replace(/\\/g,"%5C").replace(/\/+/g,"/")}export{x as applyBasePath,h as formatPathname,d as formatPathnameTemplate,i as formatTemplatePathname,p as getBestMatchingDomain,v as getHost,l as getInternalTemplate,m as getLocaleAsPrefix,f as getLocalePrefixes,u as getNormalizedPathname,s as getPathnameMatch,a as getRouteParams,g as isLocaleSupportedOnDomain,j as sanitizePathname};
