import{normalizeTrailingSlash as e}from"../shared/utils.js";import{getHost as a,getNormalizedPathname as t,getLocalePrefixes as o,isLocaleSupportedOnDomain as n,applyBasePath as l,formatTemplatePathname as r}from"./utils.js";function c(c){var m;let{localizedPathnames:p,request:s,resolvedLocale:i,routing:f}=c;const h=s.nextUrl.clone(),u=a(s.headers);function d(a,t){return a.pathname=e(a.pathname),s.nextUrl.basePath&&((a=new URL(a)).pathname=l(a.pathname,s.nextUrl.basePath)),"<".concat(a.toString(),'>; rel="alternate"; hreflang="').concat(t,'"')}function x(e,a){return p&&"object"==typeof p?r(e,p[i],p[a]):e}u&&(h.port="",h.host=u),h.protocol=null!==(m=s.headers.get("x-forwarded-proto"))&&void 0!==m?m:h.protocol,h.pathname=t(h.pathname,f.locales,f.localePrefix);const w=o(f.locales,f.localePrefix,!1).flatMap((e=>{let a,[t,o]=e;function l(e){return"/"===e?o:o+e}if(f.domains){return f.domains.filter((e=>n(t,e))).map((e=>(a=new URL(h),a.port="",a.host=e.domain,a.pathname=x(h.pathname,t),t===e.defaultLocale&&"always"!==f.localePrefix.mode||(a.pathname=l(a.pathname)),d(a,t))))}{let e;e=p&&"object"==typeof p?x(h.pathname,t):h.pathname,t===f.defaultLocale&&"always"!==f.localePrefix.mode||(e=l(e)),a=new URL(e,h)}return d(a,t)}));if(!f.domains&&("always"!==f.localePrefix.mode||"/"===h.pathname)){const e=new URL(x(h.pathname,f.defaultLocale),h);w.push(d(e,"x-default"))}return w.join(", ")}export{c as default};
