import{headers as e}from"next/headers";import{notFound as t}from"next/navigation";import{cache as n}from"react";import{HEADER_LOCALE_NAME as o}from"../../shared/constants.js";import{getCachedRequestLocale as r}from"./RequestLocaleCache.js";const i=n((function(){let n;try{n=e().get(o)}catch(e){throw e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest?new Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e}):e}return n||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),t()),n}));function s(){return r()||i()}export{s as getRequestLocale};
