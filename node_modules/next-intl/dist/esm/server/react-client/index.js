function e(e){return()=>{throw new Error("`".concat(e,"` is not supported in Client Components."))}}function t(){return e("getRequestConfig")}const n=e("getFormatter"),o=e("getNow"),s=e("getTimeZone"),r=e("getMessages"),a=e("getLocale"),g=e("getTranslations"),u=e("unstable_setRequestLocale"),c=e("setRequestLocale");export{n as getFormatter,a as getLocale,r as getMessages,o as getNow,t as getRequestConfig,s as getTimeZone,g as getTranslations,c as setRequestLocale,u as unstable_setRequestLocale};
