import n from"fs";import t from"path";function e(n){return["".concat(n,".ts"),"".concat(n,".tsx"),"".concat(n,".js"),"".concat(n,".jsx")]}let o=!1;function r(r,i){function s(e){return n.existsSync(function(n){const e=[];return i&&e.push(i),e.push(n),t.resolve(...e)}(e))}if(r){if(!s(r))throw new Error("[next-intl] Could not find i18n config at ".concat(r,", please provide a valid path."));return r}for(const n of[...e("./i18n/request"),...e("./src/i18n/request")])if(s(n))return n;for(const n of[...e("./i18n"),...e("./src/i18n")])if(s(n))return o||(console.warn("\n[next-intl] Reading request configuration from ".concat(n," is deprecated, please see https://next-intl.dev/blog/next-intl-3-22#i18n-request — you can either move your configuration to ./i18n/request.ts or provide a custom path in your Next.js config:\n\nconst withNextIntl = createNextIntlPlugin(\n  './path/to/i18n/request.tsx'\n);\n")),o=!0),n;throw new Error("\n[next-intl] Could not locate request configuration module.\n\nThis path is supported by default: ./(src/)i18n/request.{js,jsx,ts,tsx}\n\nAlternatively, you can specify a custom location in your Next.js config:\n\nconst withNextIntl = createNextIntlPlugin(\n  './path/to/i18n/request.tsx'\n);\n")}module.exports=function(n){return function(e){return function(n,e){null!=(null==e?void 0:e.i18n)&&console.warn("\n[next-intl] An `i18n` property was found in your Next.js config. This likely causes conflicts and should therefore be removed if you use the App Router.\n\nIf you're in progress of migrating from the Pages Router, you can refer to this example: https://next-intl.dev/examples#app-router-migration\n");const o={};if(null!=process.env.TURBOPACK){var i,s;if(null!=n&&n.startsWith("/"))throw new Error("[next-intl] Turbopack support for next-intl currently does not support absolute paths, please provide a relative one (e.g. './src/i18n/config.ts').\n\nFound: "+n+"\n");o.experimental={...null==e?void 0:e.experimental,turbo:{...null==e||null===(i=e.experimental)||void 0===i?void 0:i.turbo,resolveAlias:{...null==e||null===(s=e.experimental)||void 0===s||null===(s=s.turbo)||void 0===s?void 0:s.resolveAlias,"next-intl/config":r(n)}}}}else o.webpack=function(){for(var o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];let[l,u]=i;return l.resolve.alias["next-intl/config"]=t.resolve(l.context,r(n,l.context)),"function"==typeof(null==e?void 0:e.webpack)?e.webpack(l,u):l};return o.env={...null==e?void 0:e.env,_next_intl_trailing_slash:null!=e&&e.trailingSlash?"true":void 0},Object.assign({},e,o)}(n,e)}};
