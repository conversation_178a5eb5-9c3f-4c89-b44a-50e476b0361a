'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var useIntl = require('use-intl');

/**
 * This is the main entry file when non-'react-server'
 * environments import from 'next-intl'.
 *
 * Maintainer notes:
 * - Make sure this mirrors the API from 'react-server'.
 * - Make sure everything exported from this module is
 *   supported in all Next.js versions that are supported.
 */


// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
function callHook(name, hook) {
  return function () {
    try {
      return hook(...arguments);
    } catch (_unused) {
      throw new Error("Failed to call `".concat(name, "` because the context from `NextIntlClientProvider` was not found.\n\nThis can happen because:\n1) You intended to render this component as a Server Component, the render\n   failed, and therefore React attempted to render the component on the client\n   instead. If this is the case, check the console for server errors.\n2) You intended to render this component on the client side, but no context was found.\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context") );
    }
  };
}
const useTranslations = callHook('useTranslations', useIntl.useTranslations);
const useFormatter = callHook('useFormatter', useIntl.useFormatter);

exports.useFormatter = useFormatter;
exports.useTranslations = useTranslations;
Object.keys(useIntl).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return useIntl[k]; }
  });
});
