'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var navigation = require('next/navigation');
var _useLocale = require('use-intl/_useLocale');
var constants = require('../shared/constants.js');

let hasWarnedForParams = false;
function useLocale() {
  // The types aren't entirely correct here. Outside of Next.js
  // `useParams` can be called, but the return type is `null`.
  const params = navigation.useParams();
  let locale;
  try {
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/rules-of-hooks, react-compiler/react-compiler -- False positive
    locale = _useLocale.useLocale();
  } catch (error) {
    if (typeof (params === null || params === void 0 ? void 0 : params[constants.LOCALE_SEGMENT_NAME]) === 'string') {
      if (!hasWarnedForParams) {
        console.warn('Deprecation warning: `useLocale` has returned a default from `useParams().locale` since no `NextIntlClientProvider` ancestor was found for the calling component. This behavior will be removed in the next major version. Please ensure all Client Components that use `next-intl` are wrapped in a `NextIntlClientProvider`.');
        hasWarnedForParams = true;
      }
      locale = params[constants.LOCALE_SEGMENT_NAME];
    } else {
      throw error;
    }
  }
  return locale;
}

exports.default = useLocale;
