'use strict';

var fs = require('fs');
var path = require('path');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var fs__default = /*#__PURE__*/_interopDefault(fs);
var path__default = /*#__PURE__*/_interopDefault(path);

/* eslint-env node */

function withExtensions(localPath) {
  return ["".concat(localPath, ".ts"), "".concat(localPath, ".tsx"), "".concat(localPath, ".js"), "".concat(localPath, ".jsx")];
}
let hasWarnedForDeprecatedI18nConfig = false;
function resolveI18nPath(providedPath, cwd) {
  function resolvePath(pathname) {
    const parts = [];
    if (cwd) parts.push(cwd);
    parts.push(pathname);
    return path__default.default.resolve(...parts);
  }
  function pathExists(pathname) {
    return fs__default.default.existsSync(resolvePath(pathname));
  }
  if (providedPath) {
    if (!pathExists(providedPath)) {
      throw new Error("[next-intl] Could not find i18n config at ".concat(providedPath, ", please provide a valid path."));
    }
    return providedPath;
  } else {
    for (const candidate of [...withExtensions('./i18n/request'), ...withExtensions('./src/i18n/request')]) {
      if (pathExists(candidate)) {
        return candidate;
      }
    }
    for (const candidate of [...withExtensions('./i18n'), ...withExtensions('./src/i18n')]) {
      if (pathExists(candidate)) {
        if (!hasWarnedForDeprecatedI18nConfig) {
          console.warn("\n[next-intl] Reading request configuration from ".concat(candidate, " is deprecated, please see https://next-intl.dev/blog/next-intl-3-22#i18n-request \u2014 you can either move your configuration to ./i18n/request.ts or provide a custom path in your Next.js config:\n\nconst withNextIntl = createNextIntlPlugin(\n  './path/to/i18n/request.tsx'\n);\n"));
          hasWarnedForDeprecatedI18nConfig = true;
        }
        return candidate;
      }
    }
    throw new Error("\n[next-intl] Could not locate request configuration module.\n\nThis path is supported by default: ./(src/)i18n/request.{js,jsx,ts,tsx}\n\nAlternatively, you can specify a custom location in your Next.js config:\n\nconst withNextIntl = createNextIntlPlugin(\n  './path/to/i18n/request.tsx'\n);\n");
  }
}
function initPlugin(i18nPath, nextConfig) {
  if ((nextConfig === null || nextConfig === void 0 ? void 0 : nextConfig.i18n) != null) {
    console.warn("\n[next-intl] An `i18n` property was found in your Next.js config. This likely causes conflicts and should therefore be removed if you use the App Router.\n\nIf you're in progress of migrating from the Pages Router, you can refer to this example: https://next-intl.dev/examples#app-router-migration\n");
  }
  const useTurbo = process.env.TURBOPACK != null;
  const nextIntlConfig = {};

  // Assign alias for `next-intl/config`
  if (useTurbo) {
    var _nextConfig$experimen, _nextConfig$experimen2;
    if (i18nPath !== null && i18nPath !== void 0 && i18nPath.startsWith('/')) {
      throw new Error("[next-intl] Turbopack support for next-intl currently does not support absolute paths, please provide a relative one (e.g. './src/i18n/config.ts').\n\nFound: " + i18nPath + '\n');
    }
    nextIntlConfig.experimental = {
      ...(nextConfig === null || nextConfig === void 0 ? void 0 : nextConfig.experimental),
      turbo: {
        ...(nextConfig === null || nextConfig === void 0 || (_nextConfig$experimen = nextConfig.experimental) === null || _nextConfig$experimen === void 0 ? void 0 : _nextConfig$experimen.turbo),
        resolveAlias: {
          ...(nextConfig === null || nextConfig === void 0 || (_nextConfig$experimen2 = nextConfig.experimental) === null || _nextConfig$experimen2 === void 0 || (_nextConfig$experimen2 = _nextConfig$experimen2.turbo) === null || _nextConfig$experimen2 === void 0 ? void 0 : _nextConfig$experimen2.resolveAlias),
          // Turbo aliases don't work with absolute
          // paths (see error handling above)
          'next-intl/config': resolveI18nPath(i18nPath)
        }
      }
    };
  } else {
    nextIntlConfig.webpack = function webpack() {
      for (var _len = arguments.length, _ref = new Array(_len), _key = 0; _key < _len; _key++) {
        _ref[_key] = arguments[_key];
      }
      let [config, options] = _ref;
      // Webpack requires absolute paths
      config.resolve.alias['next-intl/config'] = path__default.default.resolve(config.context, resolveI18nPath(i18nPath, config.context));
      if (typeof (nextConfig === null || nextConfig === void 0 ? void 0 : nextConfig.webpack) === 'function') {
        return nextConfig.webpack(config, options);
      }
      return config;
    };
  }

  // Forward config
  nextIntlConfig.env = {
    ...(nextConfig === null || nextConfig === void 0 ? void 0 : nextConfig.env),
    _next_intl_trailing_slash: nextConfig !== null && nextConfig !== void 0 && nextConfig.trailingSlash ? 'true' : undefined
  };
  return Object.assign({}, nextConfig, nextIntlConfig);
}
module.exports = function createNextIntlPlugin(i18nPath) {
  return function withNextIntl(nextConfig) {
    return initPlugin(i18nPath, nextConfig);
  };
};
