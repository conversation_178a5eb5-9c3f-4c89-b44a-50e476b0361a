'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

/**
 * Allows to import `next-intl/server` in non-RSC environments.
 *
 * This is mostly relevant for testing, since e.g. a `generateMetadata`
 * export from a page might use `next-intl/server`, but the test
 * only uses the default export for a page.
 */

function notSupported(message) {
  return () => {
    throw new Error("`".concat(message, "` is not supported in Client Components."));
  };
}
function getRequestConfig() {
  return notSupported('getRequestConfig');
}
const getFormatter = notSupported('getFormatter');
const getNow = notSupported('getNow');
const getTimeZone = notSupported('getTimeZone');
const getMessages = notSupported('getMessages');
const getLocale = notSupported('getLocale');

// The type of `getTranslations` is not assigned here because it
// causes a type error. The types use the `react-server` entry
// anyway, therefore this is irrelevant.
const getTranslations = notSupported('getTranslations');
const unstable_setRequestLocale = notSupported('unstable_setRequestLocale');
const setRequestLocale = notSupported('setRequestLocale');

exports.getFormatter = getFormatter;
exports.getLocale = getLocale;
exports.getMessages = getMessages;
exports.getNow = getNow;
exports.getRequestConfig = getRequestConfig;
exports.getTimeZone = getTimeZone;
exports.getTranslations = getTranslations;
exports.setRequestLocale = setRequestLocale;
exports.unstable_setRequestLocale = unstable_setRequestLocale;
