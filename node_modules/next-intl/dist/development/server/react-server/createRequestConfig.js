'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var getRuntimeConfig = require('next-intl/config');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var getRuntimeConfig__default = /*#__PURE__*/_interopDefault(getRuntimeConfig);



Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () { return getRuntimeConfig__default.default; }
});
