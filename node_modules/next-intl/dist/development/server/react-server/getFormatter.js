'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var React = require('react');
var core = require('use-intl/core');
var getConfig = require('./getConfig.js');

async function getFormatterCachedImpl(locale) {
  const config = await getConfig.default(locale);
  return core.createFormatter(config);
}
const getFormatterCached = React.cache(getFormatterCachedImpl);

/**
 * Returns a formatter based on the given locale.
 *
 * The formatter automatically receives the request config, but
 * you can override it by passing in additional options.
 */
async function getFormatter(opts) {
  return getFormatterCached(opts === null || opts === void 0 ? void 0 : opts.locale);
}

exports.default = getFormatter;
