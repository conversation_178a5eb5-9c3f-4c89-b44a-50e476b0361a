'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var React = require('react');

// See https://github.com/vercel/next.js/discussions/58862
function getCacheImpl() {
  const value = {
    locale: undefined
  };
  return value;
}
const getCache = React.cache(getCacheImpl);
function getCachedRequestLocale() {
  return getCache().locale;
}
function setCachedRequestLocale(locale) {
  getCache().locale = locale;
}

exports.getCachedRequestLocale = getCachedRequestLocale;
exports.setCachedRequestLocale = setCachedRequestLocale;
