'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var React = require('react');
var getConfig = require('./getConfig.js');

async function getTimeZoneCachedImpl(locale) {
  const config = await getConfig.default(locale);
  return config.timeZone;
}
const getTimeZoneCached = React.cache(getTimeZoneCachedImpl);
async function getTimeZone(opts) {
  return getTimeZoneCached(opts === null || opts === void 0 ? void 0 : opts.locale);
}

exports.default = getTimeZone;
