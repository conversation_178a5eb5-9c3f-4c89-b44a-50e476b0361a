'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var React = require('react');
var getConfig = require('./getConfig.js');

function getMessagesFromConfig(config) {
  if (!config.messages) {
    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages');
  }
  return config.messages;
}
async function getMessagesCachedImpl(locale) {
  const config = await getConfig.default(locale);
  return getMessagesFromConfig(config);
}
const getMessagesCached = React.cache(getMessagesCachedImpl);
async function getMessages(opts) {
  return getMessagesCached(opts === null || opts === void 0 ? void 0 : opts.locale);
}

exports.default = getMessages;
exports.getMessagesFromConfig = getMessagesFromConfig;
