'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var React = require('react');
var core = require('use-intl/core');
var getConfig = require('./getConfig.js');

// Maintainer note: `getTranslations` has two different call signatures.
// We need to define these with function overloads, otherwise TypeScript
// messes up the return type.

// CALL SIGNATURE 1: `getTranslations(namespace)`

// CALL SIGNATURE 2: `getTranslations({locale, namespace})`

// IMPLEMENTATION
async function getTranslations(namespaceOrOpts) {
  let namespace;
  let locale;
  if (typeof namespaceOrOpts === 'string') {
    namespace = namespaceOrOpts;
  } else if (namespaceOrOpts) {
    locale = namespaceOrOpts.locale;
    namespace = namespaceOrOpts.namespace;
  }
  const config = await getConfig.default(locale);
  return core.createTranslator({
    ...config,
    namespace,
    messages: config.messages
  });
}
var getTranslations$1 = React.cache(getTranslations);

exports.default = getTranslations$1;
