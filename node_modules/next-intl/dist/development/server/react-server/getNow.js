'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var React = require('react');
var getConfig = require('./getConfig.js');

async function getNowCachedImpl(locale) {
  const config = await getConfig.default(locale);
  return config.now;
}
const getNowCached = React.cache(getNowCachedImpl);
async function getNow(opts) {
  return getNowCached(opts === null || opts === void 0 ? void 0 : opts.locale);
}

exports.default = getNow;
