'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var headers = require('next/headers');
var navigation = require('next/navigation');
var React = require('react');
var constants = require('../../shared/constants.js');
var RequestLocaleCache = require('./RequestLocaleCache.js');

// This was originally built for Next.js <14, where `headers()` was not async.
// With https://github.com/vercel/next.js/pull/68812, the API became async.
// This file can be removed once we remove the legacy navigation APIs.
function getHeaders() {
  return headers.headers();
}
function getLocaleFromHeaderImpl() {
  let locale;
  try {
    locale = getHeaders().get(constants.HEADER_LOCALE_NAME);
  } catch (error) {
    if (error instanceof Error && error.digest === 'DYNAMIC_SERVER_USAGE') {
      throw new Error('Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering', {
        cause: error
      });
    } else {
      throw error;
    }
  }
  if (!locale) {
    {
      console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n");
    }
    navigation.notFound();
  }
  return locale;
}
const getLocaleFromHeader = React.cache(getLocaleFromHeaderImpl);
function getRequestLocale() {
  return RequestLocaleCache.getCachedRequestLocale() || getLocaleFromHeader();
}

exports.getRequestLocale = getRequestLocale;
