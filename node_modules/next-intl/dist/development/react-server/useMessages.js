'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var getMessages = require('../server/react-server/getMessages.js');
var useConfig = require('./useConfig.js');

function useMessages() {
  for (var _len = arguments.length, _ref = new Array(_len), _key = 0; _key < _len; _key++) {
    _ref[_key] = arguments[_key];
  }
  const config = useConfig.default('useMessages');
  return getMessages.getMessagesFromConfig(config);
}

exports.default = useMessages;
