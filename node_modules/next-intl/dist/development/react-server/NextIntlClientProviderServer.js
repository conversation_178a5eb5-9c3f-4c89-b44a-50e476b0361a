'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../_virtual/_rollupPluginBabelHelpers.js');
var React = require('react');
var NextIntlClientProvider = require('../shared/NextIntlClientProvider.js');
var getLocale = require('../server/react-server/getLocale.js');
var getNow = require('../server/react-server/getNow.js');
var getTimeZone = require('../server/react-server/getTimeZone.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var React__default = /*#__PURE__*/_interopDefault(React);

async function NextIntlClientProviderServer(_ref) {
  let {
    locale,
    now,
    timeZone,
    ...rest
  } = _ref;
  return /*#__PURE__*/React__default.default.createElement(NextIntlClientProvider.default
  // We need to be careful about potentially reading from headers here.
  // See https://github.com/amannn/next-intl/issues/631
  , _rollupPluginBabelHelpers.extends({
    locale: locale !== null && locale !== void 0 ? locale : await getLocale.default(),
    now: now !== null && now !== void 0 ? now : await getNow.default(),
    timeZone: timeZone !== null && timeZone !== void 0 ? timeZone : await getTimeZone.default()
  }, rest));
}

exports.default = NextIntlClientProviderServer;
