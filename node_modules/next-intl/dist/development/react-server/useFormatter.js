'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var React = require('react');
var core = require('use-intl/core');
var useConfig = require('./useConfig.js');

const createFormatterCached = React.cache(core.createFormatter);
function useFormatter() {
  for (var _len = arguments.length, _ref = new Array(_len), _key = 0; _key < _len; _key++) {
    _ref[_key] = arguments[_key];
  }
  const config = useConfig.default('useFormatter');
  return createFormatterCached(config);
}

exports.default = useFormatter;
