'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var React = require('react');
var getConfig = require('../server/react-server/getConfig.js');

function useHook(hookName, promise) {
  try {
    return React.use(promise);
  } catch (error) {
    if (error instanceof TypeError && error.message.includes("Cannot read properties of null (reading 'use')")) {
      throw new Error("`".concat(hookName, "` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components"), {
        cause: error
      });
    } else {
      throw error;
    }
  }
}
function useConfig(hookName) {
  return useHook(hookName, getConfig.default());
}

exports.default = useConfig;
