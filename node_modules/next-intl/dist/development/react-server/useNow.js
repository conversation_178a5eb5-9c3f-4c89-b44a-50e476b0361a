'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var useConfig = require('./useConfig.js');

function useNow() {
  for (var _len = arguments.length, _ref = new Array(_len), _key = 0; _key < _len; _key++) {
    _ref[_key] = arguments[_key];
  }
  let [options] = _ref;
  if ((options === null || options === void 0 ? void 0 : options.updateInterval) != null) {
    console.error("`useNow` doesn't support the `updateInterval` option in Server Components, the value will be ignored. If you need the value to update, you can convert the component to a Client Component.");
  }
  const config = useConfig.default('useNow');
  return config.now;
}

exports.default = useNow;
