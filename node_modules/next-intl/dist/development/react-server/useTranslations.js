'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var getTranslator = require('./getTranslator.js');
var useConfig = require('./useConfig.js');

function useTranslations() {
  for (var _len = arguments.length, _ref = new Array(_len), _key = 0; _key < _len; _key++) {
    _ref[_key] = arguments[_key];
  }
  let [namespace] = _ref;
  const config = useConfig.default('useTranslations');
  return getTranslator.default(config, namespace);
}

exports.default = useTranslations;
