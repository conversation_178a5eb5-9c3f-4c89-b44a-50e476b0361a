'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

function receiveRoutingConfig(input) {
  var _input$localeDetectio, _input$alternateLinks;
  return {
    ...input,
    localePrefix: receiveLocalePrefixConfig(input.localePrefix),
    localeCookie: receiveLocaleCookie(input.localeCookie),
    localeDetection: (_input$localeDetectio = input.localeDetection) !== null && _input$localeDetectio !== void 0 ? _input$localeDetectio : true,
    alternateLinks: (_input$alternateLinks = input.alternateLinks) !== null && _input$alternateLinks !== void 0 ? _input$alternateLinks : true
  };
}
function receiveLocaleCookie(localeCookie) {
  return (localeCookie !== null && localeCookie !== void 0 ? localeCookie : true) ? {
    name: 'NEXT_LOCALE',
    maxAge: ********,
    // 1 year
    sameSite: 'lax',
    ...(typeof localeCookie === 'object' && localeCookie)

    // `path` needs to be provided based on a detected base path
    // that depends on the environment when setting a cookie
  } : false;
}
function receiveLocalePrefixConfig(localePrefix) {
  return typeof localePrefix === 'object' ? localePrefix : {
    mode: localePrefix || 'always'
  };
}

exports.receiveLocaleCookie = receiveLocaleCookie;
exports.receiveLocalePrefixConfig = receiveLocalePrefixConfig;
exports.receiveRoutingConfig = receiveRoutingConfig;
