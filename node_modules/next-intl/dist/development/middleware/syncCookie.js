'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

function syncCookie(request, response, locale, localeCookie) {
  var _request$cookies$get;
  const {
    name,
    ...rest
  } = localeCookie;
  const hasOutdatedCookie = ((_request$cookies$get = request.cookies.get(name)) === null || _request$cookies$get === void 0 ? void 0 : _request$cookies$get.value) !== locale;
  if (hasOutdatedCookie) {
    response.cookies.set(name, locale, {
      path: request.nextUrl.basePath || undefined,
      ...rest
    });
  }
}

exports.default = syncCookie;
