'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var utils$1 = require('../shared/utils.js');
var utils = require('./utils.js');

/**
 * See https://developers.google.com/search/docs/specialty/international/localized-versions
 */
function getAlternateLinksHeaderValue(_ref) {
  var _request$headers$get;
  let {
    localizedPathnames,
    request,
    resolvedLocale,
    routing
  } = _ref;
  const normalizedUrl = request.nextUrl.clone();
  const host = utils.getHost(request.headers);
  if (host) {
    normalizedUrl.port = '';
    normalizedUrl.host = host;
  }
  normalizedUrl.protocol = (_request$headers$get = request.headers.get('x-forwarded-proto')) !== null && _request$headers$get !== void 0 ? _request$headers$get : normalizedUrl.protocol;
  normalizedUrl.pathname = utils.getNormalizedPathname(normalizedUrl.pathname, routing.locales, routing.localePrefix);
  function getAlternateEntry(url, locale) {
    url.pathname = utils$1.normalizeTrailingSlash(url.pathname);
    if (request.nextUrl.basePath) {
      url = new URL(url);
      url.pathname = utils.applyBasePath(url.pathname, request.nextUrl.basePath);
    }
    return "<".concat(url.toString(), ">; rel=\"alternate\"; hreflang=\"").concat(locale, "\"");
  }
  function getLocalizedPathname(pathname, locale) {
    if (localizedPathnames && typeof localizedPathnames === 'object') {
      return utils.formatTemplatePathname(pathname, localizedPathnames[resolvedLocale], localizedPathnames[locale]);
    } else {
      return pathname;
    }
  }
  const links = utils.getLocalePrefixes(routing.locales, routing.localePrefix, false).flatMap(_ref2 => {
    let [locale, prefix] = _ref2;
    function prefixPathname(pathname) {
      if (pathname === '/') {
        return prefix;
      } else {
        return prefix + pathname;
      }
    }
    let url;
    if (routing.domains) {
      const domainConfigs = routing.domains.filter(cur => utils.isLocaleSupportedOnDomain(locale, cur));
      return domainConfigs.map(domainConfig => {
        url = new URL(normalizedUrl);
        url.port = '';
        url.host = domainConfig.domain;

        // Important: Use `normalizedUrl` here, as `url` potentially uses
        // a `basePath` that automatically gets applied to the pathname
        url.pathname = getLocalizedPathname(normalizedUrl.pathname, locale);
        if (locale !== domainConfig.defaultLocale || routing.localePrefix.mode === 'always') {
          url.pathname = prefixPathname(url.pathname);
        }
        return getAlternateEntry(url, locale);
      });
    } else {
      let pathname;
      if (localizedPathnames && typeof localizedPathnames === 'object') {
        pathname = getLocalizedPathname(normalizedUrl.pathname, locale);
      } else {
        pathname = normalizedUrl.pathname;
      }
      if (locale !== routing.defaultLocale || routing.localePrefix.mode === 'always') {
        pathname = prefixPathname(pathname);
      }
      url = new URL(pathname, normalizedUrl);
    }
    return getAlternateEntry(url, locale);
  });

  // Add x-default entry
  const shouldAddXDefault =
  // For domain-based routing there is no reasonable x-default
  !routing.domains && (routing.localePrefix.mode !== 'always' || normalizedUrl.pathname === '/');
  if (shouldAddXDefault) {
    const url = new URL(getLocalizedPathname(normalizedUrl.pathname, routing.defaultLocale), normalizedUrl);
    links.push(getAlternateEntry(url, 'x-default'));
  }
  return links.join(', ');
}

exports.default = getAlternateLinksHeaderValue;
