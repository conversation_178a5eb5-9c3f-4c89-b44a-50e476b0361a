'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var intlLocalematcher = require('@formatjs/intl-localematcher');
var Negotiator = require('negotiator');
var utils = require('./utils.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var Negotiator__default = /*#__PURE__*/_interopDefault(Negotiator);

function findDomainFromHost(requestHeaders, domains) {
  const host = utils.getHost(requestHeaders);
  if (host) {
    return domains.find(cur => cur.domain === host);
  }
  return undefined;
}
function orderLocales(locales) {
  // Workaround for https://github.com/formatjs/formatjs/issues/4469
  return locales.slice().sort((a, b) => b.length - a.length);
}
function getAcceptLanguageLocale(requestHeaders, locales, defaultLocale) {
  let locale;
  const languages = new Negotiator__default.default({
    headers: {
      'accept-language': requestHeaders.get('accept-language') || undefined
    }
  }).languages();
  try {
    const orderedLocales = orderLocales(locales);
    locale = intlLocalematcher.match(languages, orderedLocales, defaultLocale);
  } catch (_unused) {
    // Invalid language
  }
  return locale;
}
function getLocaleFromCookie(routing, requestCookies) {
  if (routing.localeCookie && requestCookies.has(routing.localeCookie.name)) {
    var _requestCookies$get;
    const value = (_requestCookies$get = requestCookies.get(routing.localeCookie.name)) === null || _requestCookies$get === void 0 ? void 0 : _requestCookies$get.value;
    if (value && routing.locales.includes(value)) {
      return value;
    }
  }
}
function resolveLocaleFromPrefix(routing, requestHeaders, requestCookies, pathname) {
  let locale;

  // Prio 1: Use route prefix
  if (pathname) {
    var _getPathnameMatch;
    locale = (_getPathnameMatch = utils.getPathnameMatch(pathname, routing.locales, routing.localePrefix)) === null || _getPathnameMatch === void 0 ? void 0 : _getPathnameMatch.locale;
  }

  // Prio 2: Use existing cookie
  if (!locale && routing.localeDetection) {
    locale = getLocaleFromCookie(routing, requestCookies);
  }

  // Prio 3: Use the `accept-language` header
  if (!locale && routing.localeDetection) {
    locale = getAcceptLanguageLocale(requestHeaders, routing.locales, routing.defaultLocale);
  }

  // Prio 4: Use default locale
  if (!locale) {
    locale = routing.defaultLocale;
  }
  return locale;
}
function resolveLocaleFromDomain(routing, requestHeaders, requestCookies, pathname) {
  const domains = routing.domains;
  const domain = findDomainFromHost(requestHeaders, domains);
  if (!domain) {
    return {
      locale: resolveLocaleFromPrefix(routing, requestHeaders, requestCookies, pathname)
    };
  }
  let locale;

  // Prio 1: Use route prefix
  if (pathname) {
    var _getPathnameMatch2;
    const prefixLocale = (_getPathnameMatch2 = utils.getPathnameMatch(pathname, routing.locales, routing.localePrefix)) === null || _getPathnameMatch2 === void 0 ? void 0 : _getPathnameMatch2.locale;
    if (prefixLocale) {
      if (utils.isLocaleSupportedOnDomain(prefixLocale, domain)) {
        locale = prefixLocale;
      } else {
        // Causes a redirect to a domain that supports the locale
        return {
          locale: prefixLocale,
          domain
        };
      }
    }
  }

  // Prio 2: Use existing cookie
  if (!locale && routing.localeDetection) {
    const cookieLocale = getLocaleFromCookie(routing, requestCookies);
    if (cookieLocale) {
      if (utils.isLocaleSupportedOnDomain(cookieLocale, domain)) {
        locale = cookieLocale;
      }
    }
  }

  // Prio 3: Use the `accept-language` header
  if (!locale && routing.localeDetection) {
    const headerLocale = getAcceptLanguageLocale(requestHeaders, domain.locales || routing.locales, domain.defaultLocale);
    if (headerLocale) {
      locale = headerLocale;
    }
  }

  // Prio 4: Use default locale
  if (!locale) {
    locale = domain.defaultLocale;
  }
  return {
    locale,
    domain
  };
}
function resolveLocale(routing, requestHeaders, requestCookies, pathname) {
  if (routing.domains) {
    return resolveLocaleFromDomain(routing, requestHeaders, requestCookies, pathname);
  } else {
    return {
      locale: resolveLocaleFromPrefix(routing, requestHeaders, requestCookies, pathname)
    };
  }
}

exports.default = resolveLocale;
exports.getAcceptLanguageLocale = getAcceptLanguageLocale;
