'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var useLocale = require('./react-server/useLocale.js');
var useTranslations = require('./react-server/useTranslations.js');
var useFormatter = require('./react-server/useFormatter.js');
var useNow = require('./react-server/useNow.js');
var useTimeZone = require('./react-server/useTimeZone.js');
var useMessages = require('./react-server/useMessages.js');
var NextIntlClientProviderServer = require('./react-server/NextIntlClientProviderServer.js');
var core = require('use-intl/core');



exports.useLocale = useLocale.default;
exports.useTranslations = useTranslations.default;
exports.useFormatter = useFormatter.default;
exports.useNow = useNow.default;
exports.useTimeZone = useTimeZone.default;
exports.useMessages = useMessages.default;
exports.NextIntlClientProvider = NextIntlClientProviderServer.default;
Object.keys(core).forEach(function (k) {
	if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
		enumerable: true,
		get: function () { return core[k]; }
	});
});
