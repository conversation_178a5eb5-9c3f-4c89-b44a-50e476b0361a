"use client";
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../../_virtual/_rollupPluginBabelHelpers.js');
var navigation = require('next/navigation');
var React = require('react');
var useLocale = require('../../react-client/useLocale.js');
var utils = require('../../shared/utils.js');
var BaseLink = require('./BaseLink.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var React__default = /*#__PURE__*/_interopDefault(React);

function LegacyBaseLink(_ref, ref) {
  let {
    href,
    locale,
    localeCookie,
    localePrefixMode,
    prefix,
    ...rest
  } = _ref;
  // The types aren't entirely correct here. Outside of Next.js
  // `useParams` can be called, but the return type is `null`.
  const pathname = navigation.usePathname();
  const curLocale = useLocale.default();
  const isChangingLocale = locale !== curLocale;
  const [localizedHref, setLocalizedHref] = React.useState(() => utils.isLocalizableHref(href) && (localePrefixMode !== 'never' || isChangingLocale) ?
  // For the `localePrefix: 'as-needed' strategy, the href shouldn't
  // be prefixed if the locale is the default locale. To determine this, we
  // need a) the default locale and b) the information if we use prefixed
  // routing. The default locale can vary by domain, therefore during the
  // RSC as well as the SSR render, we can't determine the default locale
  // statically. Therefore we always prefix the href since this will
  // always result in a valid URL, even if it might cause a redirect. This
  // is better than pointing to a non-localized href during the server
  // render, which would potentially be wrong. The final href is
  // determined in the effect below.
  utils.prefixHref(href, prefix) : href);
  React.useEffect(() => {
    if (!pathname) return;
    setLocalizedHref(utils.localizeHref(href, locale, curLocale, pathname, prefix));
  }, [curLocale, href, locale, pathname, prefix]);
  return /*#__PURE__*/React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({
    ref: ref,
    href: localizedHref,
    locale: locale,
    localeCookie: localeCookie
  }, rest));
}
const LegacyBaseLinkWithRef = /*#__PURE__*/React.forwardRef(LegacyBaseLink);
LegacyBaseLinkWithRef.displayName = 'ClientLink';

exports.default = LegacyBaseLinkWithRef;
