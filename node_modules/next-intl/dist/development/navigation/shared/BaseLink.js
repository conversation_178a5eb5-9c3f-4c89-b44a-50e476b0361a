"use client";
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../../_virtual/_rollupPluginBabelHelpers.js');
var NextLink = require('next/link');
var navigation = require('next/navigation');
var React = require('react');
var useLocale = require('../../react-client/useLocale.js');
var syncLocaleCookie = require('./syncLocaleCookie.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var NextLink__default = /*#__PURE__*/_interopDefault(NextLink);
var React__default = /*#__PURE__*/_interopDefault(React);

function BaseLink(_ref, ref) {
  let {
    defaultLocale,
    href,
    locale,
    localeCookie,
    onClick,
    prefetch,
    unprefixed,
    ...rest
  } = _ref;
  const curLocale = useLocale.default();
  const isChangingLocale = locale != null && locale !== curLocale;
  const linkLocale = locale || curLocale;
  const host = useHost();
  const finalHref =
  // Only after hydration (to avoid mismatches)
  host &&
  // If there is an `unprefixed` prop, the
  // `defaultLocale` might differ by domain
  unprefixed && (
  // Unprefix the pathname if a domain matches
  unprefixed.domains[host] === linkLocale ||
  // … and handle unknown domains by applying the
  // global `defaultLocale` (e.g. on localhost)
  !Object.keys(unprefixed.domains).includes(host) && curLocale === defaultLocale && !locale) ? unprefixed.pathname : href;

  // The types aren't entirely correct here. Outside of Next.js
  // `useParams` can be called, but the return type is `null`.
  const pathname = navigation.usePathname();
  function onLinkClick(event) {
    syncLocaleCookie.default(localeCookie, pathname, curLocale, locale);
    if (onClick) onClick(event);
  }
  if (isChangingLocale) {
    if (prefetch && "development" !== 'production') {
      console.error('The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`');
    }
    prefetch = false;
  }
  return /*#__PURE__*/React__default.default.createElement(NextLink__default.default, _rollupPluginBabelHelpers.extends({
    ref: ref,
    href: finalHref,
    hrefLang: isChangingLocale ? locale : undefined,
    onClick: onLinkClick,
    prefetch: prefetch
  }, rest));
}
function useHost() {
  const [host, setHost] = React.useState();
  React.useEffect(() => {
    setHost(window.location.host);
  }, []);
  return host;
}
var BaseLink$1 = /*#__PURE__*/React.forwardRef(BaseLink);

exports.default = BaseLink$1;
