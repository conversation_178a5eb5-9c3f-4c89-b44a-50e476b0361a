'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var getConfig = require('../../server/react-server/getConfig.js');

/**
 * This is only moved to a separate module for easier mocking in
 * `../createNavigatoin.test.tsx` in order to avoid suspending.
 */
async function getServerLocale() {
  const config = await getConfig.default();
  return config.locale;
}

exports.default = getServerLocale;
