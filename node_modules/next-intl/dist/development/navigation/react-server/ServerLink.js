'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../../_virtual/_rollupPluginBabelHelpers.js');
var React = require('react');
var utils = require('../../shared/utils.js');
var LegacyBaseLink = require('../shared/LegacyBaseLink.js');
var getLocale = require('../../server/react-server/getLocale.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var React__default = /*#__PURE__*/_interopDefault(React);

// Only used by legacy navigation APIs, can be removed when they are removed

async function ServerLink(_ref) {
  let {
    locale,
    localePrefix,
    ...rest
  } = _ref;
  const finalLocale = locale || (await getLocale.default());
  const prefix = utils.getLocalePrefix(finalLocale, localePrefix);
  return /*#__PURE__*/React__default.default.createElement(LegacyBaseLink.default, _rollupPluginBabelHelpers.extends({
    locale: finalLocale,
    localePrefixMode: localePrefix.mode,
    prefix: prefix
  }, rest));
}

exports.default = ServerLink;
