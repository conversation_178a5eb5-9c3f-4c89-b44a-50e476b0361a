'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var RequestLocaleLegacy = require('../../server/react-server/RequestLocaleLegacy.js');
var redirects = require('../shared/redirects.js');

function createRedirectFn(redirectFn) {
  return function serverRedirect(params) {
    const locale = RequestLocaleLegacy.getRequestLocale();
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    return redirectFn({
      ...params,
      locale
    }, ...args);
  };
}
const serverRedirect = createRedirectFn(redirects.baseRedirect);
const serverPermanentRedirect = createRedirectFn(redirects.basePermanentRedirect);

exports.serverPermanentRedirect = serverPermanentRedirect;
exports.serverRedirect = serverRedirect;
