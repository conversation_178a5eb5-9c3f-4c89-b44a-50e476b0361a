'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../../_virtual/_rollupPluginBabelHelpers.js');
var React = require('react');
var config = require('../../routing/config.js');
var ServerLink = require('./ServerLink.js');
var redirects = require('./redirects.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var React__default = /*#__PURE__*/_interopDefault(React);

function createSharedPathnamesNavigation(routing) {
  const localePrefix = config.receiveLocalePrefixConfig(routing === null || routing === void 0 ? void 0 : routing.localePrefix);
  const localeCookie = config.receiveLocaleCookie(routing === null || routing === void 0 ? void 0 : routing.localeCookie);
  function notSupported(hookName) {
    return () => {
      throw new Error("`".concat(hookName, "` is not supported in Server Components. You can use this hook if you convert the component to a Client Component."));
    };
  }
  function Link(props) {
    return /*#__PURE__*/React__default.default.createElement(ServerLink.default, _rollupPluginBabelHelpers.extends({
      localeCookie: localeCookie,
      localePrefix: localePrefix
    }, props));
  }
  function redirect(pathname) {
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    return redirects.serverRedirect({
      pathname,
      localePrefix
    }, ...args);
  }
  function permanentRedirect(pathname) {
    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
      args[_key2 - 1] = arguments[_key2];
    }
    return redirects.serverPermanentRedirect({
      pathname,
      localePrefix
    }, ...args);
  }
  return {
    Link,
    redirect,
    permanentRedirect,
    usePathname: notSupported('usePathname'),
    useRouter: notSupported('useRouter')
  };
}

exports.default = createSharedPathnamesNavigation;
