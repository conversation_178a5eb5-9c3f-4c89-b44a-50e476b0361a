'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var createSharedNavigationFns = require('../shared/createSharedNavigationFns.js');
var getServerLocale = require('./getServerLocale.js');

function createNavigation(routing) {
  function getLocale() {
    return getServerLocale.default();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const {
    config,
    ...fns
  } = createSharedNavigationFns.default(getLocale, routing);
  function notSupported(hookName) {
    return () => {
      throw new Error("`".concat(hookName, "` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component."));
    };
  }
  return {
    ...fns,
    usePathname: notSupported('usePathname'),
    useRouter: notSupported('useRouter')
  };
}

exports.default = createNavigation;
