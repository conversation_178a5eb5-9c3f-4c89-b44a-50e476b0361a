'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../../_virtual/_rollupPluginBabelHelpers.js');
var React = require('react');
var config = require('../../routing/config.js');
var RequestLocaleLegacy = require('../../server/react-server/RequestLocaleLegacy.js');
var utils = require('../shared/utils.js');
var ServerLink = require('./ServerLink.js');
var redirects = require('./redirects.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var React__default = /*#__PURE__*/_interopDefault(React);

function createLocalizedPathnamesNavigation(routing) {
  const config$1 = config.receiveRoutingConfig(routing);
  function Link(_ref) {
    let {
      href,
      locale,
      ...rest
    } = _ref;
    const defaultLocale = RequestLocaleLegacy.getRequestLocale();
    const finalLocale = locale || defaultLocale;
    return /*#__PURE__*/React__default.default.createElement(ServerLink.default, _rollupPluginBabelHelpers.extends({
      href: utils.compileLocalizedPathname({
        locale: finalLocale,
        // @ts-expect-error -- This is ok
        pathname: href,
        // @ts-expect-error -- This is ok
        params: typeof href === 'object' ? href.params : undefined,
        pathnames: config$1.pathnames
      }),
      locale: locale,
      localeCookie: config$1.localeCookie,
      localePrefix: config$1.localePrefix
    }, rest));
  }
  function redirect(href) {
    const locale = RequestLocaleLegacy.getRequestLocale();
    const pathname = getPathname({
      href,
      locale
    });
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    return redirects.serverRedirect({
      localePrefix: config$1.localePrefix,
      pathname
    }, ...args);
  }
  function permanentRedirect(href) {
    const locale = RequestLocaleLegacy.getRequestLocale();
    const pathname = getPathname({
      href,
      locale
    });
    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
      args[_key2 - 1] = arguments[_key2];
    }
    return redirects.serverPermanentRedirect({
      localePrefix: config$1.localePrefix,
      pathname
    }, ...args);
  }
  function getPathname(_ref2) {
    let {
      href,
      locale
    } = _ref2;
    return utils.compileLocalizedPathname({
      ...utils.normalizeNameOrNameWithParams(href),
      locale,
      pathnames: config$1.pathnames
    });
  }
  function notSupported(hookName) {
    return () => {
      throw new Error("`".concat(hookName, "` is not supported in Server Components. You can use this hook if you convert the component to a Client Component."));
    };
  }
  return {
    Link,
    redirect,
    permanentRedirect,
    getPathname,
    usePathname: notSupported('usePathname'),
    useRouter: notSupported('useRouter')
  };
}

exports.default = createLocalizedPathnamesNavigation;
