'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var navigation = require('next/navigation');
var React = require('react');
var useLocale = require('../../react-client/useLocale.js');
var utils = require('../../shared/utils.js');

function useBasePathname(config) {
  // The types aren't entirely correct here. Outside of Next.js
  // `useParams` can be called, but the return type is `null`.

  // Notes on `useNextPathname`:
  // - Types aren't entirely correct. Outside of Next.js the
  //   hook will return `null` (e.g. unit tests)
  // - A base path is stripped from the result
  // - Rewrites *are* taken into account (i.e. the pathname
  //   that the user sees in the browser is returned)
  const pathname = navigation.usePathname();
  const locale = useLocale.default();
  return React.useMemo(() => {
    if (!pathname) return pathname;
    let unlocalizedPathname = pathname;
    const prefix = utils.getLocalePrefix(locale, config.localePrefix);
    const isPathnamePrefixed = utils.hasPathnamePrefixed(prefix, pathname);
    if (isPathnamePrefixed) {
      unlocalizedPathname = utils.unprefixPathname(pathname, prefix);
    } else if (config.localePrefix.mode === 'as-needed' && config.localePrefix.prefixes) {
      // Workaround for https://github.com/vercel/next.js/issues/73085
      const localeAsPrefix = utils.getLocaleAsPrefix(locale);
      if (utils.hasPathnamePrefixed(localeAsPrefix, pathname)) {
        unlocalizedPathname = utils.unprefixPathname(pathname, localeAsPrefix);
      }
    }
    return unlocalizedPathname;
  }, [config.localePrefix, locale, pathname]);
}

exports.default = useBasePathname;
