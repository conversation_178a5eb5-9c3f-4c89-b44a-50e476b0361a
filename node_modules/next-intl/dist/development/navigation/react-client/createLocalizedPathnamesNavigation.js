'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../../_virtual/_rollupPluginBabelHelpers.js');
var React = require('react');
var useLocale = require('../../react-client/useLocale.js');
var config = require('../../routing/config.js');
var utils = require('../shared/utils.js');
var ClientLink = require('./ClientLink.js');
var redirects = require('./redirects.js');
var useBasePathname = require('./useBasePathname.js');
var useBaseRouter = require('./useBaseRouter.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var React__default = /*#__PURE__*/_interopDefault(React);

/**
 * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)
 **/
function createLocalizedPathnamesNavigation(routing) {
  const config$1 = config.receiveRoutingConfig(routing);
  const localeCookie = config.receiveLocaleCookie(routing.localeCookie);
  function useTypedLocale() {
    const locale = useLocale.default();
    const isValid = config$1.locales.includes(locale);
    if (!isValid) {
      throw new Error("Unknown locale encountered: \"".concat(locale, "\". Make sure to validate the locale in `i18n.ts`.") );
    }
    return locale;
  }
  function Link(_ref, ref) {
    let {
      href,
      locale,
      ...rest
    } = _ref;
    const defaultLocale = useTypedLocale();
    const finalLocale = locale || defaultLocale;
    return /*#__PURE__*/React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({
      ref: ref,
      href: utils.compileLocalizedPathname({
        locale: finalLocale,
        // @ts-expect-error -- This is ok
        pathname: href,
        // @ts-expect-error -- This is ok
        params: typeof href === 'object' ? href.params : undefined,
        pathnames: config$1.pathnames
      }),
      locale: locale,
      localeCookie: localeCookie,
      localePrefix: config$1.localePrefix
    }, rest));
  }
  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);
  LinkWithRef.displayName = 'Link';
  function redirect(href) {
    // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render
    const locale = useTypedLocale();
    const resolvedHref = getPathname({
      href,
      locale
    });
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    return redirects.clientRedirect({
      pathname: resolvedHref,
      localePrefix: config$1.localePrefix
    }, ...args);
  }
  function permanentRedirect(href) {
    // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render
    const locale = useTypedLocale();
    const resolvedHref = getPathname({
      href,
      locale
    });
    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
      args[_key2 - 1] = arguments[_key2];
    }
    return redirects.clientPermanentRedirect({
      pathname: resolvedHref,
      localePrefix: config$1.localePrefix
    }, ...args);
  }
  function useRouter() {
    const baseRouter = useBaseRouter.default(config$1.localePrefix, localeCookie);
    const defaultLocale = useTypedLocale();
    return React.useMemo(() => ({
      ...baseRouter,
      push(href) {
        var _args$;
        for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {
          args[_key3 - 1] = arguments[_key3];
        }
        const resolvedHref = getPathname({
          href,
          locale: ((_args$ = args[0]) === null || _args$ === void 0 ? void 0 : _args$.locale) || defaultLocale
        });
        return baseRouter.push(resolvedHref, ...args);
      },
      replace(href) {
        var _args$2;
        for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {
          args[_key4 - 1] = arguments[_key4];
        }
        const resolvedHref = getPathname({
          href,
          locale: ((_args$2 = args[0]) === null || _args$2 === void 0 ? void 0 : _args$2.locale) || defaultLocale
        });
        return baseRouter.replace(resolvedHref, ...args);
      },
      prefetch(href) {
        var _args$3;
        for (var _len5 = arguments.length, args = new Array(_len5 > 1 ? _len5 - 1 : 0), _key5 = 1; _key5 < _len5; _key5++) {
          args[_key5 - 1] = arguments[_key5];
        }
        const resolvedHref = getPathname({
          href,
          locale: ((_args$3 = args[0]) === null || _args$3 === void 0 ? void 0 : _args$3.locale) || defaultLocale
        });
        return baseRouter.prefetch(resolvedHref, ...args);
      }
    }), [baseRouter, defaultLocale]);
  }
  function usePathname() {
    const pathname = useBasePathname.default(config$1);
    const locale = useTypedLocale();

    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.
    return React.useMemo(() => pathname ? utils.getRoute(locale, pathname, config$1.pathnames) : pathname, [locale, pathname]);
  }
  function getPathname(_ref2) {
    let {
      href,
      locale
    } = _ref2;
    return utils.compileLocalizedPathname({
      ...utils.normalizeNameOrNameWithParams(href),
      locale,
      pathnames: config$1.pathnames
    });
  }
  return {
    Link: LinkWithRef,
    redirect,
    permanentRedirect,
    usePathname,
    useRouter,
    getPathname
  };
}

exports.default = createLocalizedPathnamesNavigation;
