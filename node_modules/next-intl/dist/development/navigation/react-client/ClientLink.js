'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../../_virtual/_rollupPluginBabelHelpers.js');
var React = require('react');
var useLocale = require('../../react-client/useLocale.js');
var utils = require('../../shared/utils.js');
var LegacyBaseLink = require('../shared/LegacyBaseLink.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var React__default = /*#__PURE__*/_interopDefault(React);

function ClientLink(_ref, ref) {
  let {
    locale,
    localePrefix,
    ...rest
  } = _ref;
  const defaultLocale = useLocale.default();
  const finalLocale = locale || defaultLocale;
  const prefix = utils.getLocalePrefix(finalLocale, localePrefix);
  return /*#__PURE__*/React__default.default.createElement(LegacyBaseLink.default, _rollupPluginBabelHelpers.extends({
    ref: ref,
    locale: finalLocale,
    localePrefixMode: localePrefix.mode,
    prefix: prefix
  }, rest));
}

/**
 * Wraps `next/link` and prefixes the `href` with the current locale if
 * necessary.
 *
 * @example
 * ```tsx
 * import {Link} from 'next-intl';
 *
 * // When the user is on `/en`, the link will point to `/en/about`
 * <Link href="/about">About</Link>
 *
 * // You can override the `locale` to switch to another language
 * <Link href="/" locale="de">Switch to German</Link>
 * ```
 *
 * Note that when a `locale` prop is passed to switch the locale, the `prefetch`
 * prop is not supported. This is because Next.js would prefetch the page and
 * the `set-cookie` response header would cause the locale cookie on the current
 * page to be overwritten before the user even decides to change the locale.
 */
const ClientLinkWithRef = /*#__PURE__*/React.forwardRef(ClientLink);
ClientLinkWithRef.displayName = 'ClientLink';

exports.default = ClientLinkWithRef;
