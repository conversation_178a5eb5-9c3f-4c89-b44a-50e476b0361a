'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../../_virtual/_rollupPluginBabelHelpers.js');
var React = require('react');
var config = require('../../routing/config.js');
var ClientLink = require('./ClientLink.js');
var redirects = require('./redirects.js');
var useBasePathname = require('./useBasePathname.js');
var useBaseRouter = require('./useBaseRouter.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var React__default = /*#__PURE__*/_interopDefault(React);

/**
 * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)
 **/
function createSharedPathnamesNavigation(routing) {
  const localePrefix = config.receiveLocalePrefixConfig(routing === null || routing === void 0 ? void 0 : routing.localePrefix);
  const localeCookie = config.receiveLocaleCookie(routing === null || routing === void 0 ? void 0 : routing.localeCookie);
  function Link(props, ref) {
    return /*#__PURE__*/React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({
      ref: ref,
      localeCookie: localeCookie,
      localePrefix: localePrefix
    }, props));
  }
  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);
  LinkWithRef.displayName = 'Link';
  function redirect(pathname) {
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    return redirects.clientRedirect({
      pathname,
      localePrefix
    }, ...args);
  }
  function permanentRedirect(pathname) {
    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
      args[_key2 - 1] = arguments[_key2];
    }
    return redirects.clientPermanentRedirect({
      pathname,
      localePrefix
    }, ...args);
  }
  function usePathname() {
    const result = useBasePathname.default({
      localePrefix,
      defaultLocale: routing === null || routing === void 0 ? void 0 : routing.defaultLocale
    });
    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.
    return result;
  }
  function useRouter() {
    return useBaseRouter.default(localePrefix, localeCookie);
  }
  return {
    Link: LinkWithRef,
    redirect,
    permanentRedirect,
    usePathname,
    useRouter
  };
}

exports.default = createSharedPathnamesNavigation;
