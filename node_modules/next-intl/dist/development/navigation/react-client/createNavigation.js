'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var navigation = require('next/navigation');
var React = require('react');
var useLocale = require('../../react-client/useLocale.js');
var createSharedNavigationFns = require('../shared/createSharedNavigationFns.js');
var syncLocaleCookie = require('../shared/syncLocaleCookie.js');
var utils = require('../shared/utils.js');
var useBasePathname = require('./useBasePathname.js');

function createNavigation(routing) {
  function useTypedLocale() {
    return useLocale.default();
  }
  const {
    Link,
    config,
    getPathname,
    ...redirects
  } = createSharedNavigationFns.default(useTypedLocale, routing);

  /** @see https://next-intl.dev/docs/routing/navigation#usepathname */
  function usePathname() {
    const pathname = useBasePathname.default(config);
    const locale = useTypedLocale();

    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.
    return React.useMemo(() => pathname &&
    // @ts-expect-error -- This is fine
    config.pathnames ? utils.getRoute(locale, pathname,
    // @ts-expect-error -- This is fine
    config.pathnames) : pathname, [locale, pathname]);
  }
  function useRouter() {
    const router = navigation.useRouter();
    const curLocale = useTypedLocale();
    const nextPathname = navigation.usePathname();
    return React.useMemo(() => {
      function createHandler(fn) {
        return function handler(href, options) {
          const {
            locale: nextLocale,
            ...rest
          } = options || {};

          // @ts-expect-error -- We're passing a domain here just in case
          const pathname = getPathname({
            href,
            locale: nextLocale || curLocale,
            domain: window.location.host
          });
          const args = [pathname];
          if (Object.keys(rest).length > 0) {
            // @ts-expect-error -- This is fine
            args.push(rest);
          }
          fn(...args);
          syncLocaleCookie.default(config.localeCookie, nextPathname, curLocale, nextLocale);
        };
      }
      return {
        ...router,
        /** @see https://next-intl.dev/docs/routing/navigation#userouter */
        push: createHandler(router.push),
        /** @see https://next-intl.dev/docs/routing/navigation#userouter */
        replace: createHandler(router.replace),
        /** @see https://next-intl.dev/docs/routing/navigation#userouter */
        prefetch: createHandler(router.prefetch)
      };
    }, [curLocale, nextPathname, router]);
  }
  return {
    ...redirects,
    Link,
    usePathname,
    useRouter,
    getPathname
  };
}

exports.default = createNavigation;
