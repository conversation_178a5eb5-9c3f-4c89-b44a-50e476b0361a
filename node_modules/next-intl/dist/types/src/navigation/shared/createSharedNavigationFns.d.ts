import React from 'react';
import { RoutingConfigLocalizedNavigation, RoutingConfigSharedNavigation } from '../../routing/config';
import { DomainsConfig, LocalePrefixMode, Locales, Pathnames } from '../../routing/types';
import { HrefOrHrefWithParams, QueryParams } from './utils';
type PromiseOrValue<Type> = Type | Promise<Type>;
/**
 * Shared implementations for `react-server` and `react-client`
 */
export default function createSharedNavigationFns<const AppLocales extends Locales, const AppPathnames extends Pathnames<AppLocales> = never, const AppLocalePrefixMode extends LocalePrefixMode = 'always', const AppDomains extends DomainsConfig<AppLocales> = never>(getLocale: () => PromiseOrValue<AppLocales extends never ? string : AppLocales[number]>, routing?: [AppPathnames] extends [never] ? RoutingConfigSharedNavigation<AppLocales, AppLocalePrefixMode, AppDomains> | undefined : RoutingConfigLocalizedNavigation<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>): {
    config: Omit<{}, "localePrefix" | "localeCookie" | "alternateLinks" | "localeDetection"> & {
        localePrefix: {
            mode: "never";
        } | {
            mode: "always";
            prefixes?: Partial<Record<string, string>> | undefined;
        } | {
            mode: "as-needed";
            prefixes?: Partial<Record<string, string>> | undefined;
        };
        localeCookie: import("../../routing/config").InitializedLocaleCookieConfig;
        localeDetection: NonNullable<boolean | undefined>;
        alternateLinks: NonNullable<boolean | undefined>;
    };
    Link: React.ForwardRefExoticComponent<Omit<{
        locale?: string | undefined;
        slot?: string | undefined;
        style?: React.CSSProperties | undefined;
        title?: string | undefined;
        onError?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        children?: React.ReactNode | undefined;
        ref?: React.LegacyRef<HTMLAnchorElement> | undefined;
        replace?: boolean | undefined;
        as?: (string | import("url").UrlObject) | undefined;
        scroll?: boolean | undefined;
        shallow?: boolean | undefined;
        passHref?: boolean | undefined;
        prefetch?: (boolean | null) | undefined;
        legacyBehavior?: boolean | undefined;
        onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onTouchStart?: React.TouchEventHandler<HTMLAnchorElement> | undefined;
        onClick?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        download?: any;
        hrefLang?: string | undefined;
        media?: string | undefined;
        ping?: string | undefined;
        target?: React.HTMLAttributeAnchorTarget | undefined;
        type?: string | undefined;
        referrerPolicy?: React.HTMLAttributeReferrerPolicy | undefined;
        defaultChecked?: boolean | undefined;
        defaultValue?: string | number | readonly string[] | undefined;
        suppressContentEditableWarning?: boolean | undefined;
        suppressHydrationWarning?: boolean | undefined;
        accessKey?: string | undefined;
        autoCapitalize?: "off" | "none" | "on" | "sentences" | "words" | "characters" | undefined | (string & {});
        autoFocus?: boolean | undefined;
        className?: string | undefined;
        contentEditable?: (boolean | "true" | "false") | "inherit" | "plaintext-only" | undefined;
        contextMenu?: string | undefined;
        dir?: string | undefined;
        draggable?: (boolean | "true" | "false") | undefined;
        enterKeyHint?: "enter" | "done" | "go" | "next" | "previous" | "search" | "send" | undefined;
        hidden?: boolean | undefined;
        id?: string | undefined;
        lang?: string | undefined;
        nonce?: string | undefined;
        spellCheck?: (boolean | "true" | "false") | undefined;
        tabIndex?: number | undefined;
        translate?: "yes" | "no" | undefined;
        radioGroup?: string | undefined;
        role?: React.AriaRole | undefined;
        about?: string | undefined;
        content?: string | undefined;
        datatype?: string | undefined;
        inlist?: any;
        prefix?: string | undefined;
        property?: string | undefined;
        rel?: string | undefined;
        resource?: string | undefined;
        rev?: string | undefined;
        typeof?: string | undefined;
        vocab?: string | undefined;
        autoCorrect?: string | undefined;
        autoSave?: string | undefined;
        color?: string | undefined;
        itemProp?: string | undefined;
        itemScope?: boolean | undefined;
        itemType?: string | undefined;
        itemID?: string | undefined;
        itemRef?: string | undefined;
        results?: number | undefined;
        security?: string | undefined;
        unselectable?: "on" | "off" | undefined;
        inputMode?: "none" | "text" | "tel" | "url" | "email" | "numeric" | "decimal" | "search" | undefined;
        is?: string | undefined;
        popover?: "" | "auto" | "manual" | undefined;
        popoverTargetAction?: "toggle" | "show" | "hide" | undefined;
        popoverTarget?: string | undefined;
        onToggle?: React.ToggleEventHandler<HTMLAnchorElement> | undefined;
        onBeforeToggle?: React.ToggleEventHandler<HTMLAnchorElement> | undefined;
        inert?: boolean | undefined;
        tw?: string | undefined;
        "aria-activedescendant"?: string | undefined;
        "aria-atomic"?: (boolean | "true" | "false") | undefined;
        "aria-autocomplete"?: "none" | "inline" | "list" | "both" | undefined;
        "aria-braillelabel"?: string | undefined;
        "aria-brailleroledescription"?: string | undefined;
        "aria-busy"?: (boolean | "true" | "false") | undefined;
        "aria-checked"?: boolean | "false" | "mixed" | "true" | undefined;
        "aria-colcount"?: number | undefined;
        "aria-colindex"?: number | undefined;
        "aria-colindextext"?: string | undefined;
        "aria-colspan"?: number | undefined;
        "aria-controls"?: string | undefined;
        "aria-current"?: boolean | "false" | "true" | "page" | "step" | "location" | "date" | "time" | undefined;
        "aria-describedby"?: string | undefined;
        "aria-description"?: string | undefined;
        "aria-details"?: string | undefined;
        "aria-disabled"?: (boolean | "true" | "false") | undefined;
        "aria-dropeffect"?: "none" | "copy" | "execute" | "link" | "move" | "popup" | undefined;
        "aria-errormessage"?: string | undefined;
        "aria-expanded"?: (boolean | "true" | "false") | undefined;
        "aria-flowto"?: string | undefined;
        "aria-grabbed"?: (boolean | "true" | "false") | undefined;
        "aria-haspopup"?: boolean | "false" | "true" | "menu" | "listbox" | "tree" | "grid" | "dialog" | undefined;
        "aria-hidden"?: (boolean | "true" | "false") | undefined;
        "aria-invalid"?: boolean | "false" | "true" | "grammar" | "spelling" | undefined;
        "aria-keyshortcuts"?: string | undefined;
        "aria-label"?: string | undefined;
        "aria-labelledby"?: string | undefined;
        "aria-level"?: number | undefined;
        "aria-live"?: "off" | "assertive" | "polite" | undefined;
        "aria-modal"?: (boolean | "true" | "false") | undefined;
        "aria-multiline"?: (boolean | "true" | "false") | undefined;
        "aria-multiselectable"?: (boolean | "true" | "false") | undefined;
        "aria-orientation"?: "horizontal" | "vertical" | undefined;
        "aria-owns"?: string | undefined;
        "aria-placeholder"?: string | undefined;
        "aria-posinset"?: number | undefined;
        "aria-pressed"?: boolean | "false" | "mixed" | "true" | undefined;
        "aria-readonly"?: (boolean | "true" | "false") | undefined;
        "aria-relevant"?: "additions" | "additions removals" | "additions text" | "all" | "removals" | "removals additions" | "removals text" | "text" | "text additions" | "text removals" | undefined;
        "aria-required"?: (boolean | "true" | "false") | undefined;
        "aria-roledescription"?: string | undefined;
        "aria-rowcount"?: number | undefined;
        "aria-rowindex"?: number | undefined;
        "aria-rowindextext"?: string | undefined;
        "aria-rowspan"?: number | undefined;
        "aria-selected"?: (boolean | "true" | "false") | undefined;
        "aria-setsize"?: number | undefined;
        "aria-sort"?: "none" | "ascending" | "descending" | "other" | undefined;
        "aria-valuemax"?: number | undefined;
        "aria-valuemin"?: number | undefined;
        "aria-valuenow"?: number | undefined;
        "aria-valuetext"?: string | undefined;
        dangerouslySetInnerHTML?: {
            __html: string | TrustedHTML;
        } | undefined;
        onCopy?: React.ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onCopyCapture?: React.ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onCut?: React.ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onCutCapture?: React.ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onPaste?: React.ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onPasteCapture?: React.ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onCompositionEnd?: React.CompositionEventHandler<HTMLAnchorElement> | undefined;
        onCompositionEndCapture?: React.CompositionEventHandler<HTMLAnchorElement> | undefined;
        onCompositionStart?: React.CompositionEventHandler<HTMLAnchorElement> | undefined;
        onCompositionStartCapture?: React.CompositionEventHandler<HTMLAnchorElement> | undefined;
        onCompositionUpdate?: React.CompositionEventHandler<HTMLAnchorElement> | undefined;
        onCompositionUpdateCapture?: React.CompositionEventHandler<HTMLAnchorElement> | undefined;
        onFocus?: React.FocusEventHandler<HTMLAnchorElement> | undefined;
        onFocusCapture?: React.FocusEventHandler<HTMLAnchorElement> | undefined;
        onBlur?: React.FocusEventHandler<HTMLAnchorElement> | undefined;
        onBlurCapture?: React.FocusEventHandler<HTMLAnchorElement> | undefined;
        onChange?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onChangeCapture?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onBeforeInput?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onBeforeInputCapture?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onInput?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onInputCapture?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onReset?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onResetCapture?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onSubmit?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onSubmitCapture?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onInvalid?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onInvalidCapture?: React.FormEventHandler<HTMLAnchorElement> | undefined;
        onLoad?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onErrorCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onKeyDown?: React.KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onKeyDownCapture?: React.KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onKeyPress?: React.KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onKeyPressCapture?: React.KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onKeyUp?: React.KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onKeyUpCapture?: React.KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onAbort?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onAbortCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onCanPlay?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onCanPlayCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onCanPlayThrough?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onCanPlayThroughCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onDurationChange?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onDurationChangeCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onEmptied?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onEmptiedCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onEncrypted?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onEncryptedCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onEnded?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onEndedCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadedData?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadedDataCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadedMetadata?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadedMetadataCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadStart?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadStartCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onPause?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onPauseCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onPlay?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onPlayCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onPlaying?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onPlayingCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onProgress?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onProgressCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onRateChange?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onRateChangeCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onResize?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onResizeCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onSeeked?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onSeekedCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onSeeking?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onSeekingCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onStalled?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onStalledCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onSuspend?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onSuspendCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onTimeUpdate?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onTimeUpdateCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onVolumeChange?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onVolumeChangeCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onWaiting?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onWaitingCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onAuxClick?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onAuxClickCapture?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onClickCapture?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onContextMenu?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onContextMenuCapture?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onDoubleClick?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onDoubleClickCapture?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onDrag?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragCapture?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragEnd?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragEndCapture?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragEnter?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragEnterCapture?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragExit?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragExitCapture?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragLeave?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragLeaveCapture?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragOver?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragOverCapture?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragStart?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDragStartCapture?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDrop?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onDropCapture?: React.DragEventHandler<HTMLAnchorElement> | undefined;
        onMouseDown?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseDownCapture?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseLeave?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseMove?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseMoveCapture?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseOut?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseOutCapture?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseOver?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseOverCapture?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseUp?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseUpCapture?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onSelect?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onSelectCapture?: React.ReactEventHandler<HTMLAnchorElement> | undefined;
        onTouchCancel?: React.TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchCancelCapture?: React.TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchEnd?: React.TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchEndCapture?: React.TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchMove?: React.TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchMoveCapture?: React.TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchStartCapture?: React.TouchEventHandler<HTMLAnchorElement> | undefined;
        onPointerDown?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerDownCapture?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerMove?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerMoveCapture?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerUp?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerUpCapture?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerCancel?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerCancelCapture?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerEnter?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerLeave?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerOver?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerOverCapture?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerOut?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerOutCapture?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onGotPointerCapture?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onGotPointerCaptureCapture?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onLostPointerCapture?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onLostPointerCaptureCapture?: React.PointerEventHandler<HTMLAnchorElement> | undefined;
        onScroll?: React.UIEventHandler<HTMLAnchorElement> | undefined;
        onScrollCapture?: React.UIEventHandler<HTMLAnchorElement> | undefined;
        onWheel?: React.WheelEventHandler<HTMLAnchorElement> | undefined;
        onWheelCapture?: React.WheelEventHandler<HTMLAnchorElement> | undefined;
        onAnimationStart?: React.AnimationEventHandler<HTMLAnchorElement> | undefined;
        onAnimationStartCapture?: React.AnimationEventHandler<HTMLAnchorElement> | undefined;
        onAnimationEnd?: React.AnimationEventHandler<HTMLAnchorElement> | undefined;
        onAnimationEndCapture?: React.AnimationEventHandler<HTMLAnchorElement> | undefined;
        onAnimationIteration?: React.AnimationEventHandler<HTMLAnchorElement> | undefined;
        onAnimationIterationCapture?: React.AnimationEventHandler<HTMLAnchorElement> | undefined;
        onTransitionEnd?: React.TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionEndCapture?: React.TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionCancel?: React.TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionCancelCapture?: React.TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionRun?: React.TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionRunCapture?: React.TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionStart?: React.TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionStartCapture?: React.TransitionEventHandler<HTMLAnchorElement> | undefined;
        key?: React.Key | null | undefined;
        href: [AppPathnames] extends [never] ? string | import("url").UrlObject : keyof AppPathnames extends infer T ? T extends keyof AppPathnames ? T extends `${string}[[...${string}` ? T | ({
            pathname: T;
            params?: import("./StrictParams").default<T> | undefined;
        } & Omit<import("url").UrlObject, "pathname">) : T extends `${string}[${string}` ? {
            pathname: T;
            params: import("./StrictParams").default<T>;
        } & Omit<import("url").UrlObject, "pathname"> : T | ({
            pathname: T;
        } & Omit<import("url").UrlObject, "pathname">) : never : never;
    }, "ref"> & React.RefAttributes<HTMLAnchorElement>>;
    redirect: (args: Omit<Parameters<(args: {
        /** @see https://next-intl.dev/docs/routing/navigation#getpathname */
        href: [AppPathnames] extends [never] ? string | {
            pathname: string;
            query?: QueryParams;
        } : HrefOrHrefWithParams<keyof AppPathnames>;
        locale: string;
    } & (([AppPathnames] extends [never] ? RoutingConfigSharedNavigation<AppLocales, AppLocalePrefixMode, AppDomains> | undefined : RoutingConfigLocalizedNavigation<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>) | undefined extends undefined ? {} : AppLocalePrefixMode extends "as-needed" ? [AppDomains] extends [never] ? {} : {
        /**
         * In case you're using `localePrefix: 'as-needed'` in combination with `domains`, the `defaultLocale` can differ by domain and therefore the locales that need to be prefixed can differ as well. For this particular case, this parameter should be provided in order to compute the correct pathname. Note that the actual domain is not part of the result, but only the pathname is returned.
         * @see https://next-intl.dev/docs/routing/navigation#getpathname
         */
        domain: AppDomains[number]["domain"];
    } : {}), _forcePrefix?: boolean) => string>[0], "domain"> & Partial<([AppPathnames] extends [never] ? RoutingConfigSharedNavigation<AppLocales, AppLocalePrefixMode, AppDomains> | undefined : RoutingConfigLocalizedNavigation<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>) | undefined extends undefined ? {} : AppLocalePrefixMode extends "as-needed" ? [AppDomains] extends [never] ? {} : {
        /**
         * In case you're using `localePrefix: 'as-needed'` in combination with `domains`, the `defaultLocale` can differ by domain and therefore the locales that need to be prefixed can differ as well. For this particular case, this parameter should be provided in order to compute the correct pathname. Note that the actual domain is not part of the result, but only the pathname is returned.
         * @see https://next-intl.dev/docs/routing/navigation#getpathname
         */
        domain: AppDomains[number]["domain"];
    } : {}>, type?: import("next/navigation").RedirectType | undefined) => never;
    permanentRedirect: (args: Omit<Parameters<(args: {
        /** @see https://next-intl.dev/docs/routing/navigation#getpathname */
        href: [AppPathnames] extends [never] ? string | {
            pathname: string;
            query?: QueryParams;
        } : HrefOrHrefWithParams<keyof AppPathnames>;
        locale: string;
    } & (([AppPathnames] extends [never] ? RoutingConfigSharedNavigation<AppLocales, AppLocalePrefixMode, AppDomains> | undefined : RoutingConfigLocalizedNavigation<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>) | undefined extends undefined ? {} : AppLocalePrefixMode extends "as-needed" ? [AppDomains] extends [never] ? {} : {
        /**
         * In case you're using `localePrefix: 'as-needed'` in combination with `domains`, the `defaultLocale` can differ by domain and therefore the locales that need to be prefixed can differ as well. For this particular case, this parameter should be provided in order to compute the correct pathname. Note that the actual domain is not part of the result, but only the pathname is returned.
         * @see https://next-intl.dev/docs/routing/navigation#getpathname
         */
        domain: AppDomains[number]["domain"];
    } : {}), _forcePrefix?: boolean) => string>[0], "domain"> & Partial<([AppPathnames] extends [never] ? RoutingConfigSharedNavigation<AppLocales, AppLocalePrefixMode, AppDomains> | undefined : RoutingConfigLocalizedNavigation<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>) | undefined extends undefined ? {} : AppLocalePrefixMode extends "as-needed" ? [AppDomains] extends [never] ? {} : {
        /**
         * In case you're using `localePrefix: 'as-needed'` in combination with `domains`, the `defaultLocale` can differ by domain and therefore the locales that need to be prefixed can differ as well. For this particular case, this parameter should be provided in order to compute the correct pathname. Note that the actual domain is not part of the result, but only the pathname is returned.
         * @see https://next-intl.dev/docs/routing/navigation#getpathname
         */
        domain: AppDomains[number]["domain"];
    } : {}>, type?: import("next/navigation").RedirectType | undefined) => never;
    getPathname: (args: Parameters<(args: {
        /** @see https://next-intl.dev/docs/routing/navigation#getpathname */
        href: [AppPathnames] extends [never] ? string | {
            pathname: string;
            query?: QueryParams;
        } : HrefOrHrefWithParams<keyof AppPathnames>;
        locale: string;
    } & (([AppPathnames] extends [never] ? RoutingConfigSharedNavigation<AppLocales, AppLocalePrefixMode, AppDomains> | undefined : RoutingConfigLocalizedNavigation<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>) | undefined extends undefined ? {} : AppLocalePrefixMode extends "as-needed" ? [AppDomains] extends [never] ? {} : {
        /**
         * In case you're using `localePrefix: 'as-needed'` in combination with `domains`, the `defaultLocale` can differ by domain and therefore the locales that need to be prefixed can differ as well. For this particular case, this parameter should be provided in order to compute the correct pathname. Note that the actual domain is not part of the result, but only the pathname is returned.
         * @see https://next-intl.dev/docs/routing/navigation#getpathname
         */
        domain: AppDomains[number]["domain"];
    } : {}), _forcePrefix?: boolean) => string>[0]) => string;
};
export {};
