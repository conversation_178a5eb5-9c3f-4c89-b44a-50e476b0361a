import type { ParsedUrlQueryInput } from 'node:querystring';
import type { UrlObject } from 'url';
import { ResolvedRoutingConfig } from '../../routing/config';
import { DomainsConfig, LocalePrefixMode, Locales, Pathnames } from '../../routing/types';
import StrictParams from './StrictParams';
type SearchParamValue = ParsedUrlQueryInput[keyof ParsedUrlQueryInput];
type HrefOrHrefWithParamsImpl<Pathname, Other> = Pathname extends `${string}[[...${string}` ? // Optional catch-all
Pathname | ({
    pathname: Pathname;
    params?: StrictParams<Pathname>;
} & Other) : Pathname extends `${string}[${string}` ? // Required catch-all & regular params
{
    pathname: Pathname;
    params: StrictParams<Pathname>;
} & Other : // No params
Pathname | ({
    pathname: Pathname;
} & Other);
export type HrefOrUrlObjectWithParams<Pathname> = HrefOrHrefWithParamsImpl<Pathname, Omit<UrlObject, 'pathname'>>;
export type QueryParams = Record<string, SearchParamValue>;
export type HrefOrHrefWithParams<Pathname> = HrefOrHrefWithParamsImpl<Pathname, {
    query?: QueryParams;
}>;
export declare function normalizeNameOrNameWithParams<Pathname>(href: HrefOrHrefWithParams<Pathname> | {
    locale: string;
    href: HrefOrHrefWithParams<Pathname>;
}): {
    pathname: Pathname;
    params?: StrictParams<Pathname>;
};
export declare function serializeSearchParams(searchParams: Record<string, SearchParamValue>): string;
type StrictUrlObject<Pathname> = Omit<UrlObject, 'pathname'> & {
    pathname: Pathname;
};
export declare function compileLocalizedPathname<AppLocales extends Locales, Pathname>(opts: {
    locale: AppLocales[number];
    pathname: Pathname;
    params?: StrictParams<Pathname>;
    pathnames: Pathnames<AppLocales>;
    query?: Record<string, SearchParamValue>;
}): string;
export declare function compileLocalizedPathname<AppLocales extends Locales, Pathname>(opts: {
    locale: AppLocales[number];
    pathname: StrictUrlObject<Pathname>;
    params?: StrictParams<Pathname>;
    pathnames: Pathnames<AppLocales>;
    query?: Record<string, SearchParamValue>;
}): UrlObject;
export declare function getRoute<AppLocales extends Locales>(locale: AppLocales[number], pathname: string, pathnames: Pathnames<AppLocales>): keyof Pathnames<AppLocales>;
export declare function getBasePath(pathname: string, windowPathname?: string): string;
export declare function applyPathnamePrefix<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode, AppPathnames extends Pathnames<AppLocales> | undefined, AppDomains extends DomainsConfig<AppLocales> | undefined>(pathname: string, locale: Locales[number], routing: Pick<ResolvedRoutingConfig<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>, 'localePrefix' | 'domains'> & Partial<Pick<ResolvedRoutingConfig<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>, 'defaultLocale'>>, domain?: string, force?: boolean): string;
export declare function validateReceivedConfig<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode, AppPathnames extends Pathnames<AppLocales> | undefined, AppDomains extends DomainsConfig<AppLocales> | undefined>(config: Partial<Pick<ResolvedRoutingConfig<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>, 'defaultLocale' | 'localePrefix'>>): void;
export {};
