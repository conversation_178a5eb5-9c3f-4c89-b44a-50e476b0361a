import NextLink from 'next/link';
import React, { ComponentProps } from 'react';
import { InitializedLocaleCookieConfig } from '../../routing/config';
type Props = Omit<ComponentProps<typeof NextLink>, 'locale'> & {
    locale?: string;
    defaultLocale?: string;
    localeCookie: InitializedLocaleCookieConfig;
    /** Special case for `localePrefix: 'as-needed'` and `domains`. */
    unprefixed?: {
        domains: {
            [domain: string]: string;
        };
        pathname: string;
    };
};
declare const _default: React.ForwardRefExoticComponent<Omit<Props, "ref"> & React.RefAttributes<HTMLAnchorElement>>;
export default _default;
