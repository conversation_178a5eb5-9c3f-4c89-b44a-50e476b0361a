import NextLink from 'next/link';
import React, { ComponentProps } from 'react';
import { InitializedLocaleCookieConfig } from '../../routing/config';
import { LocalePrefixMode } from '../../routing/types';
type Props = Omit<ComponentProps<typeof NextLink>, 'locale'> & {
    locale: string;
    prefix: string;
    localePrefixMode: LocalePrefixMode;
    localeCookie: InitializedLocaleCookieConfig;
};
declare const LegacyBaseLinkWithRef: React.ForwardRefExoticComponent<Omit<Props, "ref"> & React.RefAttributes<HTMLAnchorElement>>;
export default LegacyBaseLinkWithRef;
