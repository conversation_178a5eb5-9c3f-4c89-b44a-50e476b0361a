import { LocalePrefixConfigVerbose, LocalePrefixMode, Locales } from '../../routing/types';
export declare const baseRedirect: <AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode>(params: {
    pathname: string;
    locale: Locales[number];
    localePrefix: LocalePrefixConfigVerbose<AppLocales, AppLocalePrefixMode>;
}, type?: import("next/navigation").RedirectType | undefined) => never;
export declare const basePermanentRedirect: <AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode>(params: {
    pathname: string;
    locale: Locales[number];
    localePrefix: LocalePrefixConfigVerbose<AppLocales, AppLocalePrefixMode>;
}, type?: import("next/navigation").RedirectType | undefined) => never;
