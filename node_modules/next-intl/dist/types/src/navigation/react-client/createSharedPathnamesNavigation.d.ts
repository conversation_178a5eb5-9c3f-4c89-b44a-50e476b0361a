import React, { ReactElement } from 'react';
import { RoutingConfigSharedNavigation } from '../../routing/config';
import { DomainsConfig, LocalePrefixMode, Locales } from '../../routing/types';
/**
 * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)
 **/
export default function createSharedPathnamesNavigation<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode, AppDomains extends DomainsConfig<AppLocales> = never>(routing?: RoutingConfigSharedNavigation<AppLocales, AppLocalePrefixMode, AppDomains>): {
    Link: (props: Omit<Omit<Omit<Omit<Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof {
        href: string | import("url").UrlObject;
        as?: string | import("url").UrlObject;
        replace?: boolean;
        scroll?: boolean;
        shallow?: boolean;
        passHref?: boolean;
        prefetch?: boolean | null;
        locale?: string | false;
        legacyBehavior?: boolean;
        onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>;
        onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>;
        onClick?: React.MouseEventHandler<HTMLAnchorElement>;
    }> & {
        href: string | import("url").UrlObject;
        as?: string | import("url").UrlObject;
        replace?: boolean;
        scroll?: boolean;
        shallow?: boolean;
        passHref?: boolean;
        prefetch?: boolean | null;
        locale?: string | false;
        legacyBehavior?: boolean;
        onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>;
        onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>;
        onClick?: React.MouseEventHandler<HTMLAnchorElement>;
    } & {
        children?: React.ReactNode;
    } & React.RefAttributes<HTMLAnchorElement>, "locale"> & {
        locale: string;
        prefix: string;
        localePrefixMode: LocalePrefixMode;
        localeCookie: import("../../routing/config").InitializedLocaleCookieConfig;
    }, "ref"> & React.RefAttributes<HTMLAnchorElement>, "locale" | "prefix" | "localePrefixMode"> & {
        locale?: AppLocales[number] | undefined;
        localePrefix: import("../../routing/types").LocalePrefixConfigVerbose<AppLocales, AppLocalePrefixMode>;
    } & {
        ref?: React.LegacyRef<HTMLAnchorElement> | undefined;
    }, "localePrefix" | "localeCookie"> & {
        ref?: React.LegacyRef<HTMLAnchorElement> | undefined;
    }) => ReactElement;
    redirect: (pathname: string, type?: import("next/navigation").RedirectType | undefined) => never;
    permanentRedirect: (pathname: string, type?: import("next/navigation").RedirectType | undefined) => never;
    usePathname: () => string;
    useRouter: () => {
        push: (href: string, options?: (import("next/dist/shared/lib/app-router-context.shared-runtime").NavigateOptions & {
            locale?: AppLocales[number] | undefined;
        }) | undefined) => void;
        replace: (href: string, options?: (import("next/dist/shared/lib/app-router-context.shared-runtime").NavigateOptions & {
            locale?: AppLocales[number] | undefined;
        }) | undefined) => void;
        prefetch: (href: string, options?: (import("next/dist/shared/lib/app-router-context.shared-runtime").PrefetchOptions & {
            locale?: AppLocales[number] | undefined;
        }) | undefined) => void;
        back(): void;
        forward(): void;
        refresh(): void;
    };
};
