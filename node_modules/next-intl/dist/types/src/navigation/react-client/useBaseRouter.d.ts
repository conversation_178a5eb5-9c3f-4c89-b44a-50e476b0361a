import { InitializedLocaleCookieConfig } from '../../routing/config';
import { LocalePrefixConfigVerbose, LocalePrefixMode, Locales } from '../../routing/types';
type IntlNavigateOptions<AppLocales extends Locales> = {
    locale?: AppLocales[number];
};
/**
 * Returns a wrapped instance of `useRouter` from `next/navigation` that
 * will automatically localize the `href` parameters it receives.
 *
 * @example
 * ```tsx
 * 'use client';
 *
 * import {useRouter} from 'next-intl/client';
 *
 * const router = useRouter();
 *
 * // When the user is on `/en`, the router will navigate to `/en/about`
 * router.push('/about');
 *
 * // Optionally, you can switch the locale by passing the second argument
 * router.push('/about', {locale: 'de'});
 * ```
 */
export default function useBaseRouter<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode>(localePrefix: LocalePrefixConfigVerbose<AppLocales, AppLocalePrefixMode>, localeCookie: InitializedLocaleCookieConfig): {
    push: (href: string, options?: (import("next/dist/shared/lib/app-router-context.shared-runtime").NavigateOptions & IntlNavigateOptions<AppLocales>) | undefined) => void;
    replace: (href: string, options?: (import("next/dist/shared/lib/app-router-context.shared-runtime").NavigateOptions & IntlNavigateOptions<AppLocales>) | undefined) => void;
    prefetch: (href: string, options?: (import("next/dist/shared/lib/app-router-context.shared-runtime").PrefetchOptions & IntlNavigateOptions<AppLocales>) | undefined) => void;
    back(): void;
    forward(): void;
    refresh(): void;
};
export {};
