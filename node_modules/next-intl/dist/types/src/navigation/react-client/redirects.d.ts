export declare const clientRedirect: (params: Omit<Parameters<(<AppLocales extends import("../../routing/types").Locales, AppLocalePrefixMode extends import("../../routing").LocalePrefixMode>(params: {
    pathname: string;
    locale: import("../../routing/types").Locales[number];
    localePrefix: import("../../routing/types").LocalePrefixConfigVerbose<AppLocales, AppLocalePrefixMode>;
}, type?: import("next/navigation").RedirectType | undefined) => never)>[0], "locale">, type?: import("next/navigation").RedirectType | undefined) => never;
export declare const clientPermanentRedirect: (params: Omit<Parameters<(<AppLocales extends import("../../routing/types").Locales, AppLocalePrefixMode extends import("../../routing").LocalePrefixMode>(params: {
    pathname: string;
    locale: import("../../routing/types").Locales[number];
    localePrefix: import("../../routing/types").LocalePrefixConfigVerbose<AppLocales, AppLocalePrefixMode>;
}, type?: import("next/navigation").RedirectType | undefined) => never)>[0], "locale">, type?: import("next/navigation").RedirectType | undefined) => never;
