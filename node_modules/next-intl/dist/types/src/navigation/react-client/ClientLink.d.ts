import { ComponentProps, ReactElement } from 'react';
import { LocalePrefixConfigVerbose, LocalePrefixMode, Locales } from '../../routing/types';
import LegacyBaseLink from '../shared/LegacyBaseLink';
type Props<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode> = Omit<ComponentProps<typeof LegacyBaseLink>, 'locale' | 'prefix' | 'localePrefixMode'> & {
    locale?: AppLocales[number];
    localePrefix: LocalePrefixConfigVerbose<AppLocales, AppLocalePrefixMode>;
};
/**
 * Wraps `next/link` and prefixes the `href` with the current locale if
 * necessary.
 *
 * @example
 * ```tsx
 * import {Link} from 'next-intl';
 *
 * // When the user is on `/en`, the link will point to `/en/about`
 * <Link href="/about">About</Link>
 *
 * // You can override the `locale` to switch to another language
 * <Link href="/" locale="de">Switch to German</Link>
 * ```
 *
 * Note that when a `locale` prop is passed to switch the locale, the `prefetch`
 * prop is not supported. This is because Next.js would prefetch the page and
 * the `set-cookie` response header would cause the locale cookie on the current
 * page to be overwritten before the user even decides to change the locale.
 */
declare const ClientLinkWithRef: <AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode>(props: Props<AppLocales, AppLocalePrefixMode> & {
    ref?: Props<AppLocales, AppLocalePrefixMode>["ref"];
}) => ReactElement;
export default ClientLinkWithRef;
