import React, { ComponentProps } from 'react';
import { RoutingConfigSharedNavigation } from '../../routing/config';
import { DomainsConfig, LocalePrefixMode, Locales } from '../../routing/types';
import ServerLink from './ServerLink';
export default function createSharedPathnamesNavigation<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode, AppDomains extends DomainsConfig<AppLocales> = never>(routing?: RoutingConfigSharedNavigation<AppLocales, AppLocalePrefixMode, AppDomains>): {
    Link: (props: Omit<ComponentProps<typeof ServerLink<AppLocales, AppLocalePrefixMode>>, "localePrefix" | "localeCookie">) => React.JSX.Element;
    redirect: (pathname: string, type?: import("next/navigation").RedirectType | undefined) => never;
    permanentRedirect: (pathname: string, type?: import("next/navigation").RedirectType | undefined) => never;
    usePathname: () => never;
    useRouter: () => never;
};
