import React, { ComponentProps } from 'react';
import { LocalePrefixConfigVerbose, LocalePrefixMode, Locales } from '../../routing/types';
import LegacyBaseLink from '../shared/LegacyBaseLink';
type Props<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode> = Omit<ComponentProps<typeof LegacyBaseLink>, 'locale' | 'prefix' | 'localePrefixMode'> & {
    locale?: AppLocales[number];
    localePrefix: LocalePrefixConfigVerbose<AppLocales, AppLocalePrefixMode>;
};
export default function ServerLink<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode>({ locale, localePrefix, ...rest }: Props<AppLocales, AppLocalePrefixMode>): Promise<React.JSX.Element>;
export {};
