import { ReactNode } from 'react';
import { Formats, MarkupTranslationValues, MessageKeys, NamespaceKeys, NestedKeyOf, NestedValueOf, RichTranslationValues, TranslationValues, createTranslator } from 'use-intl/core';
declare function getTranslatorImpl<NestedKey extends NamespaceKeys<IntlMessages, NestedKeyOf<IntlMessages>> = never>(config: Parameters<typeof createTranslator>[0], namespace?: NestedKey): {
    <TargetKey extends MessageKeys<NestedValueOf<{
        '!': IntlMessages;
    }, [
        NestedKey
    ] extends [never] ? '!' : `!.${NestedKey}`>, NestedKeyOf<NestedValueOf<{
        '!': IntlMessages;
    }, [
        NestedKey
    ] extends [never] ? '!' : `!.${NestedKey}`>>>>(key: TargetKey, values?: TranslationValues, formats?: Formats): string;
    rich<TargetKey extends MessageKeys<NestedValueOf<{
        '!': IntlMessages;
    }, [
        NestedKey
    ] extends [never] ? '!' : `!.${NestedKey}`>, NestedKeyOf<NestedValueOf<{
        '!': IntlMessages;
    }, [
        NestedKey
    ] extends [never] ? '!' : `!.${NestedKey}`>>>>(key: TargetKey, values?: RichTranslationValues, formats?: Formats): ReactNode;
    markup<TargetKey extends MessageKeys<NestedValueOf<{
        '!': IntlMessages;
    }, [
        NestedKey
    ] extends [never] ? '!' : `!.${NestedKey}`>, NestedKeyOf<NestedValueOf<{
        '!': IntlMessages;
    }, [
        NestedKey
    ] extends [never] ? '!' : `!.${NestedKey}`>>>>(key: TargetKey, values?: MarkupTranslationValues, formats?: Formats): string;
    raw<TargetKey extends MessageKeys<NestedValueOf<{
        '!': IntlMessages;
    }, [
        NestedKey
    ] extends [never] ? '!' : `!.${NestedKey}`>, NestedKeyOf<NestedValueOf<{
        '!': IntlMessages;
    }, [
        NestedKey
    ] extends [never] ? '!' : `!.${NestedKey}`>>>>(key: TargetKey): any;
    has<TargetKey extends MessageKeys<NestedValueOf<{
        '!': IntlMessages;
    }, [
        NestedKey
    ] extends [never] ? '!' : `!.${NestedKey}`>, NestedKeyOf<NestedValueOf<{
        '!': IntlMessages;
    }, [
        NestedKey
    ] extends [never] ? '!' : `!.${NestedKey}`>>>>(key: TargetKey): boolean;
};
declare const _default: typeof getTranslatorImpl;
export default _default;
