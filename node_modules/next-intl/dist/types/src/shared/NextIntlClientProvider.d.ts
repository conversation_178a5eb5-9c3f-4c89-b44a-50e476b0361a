import React, { ComponentProps } from 'react';
import { IntlProvider } from 'use-intl/_IntlProvider';
type Props = Omit<ComponentProps<typeof IntlProvider>, 'locale'> & {
    /** This is automatically received when being rendered from a Server Component. In all other cases, e.g. when rendered from a Client Component, a unit test or with the Pages Router, you can pass this prop explicitly. */
    locale?: string;
};
export default function NextIntlClientProvider({ locale, ...rest }: Props): React.JSX.Element;
export {};
