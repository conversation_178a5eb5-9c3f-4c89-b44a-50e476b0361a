import { NextRequest } from 'next/server';
import { ResolvedRoutingConfig } from '../routing/config';
import { DomainsConfig, LocalePrefixMode, Locales, Pathnames } from '../routing/types';
/**
 * See https://developers.google.com/search/docs/specialty/international/localized-versions
 */
export default function getAlternateLinksHeaderValue<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode, AppPathnames extends Pathnames<AppLocales> | undefined, AppD<PERSON>ins extends DomainsConfig<AppLocales> | undefined>({ localizedPathnames, request, resolvedLocale, routing }: {
    routing: Omit<ResolvedRoutingConfig<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>, 'pathnames'>;
    request: NextRequest;
    resolvedLocale: AppLocales[number];
    localizedPathnames?: Pathnames<AppLocales>[string];
}): string;
