import { NextRequest, NextResponse } from 'next/server';
import { RoutingConfig } from '../routing/config';
import { DomainsConfig, LocalePrefixMode, Locales, Pathnames } from '../routing/types';
export default function createMiddleware<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode = 'always', AppPath<PERSON>s extends Pathnames<AppLocales> = never, AppD<PERSON>ins extends DomainsConfig<AppLocales> = never>(routing: RoutingConfig<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>, 
/** @deprecated Should be passed via the first parameter `routing` instead (ideally defined with `defineRouting`) */
options?: {
    /** @deprecated Should be passed via the first parameter `routing` instead (ideally defined with `defineRouting`) */
    localeCookie?: RoutingConfig<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>['localeCookie'];
    /** @deprecated Should be passed via the first parameter `routing` instead (ideally defined with `defineRouting`) */
    localeDetection?: RoutingConfig<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>['localeDetection'];
    /** @deprecated Should be passed via the first parameter `routing` instead (ideally defined with `defineRouting`) */
    alternateLinks?: RoutingConfig<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>['alternateLinks'];
}): (request: NextRequest) => NextResponse<unknown>;
