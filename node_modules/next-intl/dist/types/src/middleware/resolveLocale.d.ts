import { RequestCookies } from 'next/dist/server/web/spec-extension/cookies';
import { ResolvedRoutingConfig } from '../routing/config';
import { DomainConfig, DomainsConfig, LocalePrefixMode, Locales, Pathnames } from '../routing/types';
export declare function getAcceptLanguageLocale<AppLocales extends Locales>(requestHeaders: Headers, locales: AppLocales, defaultLocale: string): string | undefined;
export default function resolveLocale<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode, AppPathnames extends Pathnames<AppLocales> | undefined, AppDomains extends DomainsConfig<AppLocales> | undefined>(routing: Omit<ResolvedRoutingConfig<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>, 'pathnames'>, requestHeaders: Headers, requestCookies: RequestCookies, pathname: string): {
    locale: App<PERSON><PERSON>ales[number];
    domain?: DomainConfig<AppLocales>;
};
