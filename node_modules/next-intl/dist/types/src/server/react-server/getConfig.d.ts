import { IntlConfig, _createIntlFormatters } from 'use-intl/core';
declare function getConfigImpl(localeOverride?: string): Promise<IntlConfig & {
    getMessageFallback: NonNullable<IntlConfig['getMessageFallback']>;
    now: NonNullable<IntlConfig['now']>;
    onError: NonNullable<IntlConfig['onError']>;
    timeZone: NonNullable<IntlConfig['timeZone']>;
    _formatters: ReturnType<typeof _createIntlFormatters>;
}>;
declare const getConfig: typeof getConfigImpl;
export default getConfig;
