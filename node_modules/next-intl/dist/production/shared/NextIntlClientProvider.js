"use client";
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../_virtual/_rollupPluginBabelHelpers.js"),r=require("react"),t=require("use-intl/_IntlProvider");function l(e){return e&&e.__esModule?e:{default:e}}var u=l(r);exports.default=function(r){let{locale:l,...o}=r;if(!l)throw new Error(void 0);return u.default.createElement(t.IntlProvider,e.extends({locale:l},o))};
