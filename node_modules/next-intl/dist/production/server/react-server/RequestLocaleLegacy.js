"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("next/headers"),t=require("next/navigation"),r=require("react"),n=require("../../shared/constants.js"),s=require("./RequestLocaleCache.js");const i=r.cache((function(){let r;try{r=e.headers().get(n.HEADER_LOCALE_NAME)}catch(e){throw e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest?new Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e}):e}return r||t.notFound(),r}));exports.getRequestLocale=function(){return s.getCachedRequestLocale()||i()};
