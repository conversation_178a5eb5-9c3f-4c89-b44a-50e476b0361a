"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("./getConfig.js");function r(e){if(!e.messages)throw new Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return e.messages}const s=e.cache((async function(e){return r(await t.default(e))}));exports.default=async function(e){return s(null==e?void 0:e.locale)},exports.getMessagesFromConfig=r;
