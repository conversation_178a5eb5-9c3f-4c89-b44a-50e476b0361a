"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../shared/utils.js");function t(e){function t(e){return String(e)}const r=new URLSearchParams;for(const[a,n]of Object.entries(e))Array.isArray(n)?n.forEach((e=>{r.append(a,t(e))})):r.set(a,t(n));return"?"+r.toString()}exports.applyPathnamePrefix=function(t,r,a,n,o){const{mode:i}=a.localePrefix;let s;if(void 0!==o)s=o;else if(e.isLocalizableHref(t))if("always"===i)s=!0;else if("as-needed"===i){let e=a.defaultLocale;if(a.domains){const t=a.domains.find((e=>e.domain===n));t&&(e=t.defaultLocale)}s=e!==r}return s?e.prefixPathname(e.getLocalePrefix(r,a.localePrefix),t):t},exports.compileLocalizedPathname=function(r){let{pathname:a,locale:n,params:o,pathnames:i,query:s}=r;function c(e){let t=i[e];return t||(t=e),t}function l(r){let a="string"==typeof r?r:r[n];return o&&Object.entries(o).forEach((e=>{let t,r,[n,o]=e;Array.isArray(o)?(t="(\\[)?\\[...".concat(n,"\\](\\])?"),r=o.map((e=>String(e))).join("/")):(t="\\[".concat(n,"\\]"),r=String(o)),a=a.replace(new RegExp(t,"g"),r)})),a=a.replace(/\[\[\.\.\..+\]\]/g,""),a=e.normalizeTrailingSlash(a),s&&(a+=t(s)),a}if("string"==typeof a){return l(c(a))}{const{pathname:e,...t}=a;return{...t,pathname:l(c(e))}}},exports.getBasePath=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")},exports.getRoute=function(t,r,a){const n=e.getSortedPathnames(Object.keys(a)),o=decodeURI(r);for(const r of n){const n=a[r];if("string"==typeof n){const t=n;if(e.matchesPathname(t,o))return r}else if(e.matchesPathname(n[t],o))return r}return r},exports.normalizeNameOrNameWithParams=function(e){return"string"==typeof e?{pathname:e}:e},exports.serializeSearchParams=t;
