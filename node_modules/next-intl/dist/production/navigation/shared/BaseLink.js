"use client";
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../_virtual/_rollupPluginBabelHelpers.js"),t=require("next/link"),r=require("next/navigation"),n=require("react"),u=require("../../react-client/useLocale.js"),a=require("./syncLocaleCookie.js");function l(e){return e&&e.__esModule?e:{default:e}}var o=l(t),i=l(n);function c(t,l){let{defaultLocale:c,href:f,locale:s,localeCookie:d,onClick:p,prefetch:h,unprefixed:v,...k}=t;const q=u.default(),x=null!=s&&s!==q,_=s||q,j=function(){const[e,t]=n.useState();return n.useEffect((()=>{t(window.location.host)}),[]),e}(),m=j&&v&&(v.domains[j]===_||!Object.keys(v.domains).includes(j)&&q===c&&!s)?v.pathname:f,C=r.usePathname();return x&&(h=!1),i.default.createElement(o.default,e.extends({ref:l,href:m,hrefLang:x?s:void 0,onClick:function(e){a.default(d,C,q,s),p&&p(e)},prefetch:h},k))}var f=n.forwardRef(c);exports.default=f;
