"use client";
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../_virtual/_rollupPluginBabelHelpers.js"),r=require("next/navigation"),l=require("react"),t=require("../../react-client/useLocale.js"),a=require("../../shared/utils.js"),i=require("./BaseLink.js");function u(e){return e&&e.__esModule?e:{default:e}}var o=u(l);function s(u,s){let{href:f,locale:n,localeCookie:c,localePrefixMode:d,prefix:p,...x}=u;const q=r.usePathname(),v=t.default(),_=n!==v,[j,h]=l.useState((()=>a.isLocalizableHref(f)&&("never"!==d||_)?a.prefixHref(f,p):f));return l.useEffect((()=>{q&&h(a.localizeHref(f,n,v,q,p))}),[v,f,n,q,p]),o.default.createElement(i.default,e.extends({ref:s,href:j,locale:n,localeCookie:c},x))}const f=l.forwardRef(s);f.displayName="ClientLink",exports.default=f;
