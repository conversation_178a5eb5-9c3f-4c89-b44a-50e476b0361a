"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("next/navigation"),t=require("react"),r=require("../../react-client/useLocale.js"),n=require("../shared/createSharedNavigationFns.js"),a=require("../shared/syncLocaleCookie.js"),u=require("../shared/utils.js"),o=require("./useBasePathname.js");exports.default=function(s){function c(){return r.default()}const{Link:i,config:l,getPathname:h,...f}=n.default(c,s);return{...f,Link:i,usePathname:function(){const e=o.default(l),r=c();return t.useMemo((()=>e&&l.pathnames?u.getRoute(r,e,l.pathnames):e),[r,e])},useRouter:function(){const r=e.useRouter(),n=c(),u=e.usePathname();return t.useMemo((()=>{function e(e){return function(t,r){const{locale:o,...s}=r||{},c=[h({href:t,locale:o||n,domain:window.location.host})];Object.keys(s).length>0&&c.push(s),e(...c),a.default(l.localeCookie,u,n,o)}}return{...r,push:e(r.push),replace:e(r.replace),prefetch:e(r.prefetch)}}),[n,u,r])},getPathname:h}};
