"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("next/navigation"),t=require("react"),r=require("../../react-client/useLocale.js"),u=require("../../shared/utils.js"),n=require("../shared/syncLocaleCookie.js"),s=require("../shared/utils.js");exports.default=function(a,c){const o=e.useRouter(),i=r.default(),l=e.usePathname();return t.useMemo((()=>{function e(e,t){let r=window.location.pathname;const n=s.getBasePath(l);n&&(r=r.replace(n,""));const c=t||i,o=u.getLocalePrefix(c,a);return u.localizeHref(e,c,i,r,o)}function t(t){return function(r,u){const{locale:s,...a}=u||{};n.default(c,l,i,s);const o=[e(r,s)];return Object.keys(a).length>0&&o.push(a),t(...o)}}return{...o,push:t(o.push),replace:t(o.replace),prefetch:t(o.prefetch)}}),[i,c,a,l,o])};
