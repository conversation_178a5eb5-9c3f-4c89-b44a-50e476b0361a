"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../react-client/useLocale.js"),r=require("../shared/redirects.js");function t(r){return function(t){let c;try{c=e.default()}catch(e){throw e}for(var n=arguments.length,a=new Array(n>1?n-1:0),i=1;i<n;i++)a[i-1]=arguments[i];return r({...t,locale:c},...a)}}const c=t(r.baseRedirect),n=t(r.basePermanentRedirect);exports.clientPermanentRedirect=n,exports.clientRedirect=c;
