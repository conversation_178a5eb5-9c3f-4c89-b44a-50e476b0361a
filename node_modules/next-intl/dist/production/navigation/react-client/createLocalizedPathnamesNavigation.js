"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../_virtual/_rollupPluginBabelHelpers.js"),r=require("react"),a=require("../../react-client/useLocale.js"),t=require("../../routing/config.js"),l=require("../shared/utils.js"),n=require("./ClientLink.js"),o=require("./redirects.js"),c=require("./useBasePathname.js"),i=require("./useBaseRouter.js");function u(e){return e&&e.__esModule?e:{default:e}}var s=u(r);exports.default=function(u){const f=t.receiveRoutingConfig(u),h=t.receiveLocaleCookie(u.localeCookie);function d(){const e=a.default();if(!f.locales.includes(e))throw new Error(void 0);return e}function m(r,a){let{href:t,locale:o,...c}=r;const i=d(),u=o||i;return s.default.createElement(n.default,e.extends({ref:a,href:l.compileLocalizedPathname({locale:u,pathname:t,params:"object"==typeof t?t.params:void 0,pathnames:f.pathnames}),locale:o,localeCookie:h,localePrefix:f.localePrefix},c))}const p=r.forwardRef(m);function v(e){let{href:r,locale:a}=e;return l.compileLocalizedPathname({...l.normalizeNameOrNameWithParams(r),locale:a,pathnames:f.pathnames})}return p.displayName="Link",{Link:p,redirect:function(e){const r=v({href:e,locale:d()});for(var a=arguments.length,t=new Array(a>1?a-1:0),l=1;l<a;l++)t[l-1]=arguments[l];return o.clientRedirect({pathname:r,localePrefix:f.localePrefix},...t)},permanentRedirect:function(e){const r=v({href:e,locale:d()});for(var a=arguments.length,t=new Array(a>1?a-1:0),l=1;l<a;l++)t[l-1]=arguments[l];return o.clientPermanentRedirect({pathname:r,localePrefix:f.localePrefix},...t)},usePathname:function(){const e=c.default(f),a=d();return r.useMemo((()=>e?l.getRoute(a,e,f.pathnames):e),[a,e])},useRouter:function(){const e=i.default(f.localePrefix,h),a=d();return r.useMemo((()=>({...e,push(r){for(var t,l=arguments.length,n=new Array(l>1?l-1:0),o=1;o<l;o++)n[o-1]=arguments[o];const c=v({href:r,locale:(null===(t=n[0])||void 0===t?void 0:t.locale)||a});return e.push(c,...n)},replace(r){for(var t,l=arguments.length,n=new Array(l>1?l-1:0),o=1;o<l;o++)n[o-1]=arguments[o];const c=v({href:r,locale:(null===(t=n[0])||void 0===t?void 0:t.locale)||a});return e.replace(c,...n)},prefetch(r){for(var t,l=arguments.length,n=new Array(l>1?l-1:0),o=1;o<l;o++)n[o-1]=arguments[o];const c=v({href:r,locale:(null===(t=n[0])||void 0===t?void 0:t.locale)||a});return e.prefetch(c,...n)}})),[e,a])},getPathname:v}};
