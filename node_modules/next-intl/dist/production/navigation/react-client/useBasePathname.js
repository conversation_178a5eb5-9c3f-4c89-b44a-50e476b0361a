"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("next/navigation"),r=require("react"),a=require("../../react-client/useLocale.js"),t=require("../../shared/utils.js");exports.default=function(i){const n=e.usePathname(),s=a.default();return r.useMemo((()=>{if(!n)return n;let e=n;const r=t.getLocalePrefix(s,i.localePrefix);if(t.hasPathnamePrefixed(r,n))e=t.unprefixPathname(n,r);else if("as-needed"===i.localePrefix.mode&&i.localePrefix.prefixes){const r=t.getLocaleAsPrefix(s);t.hasPathnamePrefixed(r,n)&&(e=t.unprefixPathname(n,r))}return e}),[i.localePrefix,s,n])};
