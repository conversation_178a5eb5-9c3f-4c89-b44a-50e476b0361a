"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../_virtual/_rollupPluginBabelHelpers.js"),r=require("react"),t=require("../../routing/config.js"),n=require("./ClientLink.js"),l=require("./redirects.js"),i=require("./useBasePathname.js"),u=require("./useBaseRouter.js");function a(e){return e&&e.__esModule?e:{default:e}}var o=a(r);exports.default=function(a){const c=t.receiveLocalePrefixConfig(null==a?void 0:a.localePrefix),f=t.receiveLocaleCookie(null==a?void 0:a.localeCookie);function s(r,t){return o.default.createElement(n.default,e.extends({ref:t,localeCookie:f,localePrefix:c},r))}const d=r.forwardRef(s);return d.displayName="Link",{Link:d,redirect:function(e){for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];return l.clientRedirect({pathname:e,localePrefix:c},...t)},permanentRedirect:function(e){for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];return l.clientPermanentRedirect({pathname:e,localePrefix:c},...t)},usePathname:function(){return i.default({localePrefix:c,defaultLocale:null==a?void 0:a.defaultLocale})},useRouter:function(){return u.default(c,f)}}};
