"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../_virtual/_rollupPluginBabelHelpers.js"),r=require("react"),l=require("../../react-client/useLocale.js"),t=require("../../shared/utils.js"),a=require("../shared/LegacyBaseLink.js");function u(e){return e&&e.__esModule?e:{default:e}}var i=u(r);function s(r,u){let{locale:s,localePrefix:o,...c}=r;const n=l.default(),d=s||n,f=t.getLocalePrefix(d,o);return i.default.createElement(a.default,e.extends({ref:u,locale:d,localePrefixMode:o.mode,prefix:f},c))}const o=r.forwardRef(s);o.displayName="ClientLink",exports.default=o;
