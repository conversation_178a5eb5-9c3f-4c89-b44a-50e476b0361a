"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../_virtual/_rollupPluginBabelHelpers.js"),r=require("react"),l=require("../../shared/utils.js"),t=require("../shared/LegacyBaseLink.js"),a=require("../../server/react-server/getLocale.js");function u(e){return e&&e.__esModule?e:{default:e}}var s=u(r);exports.default=async function(r){let{locale:u,localePrefix:i,...o}=r;const c=u||await a.default(),d=l.getLocalePrefix(c,i);return s.default.createElement(t.default,e.extends({locale:c,localePrefixMode:i.mode,prefix:d},o))};
