"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../_virtual/_rollupPluginBabelHelpers.js"),r=require("react"),t=require("../../routing/config.js"),a=require("../../server/react-server/RequestLocaleLegacy.js"),o=require("../shared/utils.js"),n=require("./ServerLink.js"),l=require("./redirects.js");function c(e){return e&&e.__esModule?e:{default:e}}var i=c(r);exports.default=function(r){const c=t.receiveRoutingConfig(r);function u(e){let{href:r,locale:t}=e;return o.compileLocalizedPathname({...o.normalizeNameOrNameWithParams(r),locale:t,pathnames:c.pathnames})}function s(e){return()=>{throw new Error("`".concat(e,"` is not supported in Server Components. You can use this hook if you convert the component to a Client Component."))}}return{Link:function(r){let{href:t,locale:l,...u}=r;const s=a.getRequestLocale(),f=l||s;return i.default.createElement(n.default,e.extends({href:o.compileLocalizedPathname({locale:f,pathname:t,params:"object"==typeof t?t.params:void 0,pathnames:c.pathnames}),locale:l,localeCookie:c.localeCookie,localePrefix:c.localePrefix},u))},redirect:function(e){const r=u({href:e,locale:a.getRequestLocale()});for(var t=arguments.length,o=new Array(t>1?t-1:0),n=1;n<t;n++)o[n-1]=arguments[n];return l.serverRedirect({localePrefix:c.localePrefix,pathname:r},...o)},permanentRedirect:function(e){const r=u({href:e,locale:a.getRequestLocale()});for(var t=arguments.length,o=new Array(t>1?t-1:0),n=1;n<t;n++)o[n-1]=arguments[n];return l.serverPermanentRedirect({localePrefix:c.localePrefix,pathname:r},...o)},getPathname:u,usePathname:s("usePathname"),useRouter:s("useRouter")}};
