"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../shared/createSharedNavigationFns.js"),t=require("./getServerLocale.js");exports.default=function(r){const{config:n,...o}=e.default((function(){return t.default()}),r);function u(e){return()=>{throw new Error("`".concat(e,"` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component."))}}return{...o,usePathname:u("usePathname"),useRouter:u("useRouter")}};
