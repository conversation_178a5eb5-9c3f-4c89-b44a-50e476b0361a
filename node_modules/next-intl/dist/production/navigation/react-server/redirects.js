"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../server/react-server/RequestLocaleLegacy.js"),r=require("../shared/redirects.js");function t(r){return function(t){const s=e.getRequestLocale();for(var c=arguments.length,n=new Array(c>1?c-1:0),a=1;a<c;a++)n[a-1]=arguments[a];return r({...t,locale:s},...n)}}const s=t(r.baseRedirect),c=t(r.basePermanentRedirect);exports.serverPermanentRedirect=c,exports.serverRedirect=s;
