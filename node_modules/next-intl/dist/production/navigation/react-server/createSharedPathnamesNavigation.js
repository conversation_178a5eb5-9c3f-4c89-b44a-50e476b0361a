"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../_virtual/_rollupPluginBabelHelpers.js"),r=require("react"),t=require("../../routing/config.js"),n=require("./ServerLink.js"),o=require("./redirects.js");function i(e){return e&&e.__esModule?e:{default:e}}var u=i(r);exports.default=function(r){const i=t.receiveLocalePrefixConfig(null==r?void 0:r.localePrefix),a=t.receiveLocaleCookie(null==r?void 0:r.localeCookie);function l(e){return()=>{throw new Error("`".concat(e,"` is not supported in Server Components. You can use this hook if you convert the component to a Client Component."))}}return{Link:function(r){return u.default.createElement(n.default,e.extends({localeCookie:a,localePrefix:i},r))},redirect:function(e){for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];return o.serverRedirect({pathname:e,localePrefix:i},...t)},permanentRedirect:function(e){for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];return o.serverPermanentRedirect({pathname:e,localePrefix:i},...t)},usePathname:l("usePathname"),useRouter:l("useRouter")}};
