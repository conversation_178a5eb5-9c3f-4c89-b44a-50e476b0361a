"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("../server/react-server/getConfig.js");exports.default=function(n){return function(r,n){try{return e.use(n)}catch(e){throw e instanceof TypeError&&e.message.includes("Cannot read properties of null (reading 'use')")?new Error("`".concat(r,"` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components"),{cause:e}):e}}(n,r.default())};
