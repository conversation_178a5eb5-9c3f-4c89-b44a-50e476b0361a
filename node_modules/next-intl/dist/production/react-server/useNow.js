"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./useConfig.js");exports.default=function(){for(var t=arguments.length,o=new Array(t),n=0;n<t;n++)o[n]=arguments[n];let[r]=o;return null!=(null==r?void 0:r.updateInterval)&&console.error("`useNow` doesn't support the `updateInterval` option in Server Components, the value will be ignored. If you need the value to update, you can convert the component to a Client Component."),e.default("useNow").now};
