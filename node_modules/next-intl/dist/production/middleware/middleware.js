"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("next/server"),a=require("../routing/config.js"),t=require("../shared/constants.js"),l=require("../shared/utils.js"),o=require("./getAlternateLinksHeaderValue.js"),n=require("./resolveLocale.js"),r=require("./syncCookie.js"),i=require("./utils.js");exports.default=function(s,c){var d,h,m;const f=a.receiveRoutingConfig({...s,alternateLinks:null!==(d=null==c?void 0:c.alternateLinks)&&void 0!==d?d:s.alternateLinks,localeDetection:null!==(h=null==c?void 0:c.localeDetection)&&void 0!==h?h:s.localeDetection,localeCookie:null!==(m=null==c?void 0:c.localeCookie)&&void 0!==m?m:s.localeCookie});return function(a){var s;let c;try{c=decodeURI(a.nextUrl.pathname)}catch(a){return e.NextResponse.next()}const d=i.sanitizePathname(c),{domain:h,locale:m}=n.default(f,a.headers,a.cookies,d),u=h?h.defaultLocale===m:m===f.defaultLocale,x=(null===(s=f.domains)||void 0===s?void 0:s.filter((e=>i.isLocaleSupportedOnDomain(m,e))))||[],P=null!=f.domains&&!h;function p(l){const o=new URL(l,a.url);a.nextUrl.basePath&&(o.pathname=i.applyBasePath(o.pathname,a.nextUrl.basePath));const n=new Headers(a.headers);return n.set(t.HEADER_LOCALE_NAME,m),e.NextResponse.rewrite(o,{request:{headers:n}})}function v(t,o){const n=new URL(t,a.url);if(n.pathname=l.normalizeTrailingSlash(n.pathname),x.length>0&&!o&&h){const e=i.getBestMatchingDomain(h,m,x);e&&(o=e.domain,e.defaultLocale===m&&"as-needed"===f.localePrefix.mode&&(n.pathname=i.getNormalizedPathname(n.pathname,f.locales,f.localePrefix)))}var r,s;o&&(n.host=o,a.headers.get("x-forwarded-host")&&(n.protocol=null!==(r=a.headers.get("x-forwarded-proto"))&&void 0!==r?r:a.nextUrl.protocol,n.port=null!==(s=a.headers.get("x-forwarded-port"))&&void 0!==s?s:""));return a.nextUrl.basePath&&(n.pathname=i.applyBasePath(n.pathname,a.nextUrl.basePath)),e.NextResponse.redirect(n.toString())}const g=i.getNormalizedPathname(d,f.locales,f.localePrefix),L=i.getPathnameMatch(d,f.locales,f.localePrefix),U=null!=L,k="never"===f.localePrefix.mode||u&&"as-needed"===f.localePrefix.mode;let q,j,C=g;const D=f.pathnames;if(D){let e;if([e,j]=i.getInternalTemplate(D,g,m),j){const t=D[j],o="string"==typeof t?t:t[m];if(l.matchesPathname(o,g))C=i.formatTemplatePathname(g,o,j);else{let n;n=e?"string"==typeof t?t:t[e]:j;const r=k?void 0:l.getLocalePrefix(m,f.localePrefix),s=i.formatTemplatePathname(g,n,o);q=v(i.formatPathname(s,r,a.nextUrl.search))}}}if(!q)if("/"!==C||U){const e=i.formatPathname(C,i.getLocaleAsPrefix(m),a.nextUrl.search);if(U){const t=i.formatPathname(g,L.prefix,a.nextUrl.search);if("never"===f.localePrefix.mode)q=v(i.formatPathname(g,void 0,a.nextUrl.search));else if(L.exact)if(u&&k)q=v(i.formatPathname(g,void 0,a.nextUrl.search));else if(f.domains){const a=i.getBestMatchingDomain(h,L.locale,x);q=(null==h?void 0:h.domain)===(null==a?void 0:a.domain)||P?p(e):v(t,null==a?void 0:a.domain)}else q=p(e);else q=v(t)}else q=k?p(e):v(i.formatPathname(g,l.getLocalePrefix(m,f.localePrefix),a.nextUrl.search))}else q=k?p(i.formatPathname(C,i.getLocaleAsPrefix(m),a.nextUrl.search)):v(i.formatPathname(g,l.getLocalePrefix(m,f.localePrefix),a.nextUrl.search));return f.localeDetection&&f.localeCookie&&r.default(a,q,m,f.localeCookie),"never"!==f.localePrefix.mode&&f.alternateLinks&&f.locales.length>1&&q.headers.set("Link",o.default({routing:f,localizedPathnames:null!=j&&D?D[j]:void 0,request:a,resolvedLocale:m})),q}};
