"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@formatjs/intl-localematcher"),o=require("negotiator"),l=require("./utils.js");function t(e){return e&&e.__esModule?e:{default:e}}var a=t(o);function n(o,l,t){let n;const c=new a.default({headers:{"accept-language":o.get("accept-language")||void 0}}).languages();try{const o=function(e){return e.slice().sort(((e,o)=>o.length-e.length))}(l);n=e.match(c,o,t)}catch(e){}return n}function c(e,o){if(e.localeCookie&&o.has(e.localeCookie.name)){var l;const t=null===(l=o.get(e.localeCookie.name))||void 0===l?void 0:l.value;if(t&&e.locales.includes(t))return t}}function i(e,o,t,a){let i;var r;a&&(i=null===(r=l.getPathnameMatch(a,e.locales,e.localePrefix))||void 0===r?void 0:r.locale);return!i&&e.localeDetection&&(i=c(e,t)),!i&&e.localeDetection&&(i=n(o,e.locales,e.defaultLocale)),i||(i=e.defaultLocale),i}function r(e,o,t,a){const r=function(e,o){const t=l.getHost(e);if(t)return o.find((e=>e.domain===t))}(o,e.domains);if(!r)return{locale:i(e,o,t,a)};let u;if(a){var s;const o=null===(s=l.getPathnameMatch(a,e.locales,e.localePrefix))||void 0===s?void 0:s.locale;if(o){if(!l.isLocaleSupportedOnDomain(o,r))return{locale:o,domain:r};u=o}}if(!u&&e.localeDetection){const o=c(e,t);o&&l.isLocaleSupportedOnDomain(o,r)&&(u=o)}if(!u&&e.localeDetection){const l=n(o,r.locales||e.locales,r.defaultLocale);l&&(u=l)}return u||(u=r.defaultLocale),{locale:u,domain:r}}exports.default=function(e,o,l,t){return e.domains?r(e,o,l,t):{locale:i(e,o,l,t)}},exports.getAcceptLanguageLocale=n;
