"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../shared/utils.js"),a=require("./utils.js");exports.default=function(t){var o;let{localizedPathnames:n,request:l,resolvedLocale:r,routing:s}=t;const i=l.nextUrl.clone(),c=a.getHost(l.headers);function p(t,o){return t.pathname=e.normalizeTrailingSlash(t.pathname),l.nextUrl.basePath&&((t=new URL(t)).pathname=a.applyBasePath(t.pathname,l.nextUrl.basePath)),"<".concat(t.toString(),'>; rel="alternate"; hreflang="').concat(o,'"')}function m(e,t){return n&&"object"==typeof n?a.formatTemplatePathname(e,n[r],n[t]):e}c&&(i.port="",i.host=c),i.protocol=null!==(o=l.headers.get("x-forwarded-proto"))&&void 0!==o?o:i.protocol,i.pathname=a.getNormalizedPathname(i.pathname,s.locales,s.localePrefix);const h=a.getLocalePrefixes(s.locales,s.localePrefix,!1).flatMap((e=>{let t,[o,l]=e;function r(e){return"/"===e?l:l+e}if(s.domains){return s.domains.filter((e=>a.isLocaleSupportedOnDomain(o,e))).map((e=>(t=new URL(i),t.port="",t.host=e.domain,t.pathname=m(i.pathname,o),o===e.defaultLocale&&"always"!==s.localePrefix.mode||(t.pathname=r(t.pathname)),p(t,o))))}{let e;e=n&&"object"==typeof n?m(i.pathname,o):i.pathname,o===s.defaultLocale&&"always"!==s.localePrefix.mode||(e=r(e)),t=new URL(e,i)}return p(t,o)}));if(!s.domains&&("always"!==s.localePrefix.mode||"/"===i.pathname)){const e=new URL(m(i.pathname,s.defaultLocale),i);h.push(p(e,"x-default"))}return h.join(", ")};
