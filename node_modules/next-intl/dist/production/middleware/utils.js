"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../shared/utils.js");function t(t,n){let r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const o=t.map((t=>[t,e.getLocalePrefix(t,n)]));return r&&o.sort(((e,t)=>t[1].length-e[1].length)),o}function n(t,n){const r=e.normalizeTrailingSlash(n),o=e.normalizeTrailingSlash(t),a=e.templateToRegex(o).exec(r);if(!a)return;const l={};for(let e=1;e<a.length;e++){var i;const t=null===(i=o.match(/\[([^\]]+)\]/g))||void 0===i?void 0:i[e-1].replace(/[[\]]/g,"");t&&(l[t]=a[e])}return l}function r(e,t){if(!t)return e;let n=e=e.replace(/\[\[/g,"[").replace(/\]\]/g,"]");return Object.entries(t).forEach((e=>{let[t,r]=e;n=n.replace("[".concat(t,"]"),r)})),n}function o(e,t){return t.defaultLocale===e||!t.locales||t.locales.includes(e)}exports.applyBasePath=function(t,n){return e.normalizeTrailingSlash(n+t)},exports.formatPathname=function(t,n,r){let o=t;return n&&(o=e.prefixPathname(n,o)),r&&(o+=r),o},exports.formatPathnameTemplate=r,exports.formatTemplatePathname=function(t,o,a,l){let i="";return i+=r(a,n(o,t)),i=e.normalizeTrailingSlash(i),i},exports.getBestMatchingDomain=function(e,t,n){let r;return e&&o(t,e)&&(r=e),r||(r=n.find((e=>e.defaultLocale===t))),r||(r=n.find((e=>{var n;return null===(n=e.locales)||void 0===n?void 0:n.includes(t)}))),r||null!=(null==e?void 0:e.locales)||(r=e),r||(r=n.find((e=>!e.locales))),r},exports.getHost=function(e){var t,n;return null!==(t=null!==(n=e.get("x-forwarded-host"))&&void 0!==n?n:e.get("host"))&&void 0!==t?t:void 0},exports.getInternalTemplate=function(t,n,r){const o=e.getSortedPathnames(Object.keys(t));for(const a of o){const o=t[a];if("string"==typeof o){const t=o;if(e.matchesPathname(t,n))return[void 0,a]}else{const t=Object.entries(o),l=t.findIndex((e=>{let[t]=e;return t===r}));l>0&&t.unshift(t.splice(l,1)[0]);for(const[r,o]of t)if(e.matchesPathname(o,n))return[r,a]}}for(const r of Object.keys(t))if(e.matchesPathname(r,n))return[void 0,r];return[void 0,void 0]},exports.getLocaleAsPrefix=function(e){return"/".concat(e)},exports.getLocalePrefixes=t,exports.getNormalizedPathname=function(n,r,o){n.endsWith("/")||(n+="/");const a=t(r,o),l=new RegExp("^(".concat(a.map((e=>{let[,t]=e;return t.replaceAll("/","\\/")})).join("|"),")/(.*)"),"i"),i=n.match(l);let s=i?"/"+i[2]:n;return"/"!==s&&(s=e.normalizeTrailingSlash(s)),s},exports.getPathnameMatch=function(e,n,r){const o=t(n,r);for(const[t,n]of o){let r,o;if(e===n||e.startsWith(n+"/"))r=o=!0;else{const t=e.toLowerCase(),a=n.toLowerCase();(t===a||t.startsWith(a+"/"))&&(r=!1,o=!0)}if(o)return{locale:t,prefix:n,matchedPrefix:e.slice(0,n.length),exact:r}}},exports.getRouteParams=n,exports.isLocaleSupportedOnDomain=o,exports.sanitizePathname=function(e){return e.replace(/\\/g,"%5C").replace(/\/+/g,"/")};
