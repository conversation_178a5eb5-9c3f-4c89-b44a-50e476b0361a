"use strict";var n=require("fs"),t=require("path");function e(n){return n&&n.__esModule?n:{default:n}}var o=e(n),r=e(t);function i(n){return["".concat(n,".ts"),"".concat(n,".tsx"),"".concat(n,".js"),"".concat(n,".jsx")]}let l=!1;function s(n,t){function e(n){return o.default.existsSync(function(n){const e=[];return t&&e.push(t),e.push(n),r.default.resolve(...e)}(n))}if(n){if(!e(n))throw new Error("[next-intl] Could not find i18n config at ".concat(n,", please provide a valid path."));return n}for(const n of[...i("./i18n/request"),...i("./src/i18n/request")])if(e(n))return n;for(const n of[...i("./i18n"),...i("./src/i18n")])if(e(n))return l||(console.warn("\n[next-intl] Reading request configuration from ".concat(n," is deprecated, please see https://next-intl.dev/blog/next-intl-3-22#i18n-request — you can either move your configuration to ./i18n/request.ts or provide a custom path in your Next.js config:\n\nconst withNextIntl = createNextIntlPlugin(\n  './path/to/i18n/request.tsx'\n);\n")),l=!0),n;throw new Error("\n[next-intl] Could not locate request configuration module.\n\nThis path is supported by default: ./(src/)i18n/request.{js,jsx,ts,tsx}\n\nAlternatively, you can specify a custom location in your Next.js config:\n\nconst withNextIntl = createNextIntlPlugin(\n  './path/to/i18n/request.tsx'\n);\n")}module.exports=function(n){return function(t){return function(n,t){null!=(null==t?void 0:t.i18n)&&console.warn("\n[next-intl] An `i18n` property was found in your Next.js config. This likely causes conflicts and should therefore be removed if you use the App Router.\n\nIf you're in progress of migrating from the Pages Router, you can refer to this example: https://next-intl.dev/examples#app-router-migration\n");const e={};if(null!=process.env.TURBOPACK){var o,i;if(null!=n&&n.startsWith("/"))throw new Error("[next-intl] Turbopack support for next-intl currently does not support absolute paths, please provide a relative one (e.g. './src/i18n/config.ts').\n\nFound: "+n+"\n");e.experimental={...null==t?void 0:t.experimental,turbo:{...null==t||null===(o=t.experimental)||void 0===o?void 0:o.turbo,resolveAlias:{...null==t||null===(i=t.experimental)||void 0===i||null===(i=i.turbo)||void 0===i?void 0:i.resolveAlias,"next-intl/config":s(n)}}}}else e.webpack=function(){for(var e=arguments.length,o=new Array(e),i=0;i<e;i++)o[i]=arguments[i];let[l,u]=o;return l.resolve.alias["next-intl/config"]=r.default.resolve(l.context,s(n,l.context)),"function"==typeof(null==t?void 0:t.webpack)?t.webpack(l,u):l};return e.env={...null==t?void 0:t.env,_next_intl_trailing_slash:null!=t&&t.trailingSlash?"true":void 0},Object.assign({},t,e)}(n,t)}};
