"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("use-intl");function t(e,t){return function(){try{return t(...arguments)}catch(e){throw new Error(void 0)}}}const r=t(0,e.useTranslations),o=t(0,e.useFormatter);exports.useFormatter=o,exports.useTranslations=r,Object.keys(e).forEach((function(t){"default"===t||Object.prototype.hasOwnProperty.call(exports,t)||Object.defineProperty(exports,t,{enumerable:!0,get:function(){return e[t]}})}));
