import AbstractIntlMessages from '../core/AbstractIntlMessages';
import NestedKeyOf from '../core/utils/NestedKeyOf';
export default function useTranslationsImpl<Messages extends AbstractIntlMessages, NestedKey extends NestedKeyOf<Messages>>(allMessagesPrefixed: Messages, namespacePrefixed: NestedKey, namespacePrefix: string): {
    <TargetKey extends unknown>(key: TargetKey, values?: import("../core").TranslationValues, formats?: import("../core").Formats): string;
    rich: (key: string, values?: import("../core").RichTranslationValues, formats?: import("../core").Formats) => import("react").ReactNode;
    markup(key: Parameters<(key: string, values?: import("../core").RichTranslationValues, formats?: import("../core").Formats) => import("react").ReactNode>[0], values: import("../core").MarkupTranslationValues, formats?: Parameters<(key: string, values?: import("../core").RichTranslationValues, formats?: import("../core").Formats) => import("react").ReactNode>[2]): string;
    raw(key: string): any;
    has(key: Parameters<(key: string, values?: import("../core").RichTranslationValues, formats?: import("../core").Formats) => import("react").ReactNode>[0]): boolean;
};
