{"name": "@types/prop-types", "version": "15.7.14", "description": "TypeScript definitions for prop-types", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/prop-types", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "githubUsername": "ferd<PERSON><PERSON>", "url": "https://github.com/ferdaber"}, {"name": "<PERSON>", "githubUsername": "eps1lon", "url": "https://github.com/eps1lon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/prop-types"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "76a862de323d8337216b203df4c9133c09741e0faa9572926b65e698eab4ce58", "typeScriptVersion": "5.0"}