"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_disposable = void 0;
const base_config_1 = require("./base-config");
const es2015_symbol_1 = require("./es2015.symbol");
exports.esnext_disposable = {
    ...es2015_symbol_1.es2015_symbol,
    SymbolConstructor: base_config_1.TYPE,
    Disposable: base_config_1.TYPE,
    AsyncDisposable: base_config_1.TYPE,
    SuppressedError: base_config_1.TYPE_VALUE,
    SuppressedErrorConstructor: base_config_1.TYPE,
    DisposableStack: base_config_1.TYPE_VALUE,
    DisposableStackConstructor: base_config_1.TYPE,
    AsyncDisposableStack: base_config_1.TYPE_VALUE,
    AsyncDisposableStackConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=esnext.disposable.js.map