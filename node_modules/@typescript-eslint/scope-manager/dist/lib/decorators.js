"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.decorators = void 0;
const base_config_1 = require("./base-config");
exports.decorators = {
    ClassMemberDecoratorContext: base_config_1.TYPE,
    DecoratorContext: base_config_1.TYPE,
    DecoratorMetadataObject: base_config_1.TYPE,
    DecoratorMetadata: base_config_1.TYPE,
    ClassDecoratorContext: base_config_1.TYPE,
    ClassMethodDecoratorContext: base_config_1.TYPE,
    ClassGetterDecoratorContext: base_config_1.TYPE,
    ClassSetterDecoratorContext: base_config_1.TYPE,
    ClassAccessorDecoratorContext: base_config_1.TYPE,
    ClassAccessorDecoratorTarget: base_config_1.TYPE,
    ClassAccessorDecoratorResult: base_config_1.TYPE,
    ClassFieldDecoratorContext: base_config_1.TYPE,
};
//# sourceMappingURL=decorators.js.map