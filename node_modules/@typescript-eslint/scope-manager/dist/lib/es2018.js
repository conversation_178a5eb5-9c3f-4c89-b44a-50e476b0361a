"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2018 = void 0;
const es2017_1 = require("./es2017");
const es2018_asyncgenerator_1 = require("./es2018.asyncgenerator");
const es2018_asynciterable_1 = require("./es2018.asynciterable");
const es2018_intl_1 = require("./es2018.intl");
const es2018_promise_1 = require("./es2018.promise");
const es2018_regexp_1 = require("./es2018.regexp");
exports.es2018 = {
    ...es2017_1.es2017,
    ...es2018_asynciterable_1.es2018_asynciterable,
    ...es2018_asyncgenerator_1.es2018_asyncgenerator,
    ...es2018_promise_1.es2018_promise,
    ...es2018_regexp_1.es2018_regexp,
    ...es2018_intl_1.es2018_intl,
};
//# sourceMappingURL=es2018.js.map