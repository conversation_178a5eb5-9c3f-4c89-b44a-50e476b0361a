"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MappedTypeScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class MappedTypeScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scope<PERSON>anager, ScopeType_1.ScopeType.mappedType, upperScope, block, false);
    }
}
exports.MappedTypeScope = MappedTypeScope;
//# sourceMappingURL=MappedTypeScope.js.map