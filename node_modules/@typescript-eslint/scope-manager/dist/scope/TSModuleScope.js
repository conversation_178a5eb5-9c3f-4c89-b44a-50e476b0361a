"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TSModuleScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class TSModuleScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scopeManager, ScopeType_1.ScopeType.tsModule, upperScope, block, false);
    }
}
exports.TSModuleScope = TSModuleScope;
//# sourceMappingURL=TSModuleScope.js.map