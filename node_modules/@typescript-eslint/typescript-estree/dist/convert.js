"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _Converter_instances, _Converter_checkModifiers, _Converter_throwUnlessAllowInvalidAST, _Converter_withDeprecatedAliasGetter, _Converter_throwError, _Converter_checkForStatementDeclaration;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Converter = exports.convertError = void 0;
// There's lots of funny stuff due to the typing of ts.Node
/* eslint-disable @typescript-eslint/no-non-null-assertion, @typescript-eslint/no-unnecessary-condition, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access */
const ts = __importStar(require("typescript"));
const getModifiers_1 = require("./getModifiers");
const node_utils_1 = require("./node-utils");
const ts_estree_1 = require("./ts-estree");
const SyntaxKind = ts.SyntaxKind;
/**
 * Extends and formats a given error object
 * @param error the error object
 * @returns converted error object
 */
function convertError(error) {
    return (0, node_utils_1.createError)(('message' in error && error.message) || error.messageText, error.file, error.start);
}
exports.convertError = convertError;
class Converter {
    /**
     * Converts a TypeScript node into an ESTree node
     * @param ast the full TypeScript AST
     * @param options additional options for the conversion
     * @returns the converted ESTreeNode
     */
    constructor(ast, options) {
        _Converter_instances.add(this);
        this.esTreeNodeToTSNodeMap = new WeakMap();
        this.tsNodeToESTreeNodeMap = new WeakMap();
        this.allowPattern = false;
        this.ast = ast;
        this.options = { ...options };
    }
    getASTMaps() {
        return {
            esTreeNodeToTSNodeMap: this.esTreeNodeToTSNodeMap,
            tsNodeToESTreeNodeMap: this.tsNodeToESTreeNodeMap,
        };
    }
    convertProgram() {
        return this.converter(this.ast);
    }
    /**
     * Converts a TypeScript node into an ESTree node.
     * @param node the child ts.Node
     * @param parent parentNode
     * @param allowPattern flag to determine if patterns are allowed
     * @returns the converted ESTree node
     */
    converter(node, parent, allowPattern) {
        /**
         * Exit early for null and undefined
         */
        if (!node) {
            return null;
        }
        __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_checkModifiers).call(this, node);
        const pattern = this.allowPattern;
        if (allowPattern !== undefined) {
            this.allowPattern = allowPattern;
        }
        const result = this.convertNode(node, (parent ?? node.parent));
        this.registerTSNodeInNodeMap(node, result);
        this.allowPattern = pattern;
        return result;
    }
    /**
     * Fixes the exports of the given ts.Node
     * @returns the ESTreeNode with fixed exports
     */
    fixExports(node, result) {
        const isNamespaceNode = ts.isModuleDeclaration(node) &&
            Boolean(node.flags & ts.NodeFlags.Namespace);
        const modifiers = isNamespaceNode
            ? (0, node_utils_1.getNamespaceModifiers)(node)
            : (0, getModifiers_1.getModifiers)(node);
        if (modifiers?.[0].kind === SyntaxKind.ExportKeyword) {
            /**
             * Make sure that original node is registered instead of export
             */
            this.registerTSNodeInNodeMap(node, result);
            const exportKeyword = modifiers[0];
            const nextModifier = modifiers[1];
            const declarationIsDefault = nextModifier?.kind === SyntaxKind.DefaultKeyword;
            const varToken = declarationIsDefault
                ? (0, node_utils_1.findNextToken)(nextModifier, this.ast, this.ast)
                : (0, node_utils_1.findNextToken)(exportKeyword, this.ast, this.ast);
            result.range[0] = varToken.getStart(this.ast);
            result.loc = (0, node_utils_1.getLocFor)(result.range, this.ast);
            if (declarationIsDefault) {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ExportDefaultDeclaration,
                    declaration: result,
                    range: [exportKeyword.getStart(this.ast), result.range[1]],
                    exportKind: 'value',
                });
            }
            const isType = result.type === ts_estree_1.AST_NODE_TYPES.TSInterfaceDeclaration ||
                result.type === ts_estree_1.AST_NODE_TYPES.TSTypeAliasDeclaration;
            const isDeclare = 'declare' in result && result.declare;
            return this.createNode(node, 
            // @ts-expect-error - TODO, narrow the types here
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                type: ts_estree_1.AST_NODE_TYPES.ExportNamedDeclaration,
                declaration: result,
                specifiers: [],
                source: null,
                exportKind: isType || isDeclare ? 'type' : 'value',
                range: [exportKeyword.getStart(this.ast), result.range[1]],
                attributes: [],
            }, 'assertions', 'attributes', true));
        }
        return result;
    }
    /**
     * Register specific TypeScript node into map with first ESTree node provided
     */
    registerTSNodeInNodeMap(node, result) {
        if (result && this.options.shouldPreserveNodeMaps) {
            if (!this.tsNodeToESTreeNodeMap.has(node)) {
                this.tsNodeToESTreeNodeMap.set(node, result);
            }
        }
    }
    /**
     * Converts a TypeScript node into an ESTree node.
     * @param child the child ts.Node
     * @param parent parentNode
     * @returns the converted ESTree node
     */
    convertPattern(child, parent) {
        return this.converter(child, parent, true);
    }
    /**
     * Converts a TypeScript node into an ESTree node.
     * @param child the child ts.Node
     * @param parent parentNode
     * @returns the converted ESTree node
     */
    convertChild(child, parent) {
        return this.converter(child, parent, false);
    }
    createNode(
    // The 'parent' property will be added later if specified
    node, data) {
        const result = data;
        result.range ??= (0, node_utils_1.getRange)(node, this.ast);
        result.loc ??= (0, node_utils_1.getLocFor)(result.range, this.ast);
        if (result && this.options.shouldPreserveNodeMaps) {
            this.esTreeNodeToTSNodeMap.set(result, node);
        }
        return result;
    }
    convertBindingNameWithTypeAnnotation(name, tsType, parent) {
        const id = this.convertPattern(name);
        if (tsType) {
            id.typeAnnotation = this.convertTypeAnnotation(tsType, parent);
            this.fixParentLocation(id, id.typeAnnotation.range);
        }
        return id;
    }
    /**
     * Converts a child into a type annotation. This creates an intermediary
     * TypeAnnotation node to match what Flow does.
     * @param child The TypeScript AST node to convert.
     * @param parent parentNode
     * @returns The type annotation node.
     */
    convertTypeAnnotation(child, parent) {
        // in FunctionType and ConstructorType typeAnnotation has 2 characters `=>` and in other places is just colon
        const offset = parent?.kind === SyntaxKind.FunctionType ||
            parent?.kind === SyntaxKind.ConstructorType
            ? 2
            : 1;
        const annotationStartCol = child.getFullStart() - offset;
        const range = [annotationStartCol, child.end];
        const loc = (0, node_utils_1.getLocFor)(range, this.ast);
        return {
            type: ts_estree_1.AST_NODE_TYPES.TSTypeAnnotation,
            loc,
            range,
            typeAnnotation: this.convertChild(child),
        };
    }
    /**
     * Coverts body Nodes and add a directive field to StringLiterals
     * @param nodes of ts.Node
     * @param parent parentNode
     * @returns Array of body statements
     */
    convertBodyExpressions(nodes, parent) {
        let allowDirectives = (0, node_utils_1.canContainDirective)(parent);
        return (nodes
            .map(statement => {
            const child = this.convertChild(statement);
            if (allowDirectives) {
                if (child?.expression &&
                    ts.isExpressionStatement(statement) &&
                    ts.isStringLiteral(statement.expression)) {
                    const raw = child.expression.raw;
                    child.directive = raw.slice(1, -1);
                    return child; // child can be null, but it's filtered below
                }
                allowDirectives = false;
            }
            return child; // child can be null, but it's filtered below
        })
            // filter out unknown nodes for now
            .filter(statement => statement));
    }
    /**
     * Converts a ts.Node's typeArguments to TSTypeParameterInstantiation node
     * @param typeArguments ts.NodeArray typeArguments
     * @param node parent used to create this node
     * @returns TypeParameterInstantiation node
     */
    convertTypeArgumentsToTypeParameterInstantiation(typeArguments, node) {
        const greaterThanToken = (0, node_utils_1.findNextToken)(typeArguments, this.ast, this.ast);
        return this.createNode(node, {
            type: ts_estree_1.AST_NODE_TYPES.TSTypeParameterInstantiation,
            range: [typeArguments.pos - 1, greaterThanToken.end],
            params: typeArguments.map(typeArgument => this.convertChild(typeArgument)),
        });
    }
    /**
     * Converts a ts.Node's typeParameters to TSTypeParameterDeclaration node
     * @param typeParameters ts.Node typeParameters
     * @returns TypeParameterDeclaration node
     */
    convertTSTypeParametersToTypeParametersDeclaration(typeParameters) {
        const greaterThanToken = (0, node_utils_1.findNextToken)(typeParameters, this.ast, this.ast);
        const range = [
            typeParameters.pos - 1,
            greaterThanToken.end,
        ];
        return {
            type: ts_estree_1.AST_NODE_TYPES.TSTypeParameterDeclaration,
            range,
            loc: (0, node_utils_1.getLocFor)(range, this.ast),
            params: typeParameters.map(typeParameter => this.convertChild(typeParameter)),
        };
    }
    /**
     * Converts an array of ts.Node parameters into an array of ESTreeNode params
     * @param parameters An array of ts.Node params to be converted
     * @returns an array of converted ESTreeNode params
     */
    convertParameters(parameters) {
        if (!parameters?.length) {
            return [];
        }
        return parameters.map(param => {
            const convertedParam = this.convertChild(param);
            convertedParam.decorators =
                (0, getModifiers_1.getDecorators)(param)?.map(el => this.convertChild(el)) ?? [];
            return convertedParam;
        });
    }
    convertChainExpression(node, tsNode) {
        const { child, isOptional } = (() => {
            if (node.type === ts_estree_1.AST_NODE_TYPES.MemberExpression) {
                return { child: node.object, isOptional: node.optional };
            }
            if (node.type === ts_estree_1.AST_NODE_TYPES.CallExpression) {
                return { child: node.callee, isOptional: node.optional };
            }
            return { child: node.expression, isOptional: false };
        })();
        const isChildUnwrappable = (0, node_utils_1.isChildUnwrappableOptionalChain)(tsNode, child);
        if (!isChildUnwrappable && !isOptional) {
            return node;
        }
        if (isChildUnwrappable && (0, node_utils_1.isChainExpression)(child)) {
            // unwrap the chain expression child
            const newChild = child.expression;
            if (node.type === ts_estree_1.AST_NODE_TYPES.MemberExpression) {
                node.object = newChild;
            }
            else if (node.type === ts_estree_1.AST_NODE_TYPES.CallExpression) {
                node.callee = newChild;
            }
            else {
                node.expression = newChild;
            }
        }
        return this.createNode(tsNode, {
            type: ts_estree_1.AST_NODE_TYPES.ChainExpression,
            expression: node,
        });
    }
    /**
     * For nodes that are copied directly from the TypeScript AST into
     * ESTree mostly as-is. The only difference is the addition of a type
     * property instead of a kind property. Recursively copies all children.
     */
    deeplyCopy(node) {
        if (node.kind === ts.SyntaxKind.JSDocFunctionType) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, node, 'JSDoc types can only be used inside documentation comments.');
        }
        const customType = `TS${SyntaxKind[node.kind]}`;
        /**
         * If the "errorOnUnknownASTType" option is set to true, throw an error,
         * otherwise fallback to just including the unknown type as-is.
         */
        if (this.options.errorOnUnknownASTType && !ts_estree_1.AST_NODE_TYPES[customType]) {
            throw new Error(`Unknown AST_NODE_TYPE: "${customType}"`);
        }
        const result = this.createNode(node, {
            type: customType,
        });
        if ('type' in node) {
            result.typeAnnotation =
                node.type && 'kind' in node.type && ts.isTypeNode(node.type)
                    ? this.convertTypeAnnotation(node.type, node)
                    : null;
        }
        if ('typeArguments' in node) {
            result.typeArguments =
                node.typeArguments && 'pos' in node.typeArguments
                    ? this.convertTypeArgumentsToTypeParameterInstantiation(node.typeArguments, node)
                    : null;
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, result, 'typeParameters', 'typeArguments');
        }
        if ('typeParameters' in node) {
            result.typeParameters =
                node.typeParameters && 'pos' in node.typeParameters
                    ? this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters)
                    : null;
        }
        const decorators = (0, getModifiers_1.getDecorators)(node);
        if (decorators?.length) {
            result.decorators = decorators.map(el => this.convertChild(el));
        }
        // keys we never want to clone from the base typescript node as they
        // introduce garbage into our AST
        const KEYS_TO_NOT_COPY = new Set([
            '_children',
            'decorators',
            'end',
            'flags',
            'illegalDecorators',
            'heritageClauses',
            'locals',
            'localSymbol',
            'jsDoc',
            'kind',
            'modifierFlagsCache',
            'modifiers',
            'nextContainer',
            'parent',
            'pos',
            'symbol',
            'transformFlags',
            'type',
            'typeArguments',
            'typeParameters',
        ]);
        Object.entries(node)
            .filter(([key]) => !KEYS_TO_NOT_COPY.has(key))
            .forEach(([key, value]) => {
            if (Array.isArray(value)) {
                result[key] = value.map(el => this.convertChild(el));
            }
            else if (value && typeof value === 'object' && value.kind) {
                // need to check node[key].kind to ensure we don't try to convert a symbol
                result[key] = this.convertChild(value);
            }
            else {
                result[key] = value;
            }
        });
        return result;
    }
    convertJSXIdentifier(node) {
        const result = this.createNode(node, {
            type: ts_estree_1.AST_NODE_TYPES.JSXIdentifier,
            name: node.getText(),
        });
        this.registerTSNodeInNodeMap(node, result);
        return result;
    }
    convertJSXNamespaceOrIdentifier(node) {
        // TypeScript@5.1 added in ts.JsxNamespacedName directly
        // We prefer using that if it's relevant for this node type
        if (node.kind === ts.SyntaxKind.JsxNamespacedName) {
            const result = this.createNode(node, {
                type: ts_estree_1.AST_NODE_TYPES.JSXNamespacedName,
                namespace: this.createNode(node.namespace, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXIdentifier,
                    name: node.namespace.text,
                }),
                name: this.createNode(node.name, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXIdentifier,
                    name: node.name.text,
                }),
            });
            this.registerTSNodeInNodeMap(node, result);
            return result;
        }
        // TypeScript@<5.1 has to manually parse the JSX attributes
        const text = node.getText();
        const colonIndex = text.indexOf(':');
        // this is intentional we can ignore conversion if `:` is in first character
        if (colonIndex > 0) {
            const range = (0, node_utils_1.getRange)(node, this.ast);
            // @ts-expect-error -- TypeScript@<5.1 doesn't have ts.JsxNamespacedName
            const result = this.createNode(node, {
                type: ts_estree_1.AST_NODE_TYPES.JSXNamespacedName,
                namespace: this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXIdentifier,
                    name: text.slice(0, colonIndex),
                    range: [range[0], range[0] + colonIndex],
                }),
                name: this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXIdentifier,
                    name: text.slice(colonIndex + 1),
                    range: [range[0] + colonIndex + 1, range[1]],
                }),
                range,
            });
            this.registerTSNodeInNodeMap(node, result);
            return result;
        }
        return this.convertJSXIdentifier(node);
    }
    /**
     * Converts a TypeScript JSX node.tagName into an ESTree node.name
     * @param node the tagName object from a JSX ts.Node
     * @returns the converted ESTree name object
     */
    convertJSXTagName(node, parent) {
        let result;
        switch (node.kind) {
            case SyntaxKind.PropertyAccessExpression:
                if (node.name.kind === SyntaxKind.PrivateIdentifier) {
                    // This is one of the few times where TS explicitly errors, and doesn't even gracefully handle the syntax.
                    // So we shouldn't ever get into this state to begin with.
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, node.name, 'Non-private identifier expected.');
                }
                result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXMemberExpression,
                    object: this.convertJSXTagName(node.expression, parent),
                    property: this.convertJSXIdentifier(node.name),
                });
                break;
            case SyntaxKind.ThisKeyword:
            case SyntaxKind.Identifier:
            default:
                return this.convertJSXNamespaceOrIdentifier(node);
        }
        this.registerTSNodeInNodeMap(node, result);
        return result;
    }
    convertMethodSignature(node) {
        return this.createNode(node, {
            type: ts_estree_1.AST_NODE_TYPES.TSMethodSignature,
            accessibility: (0, node_utils_1.getTSNodeAccessibility)(node),
            computed: (0, node_utils_1.isComputedProperty)(node.name),
            key: this.convertChild(node.name),
            kind: (() => {
                switch (node.kind) {
                    case SyntaxKind.GetAccessor:
                        return 'get';
                    case SyntaxKind.SetAccessor:
                        return 'set';
                    case SyntaxKind.MethodSignature:
                        return 'method';
                }
            })(),
            optional: (0, node_utils_1.isOptional)(node),
            params: this.convertParameters(node.parameters),
            returnType: node.type && this.convertTypeAnnotation(node.type, node),
            readonly: (0, node_utils_1.hasModifier)(SyntaxKind.ReadonlyKeyword, node),
            static: (0, node_utils_1.hasModifier)(SyntaxKind.StaticKeyword, node),
            typeParameters: node.typeParameters &&
                this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
        });
    }
    convertImportAttributes(node) {
        return node === undefined
            ? []
            : node.elements.map(element => this.convertChild(element));
    }
    /**
     * Uses the provided range location to adjust the location data of the given Node
     * @param result The node that will have its location data mutated
     * @param childRange The child node range used to expand location
     */
    fixParentLocation(result, childRange) {
        if (childRange[0] < result.range[0]) {
            result.range[0] = childRange[0];
            result.loc.start = (0, node_utils_1.getLineAndCharacterFor)(result.range[0], this.ast);
        }
        if (childRange[1] > result.range[1]) {
            result.range[1] = childRange[1];
            result.loc.end = (0, node_utils_1.getLineAndCharacterFor)(result.range[1], this.ast);
        }
    }
    assertModuleSpecifier(node, allowNull) {
        if (!allowNull && node.moduleSpecifier == null) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node, 'Module specifier must be a string literal.');
        }
        if (node.moduleSpecifier &&
            node.moduleSpecifier?.kind !== SyntaxKind.StringLiteral) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node.moduleSpecifier, 'Module specifier must be a string literal.');
        }
    }
    /**
     * Converts a TypeScript node into an ESTree node.
     * The core of the conversion logic:
     * Identify and convert each relevant TypeScript SyntaxKind
     * @returns the converted ESTree node
     */
    convertNode(node, parent) {
        switch (node.kind) {
            case SyntaxKind.SourceFile: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Program,
                    body: this.convertBodyExpressions(node.statements, node),
                    comments: undefined,
                    range: [node.getStart(this.ast), node.endOfFileToken.end],
                    sourceType: node.externalModuleIndicator ? 'module' : 'script',
                    tokens: undefined,
                });
            }
            case SyntaxKind.Block: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.BlockStatement,
                    body: this.convertBodyExpressions(node.statements, node),
                });
            }
            case SyntaxKind.Identifier: {
                if ((0, node_utils_1.isThisInTypeQuery)(node)) {
                    // special case for `typeof this.foo` - TS emits an Identifier for `this`
                    // but we want to treat it as a ThisExpression for consistency
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.ThisExpression,
                    });
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Identifier,
                    decorators: [],
                    name: node.text,
                    optional: false,
                    typeAnnotation: undefined,
                });
            }
            case SyntaxKind.PrivateIdentifier: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.PrivateIdentifier,
                    // typescript includes the `#` in the text
                    name: node.text.slice(1),
                });
            }
            case SyntaxKind.WithStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.WithStatement,
                    object: this.convertChild(node.expression),
                    body: this.convertChild(node.statement),
                });
            // Control Flow
            case SyntaxKind.ReturnStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ReturnStatement,
                    argument: this.convertChild(node.expression),
                });
            case SyntaxKind.LabeledStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.LabeledStatement,
                    label: this.convertChild(node.label),
                    body: this.convertChild(node.statement),
                });
            case SyntaxKind.ContinueStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ContinueStatement,
                    label: this.convertChild(node.label),
                });
            case SyntaxKind.BreakStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.BreakStatement,
                    label: this.convertChild(node.label),
                });
            // Choice
            case SyntaxKind.IfStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.IfStatement,
                    test: this.convertChild(node.expression),
                    consequent: this.convertChild(node.thenStatement),
                    alternate: this.convertChild(node.elseStatement),
                });
            case SyntaxKind.SwitchStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.SwitchStatement,
                    discriminant: this.convertChild(node.expression),
                    cases: node.caseBlock.clauses.map(el => this.convertChild(el)),
                });
            case SyntaxKind.CaseClause:
            case SyntaxKind.DefaultClause:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.SwitchCase,
                    // expression is present in case only
                    test: node.kind === SyntaxKind.CaseClause
                        ? this.convertChild(node.expression)
                        : null,
                    consequent: node.statements.map(el => this.convertChild(el)),
                });
            // Exceptions
            case SyntaxKind.ThrowStatement:
                if (node.expression.end === node.expression.pos) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node, 'A throw statement must throw an expression.');
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ThrowStatement,
                    argument: this.convertChild(node.expression),
                });
            case SyntaxKind.TryStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TryStatement,
                    block: this.convertChild(node.tryBlock),
                    handler: this.convertChild(node.catchClause),
                    finalizer: this.convertChild(node.finallyBlock),
                });
            case SyntaxKind.CatchClause:
                if (node.variableDeclaration?.initializer) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, node.variableDeclaration.initializer, 'Catch clause variable cannot have an initializer.');
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.CatchClause,
                    param: node.variableDeclaration
                        ? this.convertBindingNameWithTypeAnnotation(node.variableDeclaration.name, node.variableDeclaration.type)
                        : null,
                    body: this.convertChild(node.block),
                });
            // Loops
            case SyntaxKind.WhileStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.WhileStatement,
                    test: this.convertChild(node.expression),
                    body: this.convertChild(node.statement),
                });
            /**
             * Unlike other parsers, TypeScript calls a "DoWhileStatement"
             * a "DoStatement"
             */
            case SyntaxKind.DoStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.DoWhileStatement,
                    test: this.convertChild(node.expression),
                    body: this.convertChild(node.statement),
                });
            case SyntaxKind.ForStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ForStatement,
                    init: this.convertChild(node.initializer),
                    test: this.convertChild(node.condition),
                    update: this.convertChild(node.incrementor),
                    body: this.convertChild(node.statement),
                });
            case SyntaxKind.ForInStatement:
                __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_checkForStatementDeclaration).call(this, node.initializer);
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ForInStatement,
                    left: this.convertPattern(node.initializer),
                    right: this.convertChild(node.expression),
                    body: this.convertChild(node.statement),
                });
            case SyntaxKind.ForOfStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ForOfStatement,
                    left: this.convertPattern(node.initializer),
                    right: this.convertChild(node.expression),
                    body: this.convertChild(node.statement),
                    await: Boolean(node.awaitModifier &&
                        node.awaitModifier.kind === SyntaxKind.AwaitKeyword),
                });
            // Declarations
            case SyntaxKind.FunctionDeclaration: {
                const isDeclare = (0, node_utils_1.hasModifier)(SyntaxKind.DeclareKeyword, node);
                const result = this.createNode(node, {
                    type: isDeclare || !node.body
                        ? ts_estree_1.AST_NODE_TYPES.TSDeclareFunction
                        : ts_estree_1.AST_NODE_TYPES.FunctionDeclaration,
                    async: (0, node_utils_1.hasModifier)(SyntaxKind.AsyncKeyword, node),
                    body: this.convertChild(node.body) || undefined,
                    declare: isDeclare,
                    expression: false,
                    generator: !!node.asteriskToken,
                    id: this.convertChild(node.name),
                    params: this.convertParameters(node.parameters),
                    returnType: node.type && this.convertTypeAnnotation(node.type, node),
                    typeParameters: node.typeParameters &&
                        this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
                });
                return this.fixExports(node, result);
            }
            case SyntaxKind.VariableDeclaration: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.VariableDeclarator,
                    definite: !!node.exclamationToken,
                    id: this.convertBindingNameWithTypeAnnotation(node.name, node.type, node),
                    init: this.convertChild(node.initializer),
                });
            }
            case SyntaxKind.VariableStatement: {
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.VariableDeclaration,
                    declarations: node.declarationList.declarations.map(el => this.convertChild(el)),
                    declare: (0, node_utils_1.hasModifier)(SyntaxKind.DeclareKeyword, node),
                    kind: (0, node_utils_1.getDeclarationKind)(node.declarationList),
                });
                if (!result.declarations.length) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node, 'A variable declaration list must have at least one variable declarator.');
                }
                if (result.kind === 'using' || result.kind === 'await using') {
                    node.declarationList.declarations.forEach((declaration, i) => {
                        if (result.declarations[i].init == null) {
                            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, declaration, `'${result.kind}' declarations must be initialized.`);
                        }
                        if (result.declarations[i].id.type !== ts_estree_1.AST_NODE_TYPES.Identifier) {
                            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, declaration.name, `'${result.kind}' declarations may not have binding patterns.`);
                        }
                    });
                }
                /**
                 * Semantically, decorators are not allowed on variable declarations,
                 * Pre 4.8 TS would include them in the AST, so we did as well.
                 * However as of 4.8 TS no longer includes it (as it is, well, invalid).
                 *
                 * So for consistency across versions, we no longer include it either.
                 */
                return this.fixExports(node, result);
            }
            // mostly for for-of, for-in
            case SyntaxKind.VariableDeclarationList: {
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.VariableDeclaration,
                    declarations: node.declarations.map(el => this.convertChild(el)),
                    declare: false,
                    kind: (0, node_utils_1.getDeclarationKind)(node),
                });
                if (result.kind === 'using' || result.kind === 'await using') {
                    node.declarations.forEach((declaration, i) => {
                        if (result.declarations[i].init != null) {
                            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, declaration, `'${result.kind}' declarations may not be initialized in for statement.`);
                        }
                        if (result.declarations[i].id.type !== ts_estree_1.AST_NODE_TYPES.Identifier) {
                            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, declaration.name, `'${result.kind}' declarations may not have binding patterns.`);
                        }
                    });
                }
                return result;
            }
            // Expressions
            case SyntaxKind.ExpressionStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ExpressionStatement,
                    directive: undefined,
                    expression: this.convertChild(node.expression),
                });
            case SyntaxKind.ThisKeyword:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ThisExpression,
                });
            case SyntaxKind.ArrayLiteralExpression: {
                // TypeScript uses ArrayLiteralExpression in destructuring assignment, too
                if (this.allowPattern) {
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.ArrayPattern,
                        decorators: [],
                        elements: node.elements.map(el => this.convertPattern(el)),
                        optional: false,
                        typeAnnotation: undefined,
                    });
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ArrayExpression,
                    elements: node.elements.map(el => this.convertChild(el)),
                });
            }
            case SyntaxKind.ObjectLiteralExpression: {
                // TypeScript uses ObjectLiteralExpression in destructuring assignment, too
                if (this.allowPattern) {
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.ObjectPattern,
                        decorators: [],
                        optional: false,
                        properties: node.properties.map(el => this.convertPattern(el)),
                        typeAnnotation: undefined,
                    });
                }
                const properties = [];
                for (const property of node.properties) {
                    if ((property.kind === SyntaxKind.GetAccessor ||
                        property.kind === SyntaxKind.SetAccessor ||
                        property.kind === SyntaxKind.MethodDeclaration) &&
                        !property.body) {
                        __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, property.end - 1, "'{' expected.");
                    }
                    properties.push(this.convertChild(property));
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ObjectExpression,
                    properties,
                });
            }
            case SyntaxKind.PropertyAssignment: {
                // eslint-disable-next-line deprecation/deprecation
                const { questionToken, exclamationToken } = node;
                if (questionToken) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, questionToken, 'A property assignment cannot have a question token.');
                }
                if (exclamationToken) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, exclamationToken, 'A property assignment cannot have an exclamation token.');
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Property,
                    key: this.convertChild(node.name),
                    value: this.converter(node.initializer, node, this.allowPattern),
                    computed: (0, node_utils_1.isComputedProperty)(node.name),
                    method: false,
                    optional: false,
                    shorthand: false,
                    kind: 'init',
                });
            }
            case SyntaxKind.ShorthandPropertyAssignment: {
                // eslint-disable-next-line deprecation/deprecation
                const { modifiers, questionToken, exclamationToken } = node;
                if (modifiers) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifiers[0], 'A shorthand property assignment cannot have modifiers.');
                }
                if (questionToken) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, questionToken, 'A shorthand property assignment cannot have a question token.');
                }
                if (exclamationToken) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, exclamationToken, 'A shorthand property assignment cannot have an exclamation token.');
                }
                if (node.objectAssignmentInitializer) {
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.Property,
                        key: this.convertChild(node.name),
                        value: this.createNode(node, {
                            type: ts_estree_1.AST_NODE_TYPES.AssignmentPattern,
                            decorators: [],
                            left: this.convertPattern(node.name),
                            optional: false,
                            right: this.convertChild(node.objectAssignmentInitializer),
                            typeAnnotation: undefined,
                        }),
                        computed: false,
                        method: false,
                        optional: false,
                        shorthand: true,
                        kind: 'init',
                    });
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Property,
                    computed: false,
                    key: this.convertChild(node.name),
                    kind: 'init',
                    method: false,
                    optional: false,
                    shorthand: true,
                    value: this.convertChild(node.name),
                });
            }
            case SyntaxKind.ComputedPropertyName:
                return this.convertChild(node.expression);
            case SyntaxKind.PropertyDeclaration: {
                const isAbstract = (0, node_utils_1.hasModifier)(SyntaxKind.AbstractKeyword, node);
                if (isAbstract && node.initializer) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, node.initializer, `Abstract property cannot have an initializer.`);
                }
                const isAccessor = (0, node_utils_1.hasModifier)(SyntaxKind.AccessorKeyword, node);
                const type = (() => {
                    if (isAccessor) {
                        if (isAbstract) {
                            return ts_estree_1.AST_NODE_TYPES.TSAbstractAccessorProperty;
                        }
                        return ts_estree_1.AST_NODE_TYPES.AccessorProperty;
                    }
                    if (isAbstract) {
                        return ts_estree_1.AST_NODE_TYPES.TSAbstractPropertyDefinition;
                    }
                    return ts_estree_1.AST_NODE_TYPES.PropertyDefinition;
                })();
                const key = this.convertChild(node.name);
                return this.createNode(node, {
                    type,
                    key,
                    accessibility: (0, node_utils_1.getTSNodeAccessibility)(node),
                    value: isAbstract ? null : this.convertChild(node.initializer),
                    computed: (0, node_utils_1.isComputedProperty)(node.name),
                    static: (0, node_utils_1.hasModifier)(SyntaxKind.StaticKeyword, node),
                    readonly: (0, node_utils_1.hasModifier)(SyntaxKind.ReadonlyKeyword, node),
                    decorators: (0, getModifiers_1.getDecorators)(node)?.map(el => this.convertChild(el)) ?? [],
                    declare: (0, node_utils_1.hasModifier)(SyntaxKind.DeclareKeyword, node),
                    override: (0, node_utils_1.hasModifier)(SyntaxKind.OverrideKeyword, node),
                    typeAnnotation: node.type && this.convertTypeAnnotation(node.type, node),
                    optional: (key.type === ts_estree_1.AST_NODE_TYPES.Literal ||
                        node.name.kind === SyntaxKind.Identifier ||
                        node.name.kind === SyntaxKind.ComputedPropertyName ||
                        node.name.kind === SyntaxKind.PrivateIdentifier) &&
                        !!node.questionToken,
                    definite: !!node.exclamationToken,
                });
            }
            case SyntaxKind.GetAccessor:
            case SyntaxKind.SetAccessor: {
                if (node.parent.kind === SyntaxKind.InterfaceDeclaration ||
                    node.parent.kind === SyntaxKind.TypeLiteral) {
                    return this.convertMethodSignature(node);
                }
            }
            // otherwise, it is a non-type accessor - intentional fallthrough
            case SyntaxKind.MethodDeclaration: {
                const method = this.createNode(node, {
                    type: !node.body
                        ? ts_estree_1.AST_NODE_TYPES.TSEmptyBodyFunctionExpression
                        : ts_estree_1.AST_NODE_TYPES.FunctionExpression,
                    id: null,
                    generator: !!node.asteriskToken,
                    expression: false, // ESTreeNode as ESTreeNode here
                    async: (0, node_utils_1.hasModifier)(SyntaxKind.AsyncKeyword, node),
                    body: this.convertChild(node.body),
                    declare: false,
                    range: [node.parameters.pos - 1, node.end],
                    params: [],
                    returnType: node.type && this.convertTypeAnnotation(node.type, node),
                    typeParameters: node.typeParameters &&
                        this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
                });
                if (method.typeParameters) {
                    this.fixParentLocation(method, method.typeParameters.range);
                }
                let result;
                if (parent.kind === SyntaxKind.ObjectLiteralExpression) {
                    method.params = node.parameters.map(el => this.convertChild(el));
                    result = this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.Property,
                        key: this.convertChild(node.name),
                        value: method,
                        computed: (0, node_utils_1.isComputedProperty)(node.name),
                        optional: !!node.questionToken,
                        method: node.kind === SyntaxKind.MethodDeclaration,
                        shorthand: false,
                        kind: 'init',
                    });
                }
                else {
                    // class
                    /**
                     * Unlike in object literal methods, class method params can have decorators
                     */
                    method.params = this.convertParameters(node.parameters);
                    /**
                     * TypeScript class methods can be defined as "abstract"
                     */
                    const methodDefinitionType = (0, node_utils_1.hasModifier)(SyntaxKind.AbstractKeyword, node)
                        ? ts_estree_1.AST_NODE_TYPES.TSAbstractMethodDefinition
                        : ts_estree_1.AST_NODE_TYPES.MethodDefinition;
                    result = this.createNode(node, {
                        type: methodDefinitionType,
                        accessibility: (0, node_utils_1.getTSNodeAccessibility)(node),
                        computed: (0, node_utils_1.isComputedProperty)(node.name),
                        decorators: (0, getModifiers_1.getDecorators)(node)?.map(el => this.convertChild(el)) ?? [],
                        key: this.convertChild(node.name),
                        kind: 'method',
                        optional: !!node.questionToken,
                        override: (0, node_utils_1.hasModifier)(SyntaxKind.OverrideKeyword, node),
                        static: (0, node_utils_1.hasModifier)(SyntaxKind.StaticKeyword, node),
                        value: method,
                    });
                }
                if (node.kind === SyntaxKind.GetAccessor) {
                    result.kind = 'get';
                }
                else if (node.kind === SyntaxKind.SetAccessor) {
                    result.kind = 'set';
                }
                else if (!result.static &&
                    node.name.kind === SyntaxKind.StringLiteral &&
                    node.name.text === 'constructor' &&
                    result.type !== ts_estree_1.AST_NODE_TYPES.Property) {
                    result.kind = 'constructor';
                }
                return result;
            }
            // TypeScript uses this even for static methods named "constructor"
            case SyntaxKind.Constructor: {
                const lastModifier = (0, node_utils_1.getLastModifier)(node);
                const constructorToken = (lastModifier && (0, node_utils_1.findNextToken)(lastModifier, node, this.ast)) ??
                    node.getFirstToken();
                const constructor = this.createNode(node, {
                    type: !node.body
                        ? ts_estree_1.AST_NODE_TYPES.TSEmptyBodyFunctionExpression
                        : ts_estree_1.AST_NODE_TYPES.FunctionExpression,
                    async: false,
                    body: this.convertChild(node.body),
                    declare: false,
                    expression: false, // is not present in ESTreeNode
                    generator: false,
                    id: null,
                    params: this.convertParameters(node.parameters),
                    range: [node.parameters.pos - 1, node.end],
                    returnType: node.type && this.convertTypeAnnotation(node.type, node),
                    typeParameters: node.typeParameters &&
                        this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
                });
                if (constructor.typeParameters) {
                    this.fixParentLocation(constructor, constructor.typeParameters.range);
                }
                const constructorKey = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Identifier,
                    decorators: [],
                    name: 'constructor',
                    optional: false,
                    range: [constructorToken.getStart(this.ast), constructorToken.end],
                    typeAnnotation: undefined,
                });
                const isStatic = (0, node_utils_1.hasModifier)(SyntaxKind.StaticKeyword, node);
                return this.createNode(node, {
                    type: (0, node_utils_1.hasModifier)(SyntaxKind.AbstractKeyword, node)
                        ? ts_estree_1.AST_NODE_TYPES.TSAbstractMethodDefinition
                        : ts_estree_1.AST_NODE_TYPES.MethodDefinition,
                    accessibility: (0, node_utils_1.getTSNodeAccessibility)(node),
                    computed: false,
                    decorators: [],
                    optional: false,
                    key: constructorKey,
                    kind: isStatic ? 'method' : 'constructor',
                    override: false,
                    static: isStatic,
                    value: constructor,
                });
            }
            case SyntaxKind.FunctionExpression: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.FunctionExpression,
                    async: (0, node_utils_1.hasModifier)(SyntaxKind.AsyncKeyword, node),
                    body: this.convertChild(node.body),
                    declare: false,
                    expression: false,
                    generator: !!node.asteriskToken,
                    id: this.convertChild(node.name),
                    params: this.convertParameters(node.parameters),
                    returnType: node.type && this.convertTypeAnnotation(node.type, node),
                    typeParameters: node.typeParameters &&
                        this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
                });
            }
            case SyntaxKind.SuperKeyword:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Super,
                });
            case SyntaxKind.ArrayBindingPattern:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ArrayPattern,
                    decorators: [],
                    elements: node.elements.map(el => this.convertPattern(el)),
                    optional: false,
                    typeAnnotation: undefined,
                });
            // occurs with missing array elements like [,]
            case SyntaxKind.OmittedExpression:
                return null;
            case SyntaxKind.ObjectBindingPattern:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ObjectPattern,
                    decorators: [],
                    optional: false,
                    properties: node.elements.map(el => this.convertPattern(el)),
                    typeAnnotation: undefined,
                });
            case SyntaxKind.BindingElement: {
                if (parent.kind === SyntaxKind.ArrayBindingPattern) {
                    const arrayItem = this.convertChild(node.name, parent);
                    if (node.initializer) {
                        return this.createNode(node, {
                            type: ts_estree_1.AST_NODE_TYPES.AssignmentPattern,
                            decorators: [],
                            left: arrayItem,
                            optional: false,
                            right: this.convertChild(node.initializer),
                            typeAnnotation: undefined,
                        });
                    }
                    else if (node.dotDotDotToken) {
                        return this.createNode(node, {
                            type: ts_estree_1.AST_NODE_TYPES.RestElement,
                            argument: arrayItem,
                            decorators: [],
                            optional: false,
                            typeAnnotation: undefined,
                            value: undefined,
                        });
                    }
                    return arrayItem;
                }
                let result;
                if (node.dotDotDotToken) {
                    result = this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.RestElement,
                        argument: this.convertChild(node.propertyName ?? node.name),
                        decorators: [],
                        optional: false,
                        typeAnnotation: undefined,
                        value: undefined,
                    });
                }
                else {
                    result = this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.Property,
                        key: this.convertChild(node.propertyName ?? node.name),
                        value: this.convertChild(node.name),
                        computed: Boolean(node.propertyName &&
                            node.propertyName.kind === SyntaxKind.ComputedPropertyName),
                        method: false,
                        optional: false,
                        shorthand: !node.propertyName,
                        kind: 'init',
                    });
                }
                if (node.initializer) {
                    result.value = this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.AssignmentPattern,
                        decorators: [],
                        left: this.convertChild(node.name),
                        optional: false,
                        range: [node.name.getStart(this.ast), node.initializer.end],
                        right: this.convertChild(node.initializer),
                        typeAnnotation: undefined,
                    });
                }
                return result;
            }
            case SyntaxKind.ArrowFunction: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ArrowFunctionExpression,
                    generator: false,
                    id: null,
                    params: this.convertParameters(node.parameters),
                    body: this.convertChild(node.body),
                    async: (0, node_utils_1.hasModifier)(SyntaxKind.AsyncKeyword, node),
                    expression: node.body.kind !== SyntaxKind.Block,
                    returnType: node.type && this.convertTypeAnnotation(node.type, node),
                    typeParameters: node.typeParameters &&
                        this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
                });
            }
            case SyntaxKind.YieldExpression:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.YieldExpression,
                    delegate: !!node.asteriskToken,
                    argument: this.convertChild(node.expression),
                });
            case SyntaxKind.AwaitExpression:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.AwaitExpression,
                    argument: this.convertChild(node.expression),
                });
            // Template Literals
            case SyntaxKind.NoSubstitutionTemplateLiteral:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TemplateLiteral,
                    quasis: [
                        this.createNode(node, {
                            type: ts_estree_1.AST_NODE_TYPES.TemplateElement,
                            value: {
                                raw: this.ast.text.slice(node.getStart(this.ast) + 1, node.end - 1),
                                cooked: node.text,
                            },
                            tail: true,
                        }),
                    ],
                    expressions: [],
                });
            case SyntaxKind.TemplateExpression: {
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TemplateLiteral,
                    quasis: [this.convertChild(node.head)],
                    expressions: [],
                });
                node.templateSpans.forEach(templateSpan => {
                    result.expressions.push(this.convertChild(templateSpan.expression));
                    result.quasis.push(this.convertChild(templateSpan.literal));
                });
                return result;
            }
            case SyntaxKind.TaggedTemplateExpression:
                return this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type: ts_estree_1.AST_NODE_TYPES.TaggedTemplateExpression,
                    typeArguments: node.typeArguments &&
                        this.convertTypeArgumentsToTypeParameterInstantiation(node.typeArguments, node),
                    tag: this.convertChild(node.tag),
                    quasi: this.convertChild(node.template),
                }, 'typeParameters', 'typeArguments'));
            case SyntaxKind.TemplateHead:
            case SyntaxKind.TemplateMiddle:
            case SyntaxKind.TemplateTail: {
                const tail = node.kind === SyntaxKind.TemplateTail;
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TemplateElement,
                    value: {
                        raw: this.ast.text.slice(node.getStart(this.ast) + 1, node.end - (tail ? 1 : 2)),
                        cooked: node.text,
                    },
                    tail,
                });
            }
            // Patterns
            case SyntaxKind.SpreadAssignment:
            case SyntaxKind.SpreadElement: {
                if (this.allowPattern) {
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.RestElement,
                        argument: this.convertPattern(node.expression),
                        decorators: [],
                        optional: false,
                        typeAnnotation: undefined,
                        value: undefined,
                    });
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.SpreadElement,
                    argument: this.convertChild(node.expression),
                });
            }
            case SyntaxKind.Parameter: {
                let parameter;
                let result;
                if (node.dotDotDotToken) {
                    parameter = result = this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.RestElement,
                        argument: this.convertChild(node.name),
                        decorators: [],
                        optional: false,
                        typeAnnotation: undefined,
                        value: undefined,
                    });
                }
                else if (node.initializer) {
                    parameter = this.convertChild(node.name);
                    result = this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.AssignmentPattern,
                        decorators: [],
                        left: parameter,
                        optional: false,
                        right: this.convertChild(node.initializer),
                        typeAnnotation: undefined,
                    });
                    const modifiers = (0, getModifiers_1.getModifiers)(node);
                    if (modifiers) {
                        // AssignmentPattern should not contain modifiers in range
                        result.range[0] = parameter.range[0];
                        result.loc = (0, node_utils_1.getLocFor)(result.range, this.ast);
                    }
                }
                else {
                    parameter = result = this.convertChild(node.name, parent);
                }
                if (node.type) {
                    parameter.typeAnnotation = this.convertTypeAnnotation(node.type, node);
                    this.fixParentLocation(parameter, parameter.typeAnnotation.range);
                }
                if (node.questionToken) {
                    if (node.questionToken.end > parameter.range[1]) {
                        parameter.range[1] = node.questionToken.end;
                        parameter.loc.end = (0, node_utils_1.getLineAndCharacterFor)(parameter.range[1], this.ast);
                    }
                    parameter.optional = true;
                }
                const modifiers = (0, getModifiers_1.getModifiers)(node);
                if (modifiers) {
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.TSParameterProperty,
                        accessibility: (0, node_utils_1.getTSNodeAccessibility)(node),
                        decorators: [],
                        override: (0, node_utils_1.hasModifier)(SyntaxKind.OverrideKeyword, node),
                        parameter: result,
                        readonly: (0, node_utils_1.hasModifier)(SyntaxKind.ReadonlyKeyword, node),
                        static: (0, node_utils_1.hasModifier)(SyntaxKind.StaticKeyword, node),
                    });
                }
                return result;
            }
            // Classes
            case SyntaxKind.ClassDeclaration:
                if (!node.name &&
                    (!(0, node_utils_1.hasModifier)(ts.SyntaxKind.ExportKeyword, node) ||
                        !(0, node_utils_1.hasModifier)(ts.SyntaxKind.DefaultKeyword, node))) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node, "A class declaration without the 'default' modifier must have a name.");
                }
            /* intentional fallthrough */
            case SyntaxKind.ClassExpression: {
                const heritageClauses = node.heritageClauses ?? [];
                const classNodeType = node.kind === SyntaxKind.ClassDeclaration
                    ? ts_estree_1.AST_NODE_TYPES.ClassDeclaration
                    : ts_estree_1.AST_NODE_TYPES.ClassExpression;
                let extendsClause;
                let implementsClause;
                for (const heritageClause of heritageClauses) {
                    const { token, types } = heritageClause;
                    if (types.length === 0) {
                        __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, heritageClause, `'${ts.tokenToString(token)}' list cannot be empty.`);
                    }
                    if (token === SyntaxKind.ExtendsKeyword) {
                        if (extendsClause) {
                            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, heritageClause, "'extends' clause already seen.");
                        }
                        if (implementsClause) {
                            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, heritageClause, "'extends' clause must precede 'implements' clause.");
                        }
                        if (types.length > 1) {
                            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, types[1], 'Classes can only extend a single class.');
                        }
                        extendsClause ??= heritageClause;
                    }
                    else if (token === SyntaxKind.ImplementsKeyword) {
                        if (implementsClause) {
                            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, heritageClause, "'implements' clause already seen.");
                        }
                        implementsClause ??= heritageClause;
                    }
                }
                const result = this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type: classNodeType,
                    abstract: (0, node_utils_1.hasModifier)(SyntaxKind.AbstractKeyword, node),
                    body: this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.ClassBody,
                        body: node.members
                            .filter(node_utils_1.isESTreeClassMember)
                            .map(el => this.convertChild(el)),
                        range: [node.members.pos - 1, node.end],
                    }),
                    declare: (0, node_utils_1.hasModifier)(SyntaxKind.DeclareKeyword, node),
                    decorators: (0, getModifiers_1.getDecorators)(node)?.map(el => this.convertChild(el)) ?? [],
                    id: this.convertChild(node.name),
                    implements: implementsClause?.types.map(el => this.convertChild(el)) ?? [],
                    superClass: extendsClause?.types[0]
                        ? this.convertChild(extendsClause.types[0].expression)
                        : null,
                    superTypeArguments: undefined,
                    typeParameters: node.typeParameters &&
                        this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
                }, 'superTypeParameters', 'superTypeArguments'));
                if (extendsClause?.types[0]?.typeArguments) {
                    result.superTypeArguments =
                        this.convertTypeArgumentsToTypeParameterInstantiation(extendsClause.types[0].typeArguments, extendsClause.types[0]);
                }
                return this.fixExports(node, result);
            }
            // Modules
            case SyntaxKind.ModuleBlock:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSModuleBlock,
                    body: this.convertBodyExpressions(node.statements, node),
                });
            case SyntaxKind.ImportDeclaration: {
                this.assertModuleSpecifier(node, false);
                const result = this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type: ts_estree_1.AST_NODE_TYPES.ImportDeclaration,
                    source: this.convertChild(node.moduleSpecifier),
                    specifiers: [],
                    importKind: 'value',
                    attributes: this.convertImportAttributes(
                    // eslint-disable-next-line deprecation/deprecation -- TS <5.3
                    node.attributes ?? node.assertClause),
                }, 'assertions', 'attributes', true));
                if (node.importClause) {
                    if (node.importClause.isTypeOnly) {
                        result.importKind = 'type';
                    }
                    if (node.importClause.name) {
                        result.specifiers.push(this.convertChild(node.importClause));
                    }
                    if (node.importClause.namedBindings) {
                        switch (node.importClause.namedBindings.kind) {
                            case SyntaxKind.NamespaceImport:
                                result.specifiers.push(this.convertChild(node.importClause.namedBindings));
                                break;
                            case SyntaxKind.NamedImports:
                                result.specifiers = result.specifiers.concat(node.importClause.namedBindings.elements.map(el => this.convertChild(el)));
                                break;
                        }
                    }
                }
                return result;
            }
            case SyntaxKind.NamespaceImport:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ImportNamespaceSpecifier,
                    local: this.convertChild(node.name),
                });
            case SyntaxKind.ImportSpecifier:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ImportSpecifier,
                    local: this.convertChild(node.name),
                    imported: this.convertChild(node.propertyName ?? node.name),
                    importKind: node.isTypeOnly ? 'type' : 'value',
                });
            case SyntaxKind.ImportClause: {
                const local = this.convertChild(node.name);
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ImportDefaultSpecifier,
                    local,
                    range: local.range,
                });
            }
            case SyntaxKind.ExportDeclaration: {
                if (node.exportClause?.kind === SyntaxKind.NamedExports) {
                    this.assertModuleSpecifier(node, true);
                    return this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                        type: ts_estree_1.AST_NODE_TYPES.ExportNamedDeclaration,
                        source: this.convertChild(node.moduleSpecifier),
                        specifiers: node.exportClause.elements.map(el => this.convertChild(el)),
                        exportKind: node.isTypeOnly ? 'type' : 'value',
                        declaration: null,
                        attributes: this.convertImportAttributes(
                        // eslint-disable-next-line deprecation/deprecation -- TS <5.3
                        node.attributes ?? node.assertClause),
                    }, 'assertions', 'attributes', true));
                }
                this.assertModuleSpecifier(node, false);
                return this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type: ts_estree_1.AST_NODE_TYPES.ExportAllDeclaration,
                    source: this.convertChild(node.moduleSpecifier),
                    exportKind: node.isTypeOnly ? 'type' : 'value',
                    exported: node.exportClause?.kind === SyntaxKind.NamespaceExport
                        ? this.convertChild(node.exportClause.name)
                        : null,
                    attributes: this.convertImportAttributes(
                    // eslint-disable-next-line deprecation/deprecation -- TS <5.3
                    node.attributes ?? node.assertClause),
                }, 'assertions', 'attributes', true));
            }
            case SyntaxKind.ExportSpecifier:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ExportSpecifier,
                    local: this.convertChild(node.propertyName ?? node.name),
                    exported: this.convertChild(node.name),
                    exportKind: node.isTypeOnly ? 'type' : 'value',
                });
            case SyntaxKind.ExportAssignment:
                if (node.isExportEquals) {
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.TSExportAssignment,
                        expression: this.convertChild(node.expression),
                    });
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ExportDefaultDeclaration,
                    declaration: this.convertChild(node.expression),
                    exportKind: 'value',
                });
            // Unary Operations
            case SyntaxKind.PrefixUnaryExpression:
            case SyntaxKind.PostfixUnaryExpression: {
                const operator = (0, node_utils_1.getTextForTokenKind)(node.operator);
                /**
                 * ESTree uses UpdateExpression for ++/--
                 */
                if (operator === '++' || operator === '--') {
                    if (!(0, node_utils_1.isValidAssignmentTarget)(node.operand)) {
                        __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node.operand, 'Invalid left-hand side expression in unary operation');
                    }
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.UpdateExpression,
                        operator,
                        prefix: node.kind === SyntaxKind.PrefixUnaryExpression,
                        argument: this.convertChild(node.operand),
                    });
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.UnaryExpression,
                    operator,
                    prefix: node.kind === SyntaxKind.PrefixUnaryExpression,
                    argument: this.convertChild(node.operand),
                });
            }
            case SyntaxKind.DeleteExpression:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.UnaryExpression,
                    operator: 'delete',
                    prefix: true,
                    argument: this.convertChild(node.expression),
                });
            case SyntaxKind.VoidExpression:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.UnaryExpression,
                    operator: 'void',
                    prefix: true,
                    argument: this.convertChild(node.expression),
                });
            case SyntaxKind.TypeOfExpression:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.UnaryExpression,
                    operator: 'typeof',
                    prefix: true,
                    argument: this.convertChild(node.expression),
                });
            case SyntaxKind.TypeOperator:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSTypeOperator,
                    operator: (0, node_utils_1.getTextForTokenKind)(node.operator),
                    typeAnnotation: this.convertChild(node.type),
                });
            // Binary Operations
            case SyntaxKind.BinaryExpression: {
                // TypeScript uses BinaryExpression for sequences as well
                if ((0, node_utils_1.isComma)(node.operatorToken)) {
                    const result = this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.SequenceExpression,
                        expressions: [],
                    });
                    const left = this.convertChild(node.left);
                    if (left.type === ts_estree_1.AST_NODE_TYPES.SequenceExpression &&
                        node.left.kind !== SyntaxKind.ParenthesizedExpression) {
                        result.expressions = result.expressions.concat(left.expressions);
                    }
                    else {
                        result.expressions.push(left);
                    }
                    result.expressions.push(this.convertChild(node.right));
                    return result;
                }
                const expressionType = (0, node_utils_1.getBinaryExpressionType)(node.operatorToken);
                if (this.allowPattern &&
                    expressionType.type === ts_estree_1.AST_NODE_TYPES.AssignmentExpression) {
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.AssignmentPattern,
                        decorators: [],
                        left: this.convertPattern(node.left, node),
                        optional: false,
                        right: this.convertChild(node.right),
                        typeAnnotation: undefined,
                    });
                }
                return this.createNode(node, {
                    ...expressionType,
                    left: this.converter(node.left, node, expressionType.type === ts_estree_1.AST_NODE_TYPES.AssignmentExpression),
                    right: this.convertChild(node.right),
                });
            }
            case SyntaxKind.PropertyAccessExpression: {
                const object = this.convertChild(node.expression);
                const property = this.convertChild(node.name);
                const computed = false;
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.MemberExpression,
                    object,
                    property,
                    computed,
                    optional: node.questionDotToken !== undefined,
                });
                return this.convertChainExpression(result, node);
            }
            case SyntaxKind.ElementAccessExpression: {
                const object = this.convertChild(node.expression);
                const property = this.convertChild(node.argumentExpression);
                const computed = true;
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.MemberExpression,
                    object,
                    property,
                    computed,
                    optional: node.questionDotToken !== undefined,
                });
                return this.convertChainExpression(result, node);
            }
            case SyntaxKind.CallExpression: {
                if (node.expression.kind === SyntaxKind.ImportKeyword) {
                    if (node.arguments.length !== 1 && node.arguments.length !== 2) {
                        __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node.arguments[2] ?? node, 'Dynamic import requires exactly one or two arguments.');
                    }
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.ImportExpression,
                        source: this.convertChild(node.arguments[0]),
                        attributes: node.arguments[1]
                            ? this.convertChild(node.arguments[1])
                            : null,
                    });
                }
                const callee = this.convertChild(node.expression);
                const args = node.arguments.map(el => this.convertChild(el));
                const typeArguments = node.typeArguments &&
                    this.convertTypeArgumentsToTypeParameterInstantiation(node.typeArguments, node);
                const result = this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type: ts_estree_1.AST_NODE_TYPES.CallExpression,
                    callee,
                    arguments: args,
                    optional: node.questionDotToken !== undefined,
                    typeArguments,
                }, 'typeParameters', 'typeArguments'));
                return this.convertChainExpression(result, node);
            }
            case SyntaxKind.NewExpression: {
                const typeArguments = node.typeArguments &&
                    this.convertTypeArgumentsToTypeParameterInstantiation(node.typeArguments, node);
                // NOTE - NewExpression cannot have an optional chain in it
                return this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type: ts_estree_1.AST_NODE_TYPES.NewExpression,
                    arguments: node.arguments
                        ? node.arguments.map(el => this.convertChild(el))
                        : [],
                    callee: this.convertChild(node.expression),
                    typeArguments,
                }, 'typeParameters', 'typeArguments'));
            }
            case SyntaxKind.ConditionalExpression:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ConditionalExpression,
                    test: this.convertChild(node.condition),
                    consequent: this.convertChild(node.whenTrue),
                    alternate: this.convertChild(node.whenFalse),
                });
            case SyntaxKind.MetaProperty: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.MetaProperty,
                    meta: this.createNode(
                    // TODO: do we really want to convert it to Token?
                    node.getFirstToken(), {
                        type: ts_estree_1.AST_NODE_TYPES.Identifier,
                        decorators: [],
                        name: (0, node_utils_1.getTextForTokenKind)(node.keywordToken),
                        optional: false,
                        typeAnnotation: undefined,
                    }),
                    property: this.convertChild(node.name),
                });
            }
            case SyntaxKind.Decorator: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Decorator,
                    expression: this.convertChild(node.expression),
                });
            }
            // Literals
            case SyntaxKind.StringLiteral: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Literal,
                    value: parent.kind === SyntaxKind.JsxAttribute
                        ? (0, node_utils_1.unescapeStringLiteralText)(node.text)
                        : node.text,
                    raw: node.getText(),
                });
            }
            case SyntaxKind.NumericLiteral: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Literal,
                    value: Number(node.text),
                    raw: node.getText(),
                });
            }
            case SyntaxKind.BigIntLiteral: {
                const range = (0, node_utils_1.getRange)(node, this.ast);
                const rawValue = this.ast.text.slice(range[0], range[1]);
                const bigint = rawValue
                    // remove suffix `n`
                    .slice(0, -1)
                    // `BigInt` doesn't accept numeric separator
                    // and `bigint` property should not include numeric separator
                    .replace(/_/g, '');
                const value = typeof BigInt !== 'undefined' ? BigInt(bigint) : null;
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Literal,
                    raw: rawValue,
                    value: value,
                    bigint: value == null ? bigint : String(value),
                    range,
                });
            }
            case SyntaxKind.RegularExpressionLiteral: {
                const pattern = node.text.slice(1, node.text.lastIndexOf('/'));
                const flags = node.text.slice(node.text.lastIndexOf('/') + 1);
                let regex = null;
                try {
                    regex = new RegExp(pattern, flags);
                }
                catch {
                    // Intentionally blank, so regex stays null
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Literal,
                    value: regex,
                    raw: node.text,
                    regex: {
                        pattern,
                        flags,
                    },
                });
            }
            case SyntaxKind.TrueKeyword:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Literal,
                    value: true,
                    raw: 'true',
                });
            case SyntaxKind.FalseKeyword:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Literal,
                    value: false,
                    raw: 'false',
                });
            case SyntaxKind.NullKeyword: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.Literal,
                    value: null,
                    raw: 'null',
                });
            }
            case SyntaxKind.EmptyStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.EmptyStatement,
                });
            case SyntaxKind.DebuggerStatement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.DebuggerStatement,
                });
            // JSX
            case SyntaxKind.JsxElement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXElement,
                    openingElement: this.convertChild(node.openingElement),
                    closingElement: this.convertChild(node.closingElement),
                    children: node.children.map(el => this.convertChild(el)),
                });
            case SyntaxKind.JsxFragment:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXFragment,
                    openingFragment: this.convertChild(node.openingFragment),
                    closingFragment: this.convertChild(node.closingFragment),
                    children: node.children.map(el => this.convertChild(el)),
                });
            case SyntaxKind.JsxSelfClosingElement: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXElement,
                    /**
                     * Convert SyntaxKind.JsxSelfClosingElement to SyntaxKind.JsxOpeningElement,
                     * TypeScript does not seem to have the idea of openingElement when tag is self-closing
                     */
                    openingElement: this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                        type: ts_estree_1.AST_NODE_TYPES.JSXOpeningElement,
                        typeArguments: node.typeArguments
                            ? this.convertTypeArgumentsToTypeParameterInstantiation(node.typeArguments, node)
                            : undefined,
                        selfClosing: true,
                        name: this.convertJSXTagName(node.tagName, node),
                        attributes: node.attributes.properties.map(el => this.convertChild(el)),
                        range: (0, node_utils_1.getRange)(node, this.ast),
                    }, 'typeParameters', 'typeArguments')),
                    closingElement: null,
                    children: [],
                });
            }
            case SyntaxKind.JsxOpeningElement: {
                return this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXOpeningElement,
                    typeArguments: node.typeArguments &&
                        this.convertTypeArgumentsToTypeParameterInstantiation(node.typeArguments, node),
                    selfClosing: false,
                    name: this.convertJSXTagName(node.tagName, node),
                    attributes: node.attributes.properties.map(el => this.convertChild(el)),
                }, 'typeParameters', 'typeArguments'));
            }
            case SyntaxKind.JsxClosingElement:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXClosingElement,
                    name: this.convertJSXTagName(node.tagName, node),
                });
            case SyntaxKind.JsxOpeningFragment:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXOpeningFragment,
                });
            case SyntaxKind.JsxClosingFragment:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXClosingFragment,
                });
            case SyntaxKind.JsxExpression: {
                const expression = node.expression
                    ? this.convertChild(node.expression)
                    : this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.JSXEmptyExpression,
                        range: [node.getStart(this.ast) + 1, node.getEnd() - 1],
                    });
                if (node.dotDotDotToken) {
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.JSXSpreadChild,
                        expression,
                    });
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXExpressionContainer,
                    expression,
                });
            }
            case SyntaxKind.JsxAttribute: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXAttribute,
                    name: this.convertJSXNamespaceOrIdentifier(node.name),
                    value: this.convertChild(node.initializer),
                });
            }
            case SyntaxKind.JsxText: {
                const start = node.getFullStart();
                const end = node.getEnd();
                const text = this.ast.text.slice(start, end);
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXText,
                    value: (0, node_utils_1.unescapeStringLiteralText)(text),
                    raw: text,
                    range: [start, end],
                });
            }
            case SyntaxKind.JsxSpreadAttribute:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.JSXSpreadAttribute,
                    argument: this.convertChild(node.expression),
                });
            case SyntaxKind.QualifiedName: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSQualifiedName,
                    left: this.convertChild(node.left),
                    right: this.convertChild(node.right),
                });
            }
            // TypeScript specific
            case SyntaxKind.TypeReference:
                return this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type: ts_estree_1.AST_NODE_TYPES.TSTypeReference,
                    typeName: this.convertChild(node.typeName),
                    typeArguments: node.typeArguments &&
                        this.convertTypeArgumentsToTypeParameterInstantiation(node.typeArguments, node),
                }, 'typeParameters', 'typeArguments'));
            case SyntaxKind.TypeParameter: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSTypeParameter,
                    name: this.convertChild(node.name),
                    constraint: node.constraint && this.convertChild(node.constraint),
                    default: node.default ? this.convertChild(node.default) : undefined,
                    in: (0, node_utils_1.hasModifier)(SyntaxKind.InKeyword, node),
                    out: (0, node_utils_1.hasModifier)(SyntaxKind.OutKeyword, node),
                    const: (0, node_utils_1.hasModifier)(SyntaxKind.ConstKeyword, node),
                });
            }
            case SyntaxKind.ThisType:
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSThisType,
                });
            case SyntaxKind.AnyKeyword:
            case SyntaxKind.BigIntKeyword:
            case SyntaxKind.BooleanKeyword:
            case SyntaxKind.NeverKeyword:
            case SyntaxKind.NumberKeyword:
            case SyntaxKind.ObjectKeyword:
            case SyntaxKind.StringKeyword:
            case SyntaxKind.SymbolKeyword:
            case SyntaxKind.UnknownKeyword:
            case SyntaxKind.VoidKeyword:
            case SyntaxKind.UndefinedKeyword:
            case SyntaxKind.IntrinsicKeyword: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES[`TS${SyntaxKind[node.kind]}`],
                });
            }
            case SyntaxKind.NonNullExpression: {
                const nnExpr = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSNonNullExpression,
                    expression: this.convertChild(node.expression),
                });
                return this.convertChainExpression(nnExpr, node);
            }
            case SyntaxKind.TypeLiteral: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSTypeLiteral,
                    members: node.members.map(el => this.convertChild(el)),
                });
            }
            case SyntaxKind.ArrayType: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSArrayType,
                    elementType: this.convertChild(node.elementType),
                });
            }
            case SyntaxKind.IndexedAccessType: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSIndexedAccessType,
                    objectType: this.convertChild(node.objectType),
                    indexType: this.convertChild(node.indexType),
                });
            }
            case SyntaxKind.ConditionalType: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSConditionalType,
                    checkType: this.convertChild(node.checkType),
                    extendsType: this.convertChild(node.extendsType),
                    trueType: this.convertChild(node.trueType),
                    falseType: this.convertChild(node.falseType),
                });
            }
            case SyntaxKind.TypeQuery:
                return this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type: ts_estree_1.AST_NODE_TYPES.TSTypeQuery,
                    exprName: this.convertChild(node.exprName),
                    typeArguments: node.typeArguments &&
                        this.convertTypeArgumentsToTypeParameterInstantiation(node.typeArguments, node),
                }, 'typeParameters', 'typeArguments'));
            case SyntaxKind.MappedType: {
                if (node.members && node.members.length > 0) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node.members[0], 'A mapped type may not declare properties or methods.');
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSMappedType,
                    nameType: this.convertChild(node.nameType) ?? null,
                    optional: node.questionToken &&
                        (node.questionToken.kind === SyntaxKind.QuestionToken ||
                            (0, node_utils_1.getTextForTokenKind)(node.questionToken.kind)),
                    readonly: node.readonlyToken &&
                        (node.readonlyToken.kind === SyntaxKind.ReadonlyKeyword ||
                            (0, node_utils_1.getTextForTokenKind)(node.readonlyToken.kind)),
                    typeAnnotation: node.type && this.convertChild(node.type),
                    typeParameter: this.convertChild(node.typeParameter),
                });
            }
            case SyntaxKind.ParenthesizedExpression:
                return this.convertChild(node.expression, parent);
            case SyntaxKind.TypeAliasDeclaration: {
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSTypeAliasDeclaration,
                    declare: (0, node_utils_1.hasModifier)(SyntaxKind.DeclareKeyword, node),
                    id: this.convertChild(node.name),
                    typeAnnotation: this.convertChild(node.type),
                    typeParameters: node.typeParameters &&
                        this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
                });
                return this.fixExports(node, result);
            }
            case SyntaxKind.MethodSignature: {
                return this.convertMethodSignature(node);
            }
            case SyntaxKind.PropertySignature: {
                // eslint-disable-next-line deprecation/deprecation
                const { initializer } = node;
                if (initializer) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, initializer, 'A property signature cannot have an initializer.');
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSPropertySignature,
                    accessibility: (0, node_utils_1.getTSNodeAccessibility)(node),
                    computed: (0, node_utils_1.isComputedProperty)(node.name),
                    key: this.convertChild(node.name),
                    optional: (0, node_utils_1.isOptional)(node),
                    readonly: (0, node_utils_1.hasModifier)(SyntaxKind.ReadonlyKeyword, node),
                    static: (0, node_utils_1.hasModifier)(SyntaxKind.StaticKeyword, node),
                    typeAnnotation: node.type && this.convertTypeAnnotation(node.type, node),
                });
            }
            case SyntaxKind.IndexSignature: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSIndexSignature,
                    accessibility: (0, node_utils_1.getTSNodeAccessibility)(node),
                    parameters: node.parameters.map(el => this.convertChild(el)),
                    readonly: (0, node_utils_1.hasModifier)(SyntaxKind.ReadonlyKeyword, node),
                    static: (0, node_utils_1.hasModifier)(SyntaxKind.StaticKeyword, node),
                    typeAnnotation: node.type && this.convertTypeAnnotation(node.type, node),
                });
            }
            case SyntaxKind.ConstructorType: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSConstructorType,
                    abstract: (0, node_utils_1.hasModifier)(SyntaxKind.AbstractKeyword, node),
                    params: this.convertParameters(node.parameters),
                    returnType: node.type && this.convertTypeAnnotation(node.type, node),
                    typeParameters: node.typeParameters &&
                        this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
                });
            }
            case SyntaxKind.FunctionType: {
                // eslint-disable-next-line deprecation/deprecation
                const { modifiers } = node;
                if (modifiers) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifiers[0], 'A function type cannot have modifiers.');
                }
            }
            // intentional fallthrough
            case SyntaxKind.ConstructSignature:
            case SyntaxKind.CallSignature: {
                const type = node.kind === SyntaxKind.ConstructSignature
                    ? ts_estree_1.AST_NODE_TYPES.TSConstructSignatureDeclaration
                    : node.kind === SyntaxKind.CallSignature
                        ? ts_estree_1.AST_NODE_TYPES.TSCallSignatureDeclaration
                        : ts_estree_1.AST_NODE_TYPES.TSFunctionType;
                return this.createNode(node, {
                    type,
                    params: this.convertParameters(node.parameters),
                    returnType: node.type && this.convertTypeAnnotation(node.type, node),
                    typeParameters: node.typeParameters &&
                        this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
                });
            }
            case SyntaxKind.ExpressionWithTypeArguments: {
                const parentKind = parent.kind;
                const type = parentKind === SyntaxKind.InterfaceDeclaration
                    ? ts_estree_1.AST_NODE_TYPES.TSInterfaceHeritage
                    : parentKind === SyntaxKind.HeritageClause
                        ? ts_estree_1.AST_NODE_TYPES.TSClassImplements
                        : ts_estree_1.AST_NODE_TYPES.TSInstantiationExpression;
                return this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type,
                    expression: this.convertChild(node.expression),
                    typeArguments: node.typeArguments &&
                        this.convertTypeArgumentsToTypeParameterInstantiation(node.typeArguments, node),
                }, 'typeParameters', 'typeArguments'));
            }
            case SyntaxKind.InterfaceDeclaration: {
                const interfaceHeritageClauses = node.heritageClauses ?? [];
                const interfaceExtends = [];
                for (const heritageClause of interfaceHeritageClauses) {
                    if (heritageClause.token !== SyntaxKind.ExtendsKeyword) {
                        __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, heritageClause, heritageClause.token === SyntaxKind.ImplementsKeyword
                            ? "Interface declaration cannot have 'implements' clause."
                            : 'Unexpected token.');
                    }
                    for (const heritageType of heritageClause.types) {
                        interfaceExtends.push(this.convertChild(heritageType, node));
                    }
                }
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSInterfaceDeclaration,
                    body: this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.TSInterfaceBody,
                        body: node.members.map(member => this.convertChild(member)),
                        range: [node.members.pos - 1, node.end],
                    }),
                    declare: (0, node_utils_1.hasModifier)(SyntaxKind.DeclareKeyword, node),
                    extends: interfaceExtends,
                    id: this.convertChild(node.name),
                    typeParameters: node.typeParameters &&
                        this.convertTSTypeParametersToTypeParametersDeclaration(node.typeParameters),
                });
                return this.fixExports(node, result);
            }
            case SyntaxKind.TypePredicate: {
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSTypePredicate,
                    asserts: node.assertsModifier !== undefined,
                    parameterName: this.convertChild(node.parameterName),
                    typeAnnotation: null,
                });
                /**
                 * Specific fix for type-guard location data
                 */
                if (node.type) {
                    result.typeAnnotation = this.convertTypeAnnotation(node.type, node);
                    result.typeAnnotation.loc = result.typeAnnotation.typeAnnotation.loc;
                    result.typeAnnotation.range =
                        result.typeAnnotation.typeAnnotation.range;
                }
                return result;
            }
            case SyntaxKind.ImportType: {
                const range = (0, node_utils_1.getRange)(node, this.ast);
                if (node.isTypeOf) {
                    const token = (0, node_utils_1.findNextToken)(node.getFirstToken(), node, this.ast);
                    range[0] = token.getStart(this.ast);
                }
                const result = this.createNode(node, __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_withDeprecatedAliasGetter).call(this, {
                    type: ts_estree_1.AST_NODE_TYPES.TSImportType,
                    argument: this.convertChild(node.argument),
                    qualifier: this.convertChild(node.qualifier),
                    typeArguments: node.typeArguments
                        ? this.convertTypeArgumentsToTypeParameterInstantiation(node.typeArguments, node)
                        : null,
                    range: range,
                }, 'typeParameters', 'typeArguments'));
                if (node.isTypeOf) {
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.TSTypeQuery,
                        exprName: result,
                        typeArguments: undefined,
                        typeParameters: undefined,
                    });
                }
                return result;
            }
            case SyntaxKind.EnumDeclaration: {
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSEnumDeclaration,
                    const: (0, node_utils_1.hasModifier)(SyntaxKind.ConstKeyword, node),
                    declare: (0, node_utils_1.hasModifier)(SyntaxKind.DeclareKeyword, node),
                    id: this.convertChild(node.name),
                    members: node.members.map(el => this.convertChild(el)),
                });
                return this.fixExports(node, result);
            }
            case SyntaxKind.EnumMember: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSEnumMember,
                    computed: node.name.kind === ts.SyntaxKind.ComputedPropertyName,
                    id: this.convertChild(node.name),
                    initializer: node.initializer && this.convertChild(node.initializer),
                });
            }
            case SyntaxKind.ModuleDeclaration: {
                let isDeclare = (0, node_utils_1.hasModifier)(SyntaxKind.DeclareKeyword, node);
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSModuleDeclaration,
                    ...(() => {
                        // the constraints checked by this function are syntactically enforced by TS
                        // the checks mostly exist for type's sake
                        if (node.flags & ts.NodeFlags.GlobalAugmentation) {
                            const id = this.convertChild(node.name);
                            const body = this.convertChild(node.body);
                            if (body == null ||
                                body.type === ts_estree_1.AST_NODE_TYPES.TSModuleDeclaration) {
                                __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node.body ?? node, 'Expected a valid module body');
                            }
                            if (id.type !== ts_estree_1.AST_NODE_TYPES.Identifier) {
                                __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node.name, 'global module augmentation must have an Identifier id');
                            }
                            return {
                                kind: 'global',
                                body: body,
                                declare: false,
                                global: false,
                                id,
                            };
                        }
                        if (!(node.flags & ts.NodeFlags.Namespace)) {
                            const body = this.convertChild(node.body);
                            return {
                                kind: 'module',
                                ...(body != null ? { body } : {}),
                                declare: false,
                                global: false,
                                id: this.convertChild(node.name),
                            };
                        }
                        // Nested module declarations are stored in TypeScript as nested tree nodes.
                        // We "unravel" them here by making our own nested TSQualifiedName,
                        // with the innermost node's body as the actual node body.
                        if (node.body == null) {
                            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node, 'Expected a module body');
                        }
                        if (node.name.kind !== ts.SyntaxKind.Identifier) {
                            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwUnlessAllowInvalidAST).call(this, node.name, '`namespace`s must have an Identifier id');
                        }
                        let name = this.createNode(node.name, {
                            decorators: [],
                            name: node.name.text,
                            optional: false,
                            range: [node.name.getStart(this.ast), node.name.getEnd()],
                            type: ts_estree_1.AST_NODE_TYPES.Identifier,
                            typeAnnotation: undefined,
                        });
                        while (node.body &&
                            ts.isModuleDeclaration(node.body) &&
                            node.body.name) {
                            node = node.body;
                            isDeclare ||= (0, node_utils_1.hasModifier)(SyntaxKind.DeclareKeyword, node);
                            const nextName = node.name;
                            const right = this.createNode(nextName, {
                                decorators: [],
                                name: nextName.text,
                                optional: false,
                                range: [nextName.getStart(this.ast), nextName.getEnd()],
                                type: ts_estree_1.AST_NODE_TYPES.Identifier,
                                typeAnnotation: undefined,
                            });
                            name = this.createNode(nextName, {
                                left: name,
                                right: right,
                                range: [name.range[0], right.range[1]],
                                type: ts_estree_1.AST_NODE_TYPES.TSQualifiedName,
                            });
                        }
                        return {
                            kind: 'namespace',
                            body: this.convertChild(node.body),
                            declare: false,
                            global: false,
                            id: name,
                        };
                    })(),
                });
                result.declare = isDeclare;
                if (node.flags & ts.NodeFlags.GlobalAugmentation) {
                    result.global = true;
                }
                return this.fixExports(node, result);
            }
            // TypeScript specific types
            case SyntaxKind.ParenthesizedType: {
                return this.convertChild(node.type);
            }
            case SyntaxKind.UnionType: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSUnionType,
                    types: node.types.map(el => this.convertChild(el)),
                });
            }
            case SyntaxKind.IntersectionType: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSIntersectionType,
                    types: node.types.map(el => this.convertChild(el)),
                });
            }
            case SyntaxKind.AsExpression: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSAsExpression,
                    expression: this.convertChild(node.expression),
                    typeAnnotation: this.convertChild(node.type),
                });
            }
            case SyntaxKind.InferType: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSInferType,
                    typeParameter: this.convertChild(node.typeParameter),
                });
            }
            case SyntaxKind.LiteralType: {
                if (node.literal.kind === SyntaxKind.NullKeyword) {
                    // 4.0 started nesting null types inside a LiteralType node
                    // but our AST is designed around the old way of null being a keyword
                    return this.createNode(node.literal, {
                        type: ts_estree_1.AST_NODE_TYPES.TSNullKeyword,
                    });
                }
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSLiteralType,
                    literal: this.convertChild(node.literal),
                });
            }
            case SyntaxKind.TypeAssertionExpression: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSTypeAssertion,
                    typeAnnotation: this.convertChild(node.type),
                    expression: this.convertChild(node.expression),
                });
            }
            case SyntaxKind.ImportEqualsDeclaration: {
                return this.fixExports(node, this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSImportEqualsDeclaration,
                    id: this.convertChild(node.name),
                    importKind: node.isTypeOnly ? 'type' : 'value',
                    moduleReference: this.convertChild(node.moduleReference),
                }));
            }
            case SyntaxKind.ExternalModuleReference: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSExternalModuleReference,
                    expression: this.convertChild(node.expression),
                });
            }
            case SyntaxKind.NamespaceExportDeclaration: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSNamespaceExportDeclaration,
                    id: this.convertChild(node.name),
                });
            }
            case SyntaxKind.AbstractKeyword: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSAbstractKeyword,
                });
            }
            // Tuple
            case SyntaxKind.TupleType: {
                const elementTypes = node.elements.map(el => this.convertChild(el));
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSTupleType,
                    elementTypes,
                });
            }
            case SyntaxKind.NamedTupleMember: {
                const member = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSNamedTupleMember,
                    elementType: this.convertChild(node.type, node),
                    label: this.convertChild(node.name, node),
                    optional: node.questionToken != null,
                });
                if (node.dotDotDotToken) {
                    // adjust the start to account for the "..."
                    member.range[0] = member.label.range[0];
                    member.loc.start = member.label.loc.start;
                    return this.createNode(node, {
                        type: ts_estree_1.AST_NODE_TYPES.TSRestType,
                        typeAnnotation: member,
                    });
                }
                return member;
            }
            case SyntaxKind.OptionalType: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSOptionalType,
                    typeAnnotation: this.convertChild(node.type),
                });
            }
            case SyntaxKind.RestType: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSRestType,
                    typeAnnotation: this.convertChild(node.type),
                });
            }
            // Template Literal Types
            case SyntaxKind.TemplateLiteralType: {
                const result = this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSTemplateLiteralType,
                    quasis: [this.convertChild(node.head)],
                    types: [],
                });
                node.templateSpans.forEach(templateSpan => {
                    result.types.push(this.convertChild(templateSpan.type));
                    result.quasis.push(this.convertChild(templateSpan.literal));
                });
                return result;
            }
            case SyntaxKind.ClassStaticBlockDeclaration: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.StaticBlock,
                    body: this.convertBodyExpressions(node.body.statements, node),
                });
            }
            // eslint-disable-next-line deprecation/deprecation -- required for backwards-compatibility
            case SyntaxKind.AssertEntry:
            case SyntaxKind.ImportAttribute: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.ImportAttribute,
                    key: this.convertChild(node.name),
                    value: this.convertChild(node.value),
                });
            }
            case SyntaxKind.SatisfiesExpression: {
                return this.createNode(node, {
                    type: ts_estree_1.AST_NODE_TYPES.TSSatisfiesExpression,
                    expression: this.convertChild(node.expression),
                    typeAnnotation: this.convertChild(node.type),
                });
            }
            default:
                return this.deeplyCopy(node);
        }
    }
}
exports.Converter = Converter;
_Converter_instances = new WeakSet(), _Converter_checkModifiers = function _Converter_checkModifiers(node) {
    if (this.options.allowInvalidAST) {
        return;
    }
    // typescript<5.0.0
    if ((0, node_utils_1.nodeHasIllegalDecorators)(node)) {
        __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, node.illegalDecorators[0], 'Decorators are not valid here.');
    }
    for (const decorator of (0, getModifiers_1.getDecorators)(node, 
    /* includeIllegalDecorators */ true) ?? []) {
        // `checkGrammarModifiers` function in typescript
        if (!(0, node_utils_1.nodeCanBeDecorated)(node)) {
            if (ts.isMethodDeclaration(node) && !(0, node_utils_1.nodeIsPresent)(node.body)) {
                __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, decorator, 'A decorator can only decorate a method implementation, not an overload.');
            }
            else {
                __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, decorator, 'Decorators are not valid here.');
            }
        }
    }
    for (const modifier of (0, getModifiers_1.getModifiers)(node, 
    /* includeIllegalModifiers */ true) ?? []) {
        if (modifier.kind !== SyntaxKind.ReadonlyKeyword) {
            if (node.kind === SyntaxKind.PropertySignature ||
                node.kind === SyntaxKind.MethodSignature) {
                __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, `'${ts.tokenToString(modifier.kind)}' modifier cannot appear on a type member`);
            }
            if (node.kind === SyntaxKind.IndexSignature &&
                (modifier.kind !== SyntaxKind.StaticKeyword ||
                    !ts.isClassLike(node.parent))) {
                __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, `'${ts.tokenToString(modifier.kind)}' modifier cannot appear on an index signature`);
            }
        }
        if (modifier.kind !== SyntaxKind.InKeyword &&
            modifier.kind !== SyntaxKind.OutKeyword &&
            modifier.kind !== SyntaxKind.ConstKeyword &&
            node.kind === SyntaxKind.TypeParameter) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, `'${ts.tokenToString(modifier.kind)}' modifier cannot appear on a type parameter`);
        }
        if ((modifier.kind === SyntaxKind.InKeyword ||
            modifier.kind === SyntaxKind.OutKeyword) &&
            (node.kind !== SyntaxKind.TypeParameter ||
                !(ts.isInterfaceDeclaration(node.parent) ||
                    ts.isClassLike(node.parent) ||
                    ts.isTypeAliasDeclaration(node.parent)))) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, `'${ts.tokenToString(modifier.kind)}' modifier can only appear on a type parameter of a class, interface or type alias`);
        }
        if (modifier.kind === SyntaxKind.ReadonlyKeyword &&
            node.kind !== SyntaxKind.PropertyDeclaration &&
            node.kind !== SyntaxKind.PropertySignature &&
            node.kind !== SyntaxKind.IndexSignature &&
            node.kind !== SyntaxKind.Parameter) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, "'readonly' modifier can only appear on a property declaration or index signature.");
        }
        if (modifier.kind === SyntaxKind.DeclareKeyword &&
            ts.isClassLike(node.parent) &&
            !ts.isPropertyDeclaration(node)) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, `'${ts.tokenToString(modifier.kind)}' modifier cannot appear on class elements of this kind.`);
        }
        if (modifier.kind === SyntaxKind.DeclareKeyword &&
            ts.isVariableStatement(node)) {
            const declarationKind = (0, node_utils_1.getDeclarationKind)(node.declarationList);
            if (declarationKind === 'using' || declarationKind === 'await using') {
                __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, `'declare' modifier cannot appear on a '${declarationKind}' declaration.`);
            }
        }
        if (modifier.kind === SyntaxKind.AbstractKeyword &&
            node.kind !== SyntaxKind.ClassDeclaration &&
            node.kind !== SyntaxKind.ConstructorType &&
            node.kind !== SyntaxKind.MethodDeclaration &&
            node.kind !== SyntaxKind.PropertyDeclaration &&
            node.kind !== SyntaxKind.GetAccessor &&
            node.kind !== SyntaxKind.SetAccessor) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, `'${ts.tokenToString(modifier.kind)}' modifier can only appear on a class, method, or property declaration.`);
        }
        if ((modifier.kind === SyntaxKind.StaticKeyword ||
            modifier.kind === SyntaxKind.PublicKeyword ||
            modifier.kind === SyntaxKind.ProtectedKeyword ||
            modifier.kind === SyntaxKind.PrivateKeyword) &&
            (node.parent.kind === SyntaxKind.ModuleBlock ||
                node.parent.kind === SyntaxKind.SourceFile)) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, `'${ts.tokenToString(modifier.kind)}' modifier cannot appear on a module or namespace element.`);
        }
        if (modifier.kind === SyntaxKind.AccessorKeyword &&
            node.kind !== SyntaxKind.PropertyDeclaration) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, "'accessor' modifier can only appear on a property declaration.");
        }
        // `checkGrammarAsyncModifier` function in `typescript`
        if (modifier.kind === SyntaxKind.AsyncKeyword &&
            node.kind !== SyntaxKind.MethodDeclaration &&
            node.kind !== SyntaxKind.FunctionDeclaration &&
            node.kind !== SyntaxKind.FunctionExpression &&
            node.kind !== SyntaxKind.ArrowFunction) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, "'async' modifier cannot be used here.");
        }
        // `checkGrammarModifiers` function in `typescript`
        if (node.kind === SyntaxKind.Parameter &&
            (modifier.kind === SyntaxKind.StaticKeyword ||
                modifier.kind === SyntaxKind.ExportKeyword ||
                modifier.kind === SyntaxKind.DeclareKeyword ||
                modifier.kind === SyntaxKind.AsyncKeyword)) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, `'${ts.tokenToString(modifier.kind)}' modifier cannot appear on a parameter.`);
        }
        // `checkGrammarModifiers` function in `typescript`
        if (modifier.kind === SyntaxKind.PublicKeyword ||
            modifier.kind === SyntaxKind.ProtectedKeyword ||
            modifier.kind === SyntaxKind.PrivateKeyword) {
            for (const anotherModifier of (0, getModifiers_1.getModifiers)(node) ?? []) {
                if (anotherModifier !== modifier &&
                    (anotherModifier.kind === SyntaxKind.PublicKeyword ||
                        anotherModifier.kind === SyntaxKind.ProtectedKeyword ||
                        anotherModifier.kind === SyntaxKind.PrivateKeyword)) {
                    __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, anotherModifier, `Accessibility modifier already seen.`);
                }
            }
        }
        // `checkParameter` function in `typescript`
        if (node.kind === SyntaxKind.Parameter &&
            // In `typescript` package, it's `ts.hasSyntacticModifier(node, ts.ModifierFlags.ParameterPropertyModifier)`
            // https://github.com/typescript-eslint/typescript-eslint/pull/6615#discussion_r1136489935
            (modifier.kind === SyntaxKind.PublicKeyword ||
                modifier.kind === SyntaxKind.PrivateKeyword ||
                modifier.kind === SyntaxKind.ProtectedKeyword ||
                modifier.kind === SyntaxKind.ReadonlyKeyword ||
                modifier.kind === SyntaxKind.OverrideKeyword)) {
            const func = (0, node_utils_1.getContainingFunction)(node);
            if (!(func.kind === SyntaxKind.Constructor && (0, node_utils_1.nodeIsPresent)(func.body))) {
                __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, modifier, 'A parameter property is only allowed in a constructor implementation.');
            }
        }
    }
}, _Converter_throwUnlessAllowInvalidAST = function _Converter_throwUnlessAllowInvalidAST(node, message) {
    if (!this.options.allowInvalidAST) {
        __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, node, message);
    }
}, _Converter_withDeprecatedAliasGetter = function _Converter_withDeprecatedAliasGetter(node, aliasKey, valueKey, suppressWarnings = false) {
    let warned = suppressWarnings;
    Object.defineProperty(node, aliasKey, {
        configurable: true,
        get: this.options.suppressDeprecatedPropertyWarnings
            ? () => node[valueKey]
            : () => {
                if (!warned) {
                    process.emitWarning(`The '${aliasKey}' property is deprecated on ${node.type} nodes. Use '${valueKey}' instead. See https://typescript-eslint.io/linting/troubleshooting#the-key-property-is-deprecated-on-type-nodes-use-key-instead-warnings.`, 'DeprecationWarning');
                    warned = true;
                }
                return node[valueKey];
            },
        set(value) {
            Object.defineProperty(node, aliasKey, {
                enumerable: true,
                writable: true,
                value,
            });
        },
    });
    return node;
}, _Converter_throwError = function _Converter_throwError(node, message) {
    let start;
    let end;
    if (typeof node === 'number') {
        start = end = node;
    }
    else {
        start = node.getStart(this.ast);
        end = node.getEnd();
    }
    throw (0, node_utils_1.createError)(message, this.ast, start, end);
}, _Converter_checkForStatementDeclaration = function _Converter_checkForStatementDeclaration(initializer) {
    if (ts.isVariableDeclarationList(initializer)) {
        if ((initializer.flags & ts.NodeFlags.Using) !== 0) {
            __classPrivateFieldGet(this, _Converter_instances, "m", _Converter_throwError).call(this, initializer, "The left-hand side of a 'for...in' statement cannot be a 'using' declaration.");
        }
    }
};
//# sourceMappingURL=convert.js.map