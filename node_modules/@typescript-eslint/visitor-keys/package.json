{"name": "@typescript-eslint/visitor-keys", "version": "7.2.0", "description": "Visitor keys used to help traverse the TypeScript-ESTree AST", "files": ["dist", "_ts4.3", "package.json", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^16.0.0 || >=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/visitor-keys"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"build": "tsc -b tsconfig.build.json", "postbuild": "downlevel-dts dist _ts4.3/dist --to=4.3", "clean": "tsc -b tsconfig.build.json --clean", "postclean": "rimraf dist && rimraf _ts3.4 && rimraf _ts4.3 && rimraf coverage", "format": "prettier --write \"./**/*.{ts,mts,cts,tsx,js,mjs,cjs,jsx,json,md,css}\" --ignore-path ../../.prettierignore", "lint": "npx nx lint", "test": "jest --coverage", "typecheck": "tsc -p tsconfig.json --noEmit"}, "dependencies": {"@typescript-eslint/types": "7.2.0", "eslint-visitor-keys": "^3.4.1"}, "devDependencies": {"@types/eslint-visitor-keys": "*", "downlevel-dts": "*", "jest": "29.7.0", "prettier": "^3.0.3", "rimraf": "*", "typescript": "*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "typesVersions": {"<4.7": {"*": ["_ts4.3/*"]}}}