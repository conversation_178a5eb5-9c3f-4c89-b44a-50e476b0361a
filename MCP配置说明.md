# MasterGo Magic MCP 配置说明

## 配置信息

### 服务器配置
- **服务器名称**: mastergo-magic-mcp
- **命令**: npx
- **包**: @mastergo/magic-mcp
- **Token**: mg_8059283fbcec4b61b7ff6285bf767a8c
- **URL**: https://mastergo.com

### 配置文件内容
```json
{
  "mcpServers": {
    "mastergo-magic-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@mastergo/magic-mcp",
        "--token=mg_8059283fbcec4b61b7ff6285bf767a8c",
        "--url=https://mastergo.com"
      ],
      "env": {
        "NPM_CONFIG_REGISTRY": "https://registry.npmjs.org/"
      }
    }
  }
}
```

## 配置步骤

### 1. 找到MCP配置文件位置
根据您使用的客户端，配置文件位置可能不同：

**Claude Desktop (macOS)**:
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

**Claude Desktop (Windows)**:
```
%APPDATA%\Claude\claude_desktop_config.json
```

**其他MCP客户端**:
请查看相应客户端的文档

### 2. 编辑配置文件
1. 打开配置文件（如果不存在则创建）
2. 将上面的JSON配置添加到文件中
3. 如果文件已有其他配置，请将 `mastergo-magic-mcp` 部分添加到 `mcpServers` 对象中

### 3. 重启客户端
保存配置文件后，重启您的MCP客户端以加载新配置。

## 验证配置

配置成功后，您应该能够：
1. 在MCP客户端中看到 MasterGo Magic MCP 服务器
2. 使用MasterGo相关的功能和工具
3. 通过提供的token访问您的MasterGo账户

## 注意事项

1. **Token安全**: 请妥善保管您的token，不要分享给他人
2. **网络连接**: 确保您的网络可以访问 https://mastergo.com
3. **Node.js**: 确保系统已安装Node.js和npm
4. **权限**: 确保有足够的权限执行npx命令

## 故障排除

### 常见问题
1. **Token无效**: 检查token是否正确复制，没有多余的空格
2. **网络问题**: 确认可以访问MasterGo网站
3. **npm问题**: 尝试清除npm缓存 `npm cache clean --force`
4. **权限问题**: 确保有执行npx的权限

### 测试连接
您可以在终端中手动运行以下命令来测试配置：
```bash
npx -y @mastergo/magic-mcp --token=mg_8059283fbcec4b61b7ff6285bf767a8c --url=https://mastergo.com
```

## 相关链接
- MasterGo官网: https://mastergo.com
- 账户安全设置: https://mastergo.com/files/account?tab=security
- MCP文档: 请参考您使用的MCP客户端文档
