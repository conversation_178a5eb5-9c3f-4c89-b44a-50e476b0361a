/* ===== RESET & BASE STYLES ===== */
:root {
    /* Primary Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Accent Colors */
    --accent-gold: #f59e0b;
    --accent-green: #10b981;
    --accent-red: #ef4444;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
    --gradient-hero: linear-gradient(135deg, rgba(37, 99, 235, 0.9) 0%, rgba(30, 78, 216, 0.95) 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-display: 'Playfair Display', Georgia, serif;

    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 6rem 0;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: #ffffff;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* ===== PRELOADER ===== */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loader {
    width: 80px;
    height: 80px;
    position: relative;
}

.loader-inner {
    width: 100%;
    height: 100%;
    position: relative;
}

.loader-line-wrap {
    animation: spin 2s cubic-bezier(0.175, 0.885, 0.32, 1.275) infinite;
    box-sizing: border-box;
    height: 50px;
    left: 50%;
    overflow: hidden;
    position: absolute;
    top: 50%;
    transform-origin: 50% 100%;
    width: 1px;
}

.loader-line {
    border: 2px solid transparent;
    border-radius: 100%;
    box-sizing: border-box;
    height: 100px;
    left: 50%;
    margin: 0 0 0 -1px;
    position: absolute;
    top: 0;
    width: 100px;
}

.loader-line-wrap:nth-child(1) { animation-delay: -50ms; }
.loader-line-wrap:nth-child(2) { animation-delay: -100ms; }
.loader-line-wrap:nth-child(3) { animation-delay: -150ms; }
.loader-line-wrap:nth-child(4) { animation-delay: -200ms; }
.loader-line-wrap:nth-child(5) { animation-delay: -250ms; }

.loader-line-wrap:nth-child(1) .loader-line { border-color: hsl(0, 80%, 60%); }
.loader-line-wrap:nth-child(2) .loader-line { border-color: hsl(60, 80%, 60%); }
.loader-line-wrap:nth-child(3) .loader-line { border-color: hsl(120, 80%, 60%); }
.loader-line-wrap:nth-child(4) .loader-line { border-color: hsl(180, 80%, 60%); }
.loader-line-wrap:nth-child(5) .loader-line { border-color: hsl(240, 80%, 60%); }

@keyframes spin {
    0%, 15% { transform: rotate(0); }
    25% { transform: rotate(30deg); }
    35% { transform: rotate(60deg); }
    45% { transform: rotate(90deg); }
    55% { transform: rotate(120deg); }
    65% { transform: rotate(150deg); }
    75% { transform: rotate(180deg); }
    85% { transform: rotate(210deg); }
    95% { transform: rotate(240deg); }
    100% { transform: rotate(270deg); }
}

.preloader-text {
    margin-top: 2rem;
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all var(--transition-normal);
    padding: 0.75rem 0;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--gray-800);
}

.brand-logo i {
    font-size: 1.75rem;
    color: var(--primary-600);
}

.brand-text {
    font-size: 1.5rem;
    font-weight: 800;
    letter-spacing: -0.025em;
}

.brand-tagline {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-weight: 500;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    text-decoration: none;
    color: var(--gray-700);
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.5rem 0;
    position: relative;
    transition: color var(--transition-normal);
}

.nav-link:hover {
    color: var(--primary-600);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav-link:hover::after {
    width: 100%;
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    min-width: 200px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    border: 1px solid var(--gray-100);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1.25rem;
    color: var(--gray-700);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all var(--transition-fast);
}

.dropdown-menu a:hover {
    background: var(--gray-50);
    color: var(--primary-600);
}

/* Language Toggle */
.language-toggle {
    position: relative;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.lang-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.lang-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    min-width: 150px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    border: 1px solid var(--gray-100);
}

.language-toggle:hover .lang-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lang-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--gray-700);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all var(--transition-fast);
}

.lang-option:hover {
    background: var(--gray-50);
    color: var(--primary-600);
}

.lang-option img {
    width: 20px;
    height: auto;
    border-radius: 2px;
}

/* Navigation Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-btn {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-btn span {
    width: 24px;
    height: 2px;
    background: var(--gray-700);
    transition: all var(--transition-normal);
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-full);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all var(--transition-normal);
    border: 2px solid transparent;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-600);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-600);
    border-color: var(--primary-600);
}

.btn-secondary:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-outline:hover {
    background: var(--gray-800);
    color: white;
    border-color: var(--gray-800);
}

.btn-white {
    background: white;
    color: var(--gray-800);
    border-color: white;
}

.btn-white:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
}

.hero-video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.hero-video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hero);
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-content {
    position: relative;
    z-index: 1;
    width: 100%;
    padding: 8rem 0 4rem;
}

.hero-text {
    max-width: 800px;
    margin-bottom: 4rem;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: white;
    padding: 0.5rem 1.25rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-title {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.1;
    color: white;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.title-line {
    display: block;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
}

.title-line:nth-child(1) { animation-delay: 0.2s; }
.title-line:nth-child(2) { animation-delay: 0.4s; }
.title-line:nth-child(3) { animation-delay: 0.6s; }

.title-line.highlight {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-slogan {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.slogan-en {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-style: italic;
}

.slogan-zh {
    font-size: 1.5rem;
    color: var(--accent-gold);
    font-weight: 600;
    letter-spacing: 0.1em;
}

.hero-description {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 2.5rem;
    max-width: 600px;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    max-width: 800px;
}

.stat-item {
    text-align: center;
    color: white;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--accent-gold);
    margin-bottom: 0.5rem;
    font-family: var(--font-display);
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: white;
    animation: bounce 2s infinite;
}

.scroll-text {
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0.8;
}

.scroll-arrow {
    font-size: 1.25rem;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* ===== TRUST SECTION ===== */
.trust-section {
    padding: 3rem 0;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.trust-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    flex-wrap: wrap;
}

.trust-text h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-700);
}

.trust-logos {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.logo-item {
    opacity: 0.6;
    transition: opacity var(--transition-normal);
}

.logo-item:hover {
    opacity: 1;
}

.logo-item img {
    height: 40px;
    width: auto;
    filter: grayscale(100%);
    transition: filter var(--transition-normal);
}

.logo-item:hover img {
    filter: grayscale(0%);
}

/* ===== SECTION HEADERS ===== */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-badge {
    display: inline-block;
    background: var(--primary-50);
    color: var(--primary-600);
    padding: 0.5rem 1.25rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: 1.5rem;
}

.section-description {
    font-size: 1.125rem;
    color: var(--gray-600);
    line-height: 1.7;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: var(--section-padding);
    background: white;
}

.about-content {
    margin-top: 2rem;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.feature-item {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
    box-shadow: var(--shadow-lg);
}

.feature-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.75rem;
}

.feature-content p {
    color: var(--gray-600);
    line-height: 1.6;
}

.about-cta {
    margin-top: 2rem;
}

.about-visual {
    position: relative;
}

.visual-container {
    position: relative;
}

.main-image {
    position: relative;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-2xl);
}

.main-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.main-image:hover .image-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    font-size: 2rem;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.play-button:hover {
    background: white;
    transform: scale(1.1);
}

.floating-cards {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: var(--radius-lg);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: float 3s ease-in-out infinite;
    border: 1px solid var(--gray-100);
}

.floating-card.card-1 {
    top: 10%;
    right: -10%;
    animation-delay: 0s;
}

.floating-card.card-2 {
    bottom: 30%;
    left: -15%;
    animation-delay: 1s;
}

.floating-card.card-3 {
    top: 60%;
    right: -5%;
    animation-delay: 2s;
}

.card-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-50);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    font-size: 1.25rem;
}

.card-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-800);
    white-space: nowrap;
}

/* ===== PRODUCTS SECTION ===== */
.products-section {
    padding: var(--section-padding);
    background: var(--gray-50);
}

.products-filter {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: white;
    color: var(--gray-600);
    border: 2px solid var(--gray-200);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-full);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
    transform: translateY(-2px);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.product-card {
    background: white;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-100);
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--primary-600);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.product-badge.eco {
    background: var(--accent-green);
}

.product-badge.new {
    background: var(--accent-gold);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-content {
    padding: 2rem;
}

.product-category {
    color: var(--primary-600);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.product-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.product-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.product-specs {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.spec-label {
    font-weight: 500;
    color: var(--gray-700);
}

.spec-value {
    font-weight: 600;
    color: var(--primary-600);
}

.product-applications {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.app-tag {
    background: var(--primary-50);
    color: var(--primary-600);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
}

.products-cta {
    background: white;
    border-radius: var(--radius-2xl);
    padding: 3rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-100);
}

.cta-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.cta-content p {
    color: var(--gray-600);
    font-size: 1.125rem;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .about-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .about-visual {
        order: -1;
    }

    .floating-card.card-1,
    .floating-card.card-2,
    .floating-card.card-3 {
        display: none;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        flex-direction: column;
        padding: 2rem;
        box-shadow: var(--shadow-xl);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-item {
        margin-bottom: 1rem;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: var(--gray-50);
        margin-top: 0.5rem;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .mobile-menu-btn.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .mobile-menu-btn.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-btn.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .hero-slogan .slogan-en {
        font-size: 1rem;
    }

    .hero-slogan .slogan-zh {
        font-size: 1.25rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .trust-content {
        flex-direction: column;
        text-align: center;
    }

    .trust-logos {
        justify-content: center;
    }

    .section-title {
        font-size: clamp(1.75rem, 6vw, 2.5rem);
    }

    .about-features {
        gap: 1.5rem;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        align-items: center;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .products-filter {
        flex-direction: column;
        align-items: center;
    }

    .filter-btn {
        width: 200px;
        text-align: center;
    }

    .products-cta {
        padding: 2rem;
    }

    .applications-grid {
        grid-template-columns: 1fr;
    }

    .quality-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero-content {
        padding: 6rem 0 3rem;
    }

    .hero-badge {
        font-size: 0.75rem;
        padding: 0.375rem 1rem;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .section-badge {
        font-size: 0.75rem;
        padding: 0.375rem 1rem;
    }

    .about-section,
    .products-section,
    .applications-section,
    .quality-section,
    .contact-section {
        padding: 4rem 0;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .contact-item {
        padding: 1.5rem;
    }

    .footer {
        padding: 3rem 0 1rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .main-image {
        height: 250px;
    }

    .main-image img {
        height: 250px;
    }

    .product-content {
        padding: 1.5rem;
    }

    .product-title {
        font-size: 1.25rem;
    }

    .products-cta {
        padding: 1.5rem;
    }

    .cta-content h3 {
        font-size: 1.5rem;
    }

    .cta-content p {
        font-size: 1rem;
    }

    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }

    .btn-large {
        padding: 0.875rem 1.75rem;
        font-size: 0.95rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.fade-in {
    opacity: 0;
    animation: fadeInUp 0.6s ease forwards;
}

.slide-in-left {
    transform: translateX(-50px);
    opacity: 0;
    animation: slideInLeft 0.6s ease forwards;
}

.slide-in-right {
    transform: translateX(50px);
    opacity: 0;
    animation: slideInRight 0.6s ease forwards;
}

@keyframes slideInLeft {
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .scroll-indicator {
        animation: none;
    }

    .floating-card {
        animation: none;
    }
}

/* Focus styles for accessibility */
.btn:focus,
.nav-link:focus,
.lang-btn:focus,
.filter-btn:focus {
    outline: 2px solid var(--primary-600);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --gray-600: #000000;
        --gray-700: #000000;
        --gray-800: #000000;
        --gray-900: #000000;
    }

    .btn-outline {
        border-width: 3px;
    }

    .nav-link::after {
        height: 3px;
    }
}

/* Print styles */
@media print {
    .navbar,
    .scroll-indicator,
    .floating-cards,
    .hero-particles,
    .btn,
    .mobile-menu-btn {
        display: none !important;
    }

    .hero {
        min-height: auto;
        padding: 2rem 0;
    }

    .hero-background,
    .hero-overlay {
        display: none;
    }

    .hero-content {
        color: black;
    }

    .section-padding {
        padding: 2rem 0;
    }
}

/* ===== APPLICATIONS SECTION ===== */
.applications-section {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.applications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
}

.application-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    position: relative;
}

.application-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
}

.app-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.app-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.application-card:hover .app-image img {
    transform: scale(1.1);
}

.app-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(59, 130, 246, 0.9);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.application-card:hover .app-overlay {
    opacity: 1;
}

.app-content {
    padding: 2rem;
}

.app-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.app-content p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* ===== QUALITY SECTION ===== */
.quality-section {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    color: white;
}

.quality-section .section-badge {
    background: rgba(59, 130, 246, 0.2);
    color: var(--primary-400);
}

.quality-section .section-title {
    color: white;
}

.quality-section .section-description {
    color: var(--gray-300);
}

.quality-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.quality-item {
    text-align: center;
    padding: 2.5rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.quality-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.1);
}

.quality-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.quality-item h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.quality-item p {
    color: var(--gray-300);
    line-height: 1.6;
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 4rem;
}

.contact-form {
    background: white;
    padding: 2.5rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    transition: all var(--transition-normal);
    background: transparent;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-600);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group label {
    position: absolute;
    top: 1rem;
    left: 1.25rem;
    color: var(--gray-500);
    transition: all var(--transition-normal);
    pointer-events: none;
    background: white;
    padding: 0 0.25rem;
}

.form-group input:focus + label,
.form-group textarea:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group textarea:not(:placeholder-shown) + label {
    top: -0.5rem;
    left: 1rem;
    font-size: 0.75rem;
    color: var(--primary-600);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2rem;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--gray-600);
    margin-bottom: 0.75rem;
}

.whatsapp-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #25d366;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-normal);
}

.whatsapp-btn:hover {
    background: #128c7e;
    transform: translateY(-2px);
}

/* ===== FOOTER ===== */
.footer {
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-950) 100%);
    color: white;
    padding: 4rem 0 1.5rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2.5rem;
    margin-bottom: 2.5rem;
}

.footer-section h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--primary-400);
}

.footer-section h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: white;
}

.footer-section p {
    color: var(--gray-300);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section ul li a {
    color: var(--gray-300);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-section ul li a:hover {
    color: var(--primary-400);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(59, 130, 246, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-400);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.social-links a:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-3px);
}

.contact-info-footer p {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.contact-info-footer i {
    color: var(--primary-400);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1.5rem;
    text-align: center;
}

.footer-bottom p {
    color: var(--gray-400);
    margin: 0;
}
