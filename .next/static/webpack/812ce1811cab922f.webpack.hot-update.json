{"c": ["app/[locale]/page", "app/[locale]/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"]}