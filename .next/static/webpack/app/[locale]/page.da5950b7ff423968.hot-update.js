/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAdvantages.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FProduct.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAdvantages.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FProduct.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/About.tsx */ \"(app-pages-browser)/./src/components/About.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Advantages.tsx */ \"(app-pages-browser)/./src/components/Advantages.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Contact.tsx */ \"(app-pages-browser)/./src/components/Contact.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(app-pages-browser)/./src/components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero.tsx */ \"(app-pages-browser)/./src/components/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Product.tsx */ \"(app-pages-browser)/./src/components/Product.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ5dCUyRkRvd25sb2FkcyUyRiVFNiU5RCVBRCVFNSVCNyU5RSVFNSU4RCVBMSVFNiU4MSVBOSUyMHZzJTIwY29kZSUyMCVFNSU4MSU5QSVFNyU5QSU4NCVFNyVCRCU5MSVFNyVBQiU5OSUyRm5vZGVfbW9kdWxlcyUyRm5leHQtaW50bCUyRmRpc3QlMkZlc20lMkZzaGFyZWQlMkZOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnl0JTJGRG93bmxvYWRzJTJGJUU2JTlEJUFEJUU1JUI3JTlFJUU1JThEJUExJUU2JTgxJUE5JTIwdnMlMjBjb2RlJTIwJUU1JTgxJTlBJUU3JTlBJTg0JUU3JUJEJTkxJUU3JUFCJTk5JTJGc3JjJTJGY29tcG9uZW50cyUyRkFib3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ5dCUyRkRvd25sb2FkcyUyRiVFNiU5RCVBRCVFNSVCNyU5RSVFNSU4RCVBMSVFNiU4MSVBOSUyMHZzJTIwY29kZSUyMCVFNSU4MSU5QSVFNyU5QSU4NCVFNyVCRCU5MSVFNyVBQiU5OSUyRnNyYyUyRmNvbXBvbmVudHMlMkZBZHZhbnRhZ2VzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ5dCUyRkRvd25sb2FkcyUyRiVFNiU5RCVBRCVFNSVCNyU5RSVFNSU4RCVBMSVFNiU4MSVBOSUyMHZzJTIwY29kZSUyMCVFNSU4MSU5QSVFNyU5QSU4NCVFNyVCRCU5MSVFNyVBQiU5OSUyRnNyYyUyRmNvbXBvbmVudHMlMkZDb250YWN0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ5dCUyRkRvd25sb2FkcyUyRiVFNiU5RCVBRCVFNSVCNyU5RSVFNSU4RCVBMSVFNiU4MSVBOSUyMHZzJTIwY29kZSUyMCVFNSU4MSU5QSVFNyU5QSU4NCVFNyVCRCU5MSVFNyVBQiU5OSUyRnNyYyUyRmNvbXBvbmVudHMlMkZIZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnl0JTJGRG93bmxvYWRzJTJGJUU2JTlEJUFEJUU1JUI3JTlFJUU1JThEJUExJUU2JTgxJUE5JTIwdnMlMjBjb2RlJTIwJUU1JTgxJTlBJUU3JTlBJTg0JUU3JUJEJTkxJUU3JUFCJTk5JTJGc3JjJTJGY29tcG9uZW50cyUyRkhlcm8udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnl0JTJGRG93bmxvYWRzJTJGJUU2JTlEJUFEJUU1JUI3JTlFJUU1JThEJUExJUU2JTgxJUE5JTIwdnMlMjBjb2RlJTIwJUU1JTgxJTlBJUU3JTlBJTg0JUU3JUJEJTkxJUU3JUFCJTk5JTJGc3JjJTJGY29tcG9uZW50cyUyRlByb2R1Y3QudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhQQUF3SztBQUN4SztBQUNBLDhLQUFnSTtBQUNoSTtBQUNBLHdMQUFxSTtBQUNySTtBQUNBLGtMQUFrSTtBQUNsSTtBQUNBLGdMQUFpSTtBQUNqSTtBQUNBLDRLQUErSDtBQUMvSDtBQUNBLGtMQUFrSSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzJkNTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL3l0L0Rvd25sb2Fkcy/mna3lt57ljaHmgakgdnMgY29kZSDlgZrnmoTnvZHnq5kvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zaGFyZWQvTmV4dEludGxDbGllbnRQcm92aWRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy95dC9Eb3dubG9hZHMv5p2t5bee5Y2h5oGpIHZzIGNvZGUg5YGa55qE572R56uZL3NyYy9jb21wb25lbnRzL0Fib3V0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy95dC9Eb3dubG9hZHMv5p2t5bee5Y2h5oGpIHZzIGNvZGUg5YGa55qE572R56uZL3NyYy9jb21wb25lbnRzL0FkdmFudGFnZXMudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL3l0L0Rvd25sb2Fkcy/mna3lt57ljaHmgakgdnMgY29kZSDlgZrnmoTnvZHnq5kvc3JjL2NvbXBvbmVudHMvQ29udGFjdC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMveXQvRG93bmxvYWRzL+adreW3nuWNoeaBqSB2cyBjb2RlIOWBmueahOe9keermS9zcmMvY29tcG9uZW50cy9IZWFkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL3l0L0Rvd25sb2Fkcy/mna3lt57ljaHmgakgdnMgY29kZSDlgZrnmoTnvZHnq5kvc3JjL2NvbXBvbmVudHMvSGVyby50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMveXQvRG93bmxvYWRzL+adreW3nuWNoeaBqSB2cyBjb2RlIOWBmueahOe9keermS9zcmMvY29tcG9uZW50cy9Qcm9kdWN0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAdvantages.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FProduct.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Advantages.tsx":
/*!***************************************!*\
  !*** ./src/components/Advantages.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Advantages; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_Leaf_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Leaf,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_Leaf_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Leaf,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/leaf.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_Leaf_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Leaf,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_Leaf_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Leaf,Settings,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Advantages() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations)(\"advantages\");\n    const advantages = [\n        {\n            icon: _barrel_optimize_names_Award_Leaf_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            key: \"highViscosity\",\n            color: \"bg-blue-500\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Leaf_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            key: \"ecoFriendly\",\n            color: \"bg-green-500\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Leaf_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            key: \"customizable\",\n            color: \"bg-purple-500\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Leaf_Settings_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            key: \"quality\",\n            color: \"bg-orange-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t(\"title\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-primary-600 font-semibold\",\n                            children: t(\"subtitle\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: advantages.map((advantage, index)=>{\n                        const IconComponent = advantage.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 \".concat(advantage.color, \" rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"w-8 h-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 mb-4\",\n                                    children: t(\"\".concat(advantage.key, \".title\"))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 leading-relaxed\",\n                                    children: t(\"\".concat(advantage.key, \".description\"))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary-50 to-blue-50 rounded-2xl p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"Ready to Experience KARN Quality?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6 max-w-2xl mx-auto\",\n                                children: \"Contact us today to discuss your specific requirements and discover how our premium CMS products can enhance your industrial applications.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    const element = document.getElementById(\"contact\");\n                                    if (element) {\n                                        element.scrollIntoView({\n                                            behavior: \"smooth\"\n                                        });\n                                    }\n                                },\n                                className: \"inline-flex items-center px-8 py-3 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-colors shadow-lg hover:shadow-xl\",\n                                children: \"Get Quote\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Advantages.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_s(Advantages, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations\n    ];\n});\n_c = Advantages;\nvar _c;\n$RefreshReg$(_c, \"Advantages\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Advantages.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.mjs":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/award.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Award; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Award = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Award\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"8\",\n            r: \"6\",\n            key: \"1vp47v\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11\",\n            key: \"em7aur\"\n        }\n    ]\n]);\n //# sourceMappingURL=award.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/leaf.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/leaf.mjs ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Leaf; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Leaf = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Leaf\", [\n    [\n        \"path\",\n        {\n            d: \"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z\",\n            key: \"nnexq3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12\",\n            key: \"mt58a7\"\n        }\n    ]\n]);\n //# sourceMappingURL=leaf.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/leaf.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/settings.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Settings; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Settings = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Settings\", [\n    [\n        \"path\",\n        {\n            d: \"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z\",\n            key: \"1qme2f\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n]);\n //# sourceMappingURL=settings.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-up.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TrendingUp; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst TrendingUp = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"TrendingUp\", [\n    [\n        \"polyline\",\n        {\n            points: \"22 7 13.5 15.5 8.5 10.5 2 17\",\n            key: \"126l90\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 7 22 7 22 13\",\n            key: \"kwv8wd\"\n        }\n    ]\n]);\n //# sourceMappingURL=trending-up.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs\n"));

/***/ })

});