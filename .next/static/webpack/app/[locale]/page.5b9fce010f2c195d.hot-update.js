/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAdvantages.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FProduct.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAdvantages.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FProduct.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/About.tsx */ \"(app-pages-browser)/./src/components/About.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Advantages.tsx */ \"(app-pages-browser)/./src/components/Advantages.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Contact.tsx */ \"(app-pages-browser)/./src/components/Contact.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(app-pages-browser)/./src/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(app-pages-browser)/./src/components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero.tsx */ \"(app-pages-browser)/./src/components/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Product.tsx */ \"(app-pages-browser)/./src/components/Product.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FAdvantages.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fcomponents%2FProduct.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Footer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.mjs\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.mjs\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Footer() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations)(\"footer\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"KARN\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-4\",\n                                    children: t(\"company\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-300 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t(\"address\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    const element = document.getElementById(\"about\");\n                                                    if (element) {\n                                                        element.scrollIntoView({\n                                                            behavior: \"smooth\"\n                                                        });\n                                                    }\n                                                },\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    const element = document.getElementById(\"products\");\n                                                    if (element) {\n                                                        element.scrollIntoView({\n                                                            behavior: \"smooth\"\n                                                        });\n                                                    }\n                                                },\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"Products\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    const element = document.getElementById(\"contact\");\n                                                    if (element) {\n                                                        element.scrollIntoView({\n                                                            behavior: \"smooth\"\n                                                        });\n                                                    }\n                                                },\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t(\"follow\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors\",\n                                            \"aria-label\": \"LinkedIn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors\",\n                                            \"aria-label\": \"Facebook\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: [\n                            \"\\xa9 2024 \",\n                            t(\"company\"),\n                            \". \",\n                            t(\"rights\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/杭州卡恩 vs code 做的网站/src/components/Footer.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_s(Footer, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations\n    ];\n});\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Footer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/facebook.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Facebook; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Facebook = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Facebook\", [\n    [\n        \"path\",\n        {\n            d: \"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\",\n            key: \"1jg4f8\"\n        }\n    ]\n]);\n //# sourceMappingURL=facebook.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZmFjZWJvb2subWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBYU0sTUFBQUEsV0FBV0MsaUVBQWdCQSxDQUFDLFlBQVk7SUFDNUM7UUFDRTtRQUNBO1lBQ0VDLEdBQUc7WUFDSEMsS0FBSztRQUNQO0tBQ0Y7Q0FDRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL2ljb25zL2ZhY2Vib29rLnRzPzU4MTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBGYWNlYm9va1xuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTVRnZ01tZ3RNMkUxSURVZ01DQXdJREF0TlNBMWRqTklOM1kwYUROMk9HZzBkaTA0YUROc01TMDBhQzAwVmpkaE1TQXhJREFnTUNBeElERXRNV2d6ZWlJZ0x6NEtQQzl6ZG1jK0NnPT0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2ZhY2Vib29rXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgRmFjZWJvb2sgPSBjcmVhdGVMdWNpZGVJY29uKCdGYWNlYm9vaycsIFtcbiAgW1xuICAgICdwYXRoJyxcbiAgICB7XG4gICAgICBkOiAnTTE4IDJoLTNhNSA1IDAgMCAwLTUgNXYzSDd2NGgzdjhoNHYtOGgzbDEtNGgtNFY3YTEgMSAwIDAgMSAxLTFoM3onLFxuICAgICAga2V5OiAnMWpnNGY4JyxcbiAgICB9LFxuICBdLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IEZhY2Vib29rO1xuIl0sIm5hbWVzIjpbIkZhY2Vib29rIiwiY3JlYXRlTHVjaWRlSWNvbiIsImQiLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/linkedin.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Linkedin; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Linkedin = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Linkedin\", [\n    [\n        \"path\",\n        {\n            d: \"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z\",\n            key: \"c2jq9f\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"4\",\n            height: \"12\",\n            x: \"2\",\n            y: \"9\",\n            key: \"mk3on5\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"4\",\n            cy: \"4\",\n            r: \"2\",\n            key: \"bt5ra8\"\n        }\n    ]\n]);\n //# sourceMappingURL=linkedin.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbGlua2VkaW4ubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBYU0sTUFBQUEsV0FBV0MsaUVBQWdCQSxDQUFDLFlBQVk7SUFDNUM7UUFDRTtRQUNBO1lBQ0VDLEdBQUc7WUFDSEMsS0FBSztRQUNQO0tBQ0Y7SUFDQTtRQUFDO1FBQVE7WUFBRUMsT0FBTztZQUFLQyxRQUFRO1lBQU1DLEdBQUc7WUFBS0MsR0FBRztZQUFLSixLQUFLO1FBQUE7S0FBVTtJQUNwRTtRQUFDO1FBQVU7WUFBRUssSUFBSTtZQUFLQyxJQUFJO1lBQUtDLEdBQUc7WUFBS1AsS0FBSztRQUFBO0tBQVU7Q0FDdkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9pY29ucy9saW5rZWRpbi50cz84N2JkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgTGlua2VkaW5cbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1UWWdPR0UySURZZ01DQXdJREVnTmlBMmRqZG9MVFIyTFRkaE1pQXlJREFnTUNBd0xUSXRNaUF5SURJZ01DQXdJREF0TWlBeWRqZG9MVFIyTFRkaE5pQTJJREFnTUNBeElEWXRObm9pSUM4K0NpQWdQSEpsWTNRZ2QybGtkR2c5SWpRaUlHaGxhV2RvZEQwaU1USWlJSGc5SWpJaUlIazlJamtpSUM4K0NpQWdQR05wY21Oc1pTQmplRDBpTkNJZ1kzazlJalFpSUhJOUlqSWlJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9saW5rZWRpblxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IExpbmtlZGluID0gY3JlYXRlTHVjaWRlSWNvbignTGlua2VkaW4nLCBbXG4gIFtcbiAgICAncGF0aCcsXG4gICAge1xuICAgICAgZDogJ00xNiA4YTYgNiAwIDAgMSA2IDZ2N2gtNHYtN2EyIDIgMCAwIDAtMi0yIDIgMiAwIDAgMC0yIDJ2N2gtNHYtN2E2IDYgMCAwIDEgNi02eicsXG4gICAgICBrZXk6ICdjMmpxOWYnLFxuICAgIH0sXG4gIF0sXG4gIFsncmVjdCcsIHsgd2lkdGg6ICc0JywgaGVpZ2h0OiAnMTInLCB4OiAnMicsIHk6ICc5Jywga2V5OiAnbWszb241JyB9XSxcbiAgWydjaXJjbGUnLCB7IGN4OiAnNCcsIGN5OiAnNCcsIHI6ICcyJywga2V5OiAnYnQ1cmE4JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBMaW5rZWRpbjtcbiJdLCJuYW1lcyI6WyJMaW5rZWRpbiIsImNyZWF0ZUx1Y2lkZUljb24iLCJkIiwia2V5Iiwid2lkdGgiLCJoZWlnaHQiLCJ4IiwieSIsImN4IiwiY3kiLCJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.mjs\n"));

/***/ })

});