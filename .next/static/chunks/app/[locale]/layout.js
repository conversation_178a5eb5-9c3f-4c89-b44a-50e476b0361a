/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/[locale]/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ5dCUyRkRvd25sb2FkcyUyRiVFNiU5RCVBRCVFNSVCNyU5RSVFNSU4RCVBMSVFNiU4MSVBOSUyMHZzJTIwY29kZSUyMCVFNSU4MSU5QSVFNyU5QSU4NCVFNyVCRCU5MSVFNyVBQiU5OSUyRm5vZGVfbW9kdWxlcyUyRm5leHQtaW50bCUyRmRpc3QlMkZlc20lMkZzaGFyZWQlMkZOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnl0JTJGRG93bmxvYWRzJTJGJUU2JTlEJUFEJUU1JUI3JTlFJUU1JThEJUExJUU2JTgxJUE5JTIwdnMlMjBjb2RlJTIwJUU1JTgxJTlBJUU3JTlBJTg0JUU3JUJEJTkxJUU3JUFCJTk5JTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4UEFBd0s7QUFDeEs7QUFDQSxvS0FBOEYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz82OGExIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy95dC9Eb3dubG9hZHMv5p2t5bee5Y2h5oGpIHZzIGNvZGUg5YGa55qE572R56uZL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy95dC9Eb3dubG9hZHMv5p2t5bee5Y2h5oGpIHZzIGNvZGUg5YGa55qE572R56uZL3NyYy9hcHAvZ2xvYmFscy5jc3NcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@formatjs/fast-memoize/lib/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: function() { return /* binding */ memoize; },\n/* harmony export */   strategies: function() { return /* binding */ strategies; }\n/* harmony export */ });\n//\n// Main\n//\nfunction memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nvar strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": function() { return /* binding */ n; }\n/* harmony export */ });\nfunction n(){return n=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)({}).hasOwnProperty.call(t,a)&&(n[a]=t[a])}return n},n.apply(null,arguments)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsYUFBYSx3REFBd0QsWUFBWSxtQkFBbUIsS0FBSyxtQkFBbUIsa0JBQWtCLHdDQUF3QyxTQUFTLHlCQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanM/OGQwYyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBuKCl7cmV0dXJuIG49T2JqZWN0LmFzc2lnbj9PYmplY3QuYXNzaWduLmJpbmQoKTpmdW5jdGlvbihuKXtmb3IodmFyIHI9MTtyPGFyZ3VtZW50cy5sZW5ndGg7cisrKXt2YXIgdD1hcmd1bWVudHNbcl07Zm9yKHZhciBhIGluIHQpKHt9KS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHQsYSkmJihuW2FdPXRbYV0pfXJldHVybiBufSxuLmFwcGx5KG51bGwsYXJndW1lbnRzKX1leHBvcnR7biBhcyBleHRlbmRzfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ r; }\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_IntlProvider */ \"(app-pages-browser)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction r(r) {\n    let { locale: o, ...i } = r;\n    if (!o) throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({\n        locale: o\n    }, i));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7NkRBQ21FO0FBQXFCO0FBQXNEO0FBQUEsU0FBU0ssRUFBRUEsQ0FBQztJQUFFLElBQUcsRUFBQ0MsUUFBT0MsQ0FBQyxFQUFDLEdBQUdDLEdBQUUsR0FBQ0g7SUFBRSxJQUFHLENBQUNFLEdBQUUsTUFBTSxJQUFJRSxNQUFNO0lBQStKLHFCQUFPUCwwREFBZSxDQUFDRSwrREFBQ0EsRUFBQ0gsZ0ZBQUNBLENBQUM7UUFBQ0ssUUFBT0M7SUFBQyxHQUFFQztBQUFHO0FBQXNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanM/ZGNlNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydHtleHRlbmRzIGFzIGV9ZnJvbVwiLi4vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qc1wiO2ltcG9ydCBsIGZyb21cInJlYWN0XCI7aW1wb3J0e0ludGxQcm92aWRlciBhcyB0fWZyb21cInVzZS1pbnRsL19JbnRsUHJvdmlkZXJcIjtmdW5jdGlvbiByKHIpe2xldHtsb2NhbGU6bywuLi5pfT1yO2lmKCFvKXRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBkZXRlcm1pbmUgbG9jYWxlIGluIGBOZXh0SW50bENsaWVudFByb3ZpZGVyYCwgcGxlYXNlIHByb3ZpZGUgdGhlIGBsb2NhbGVgIHByb3AgZXhwbGljaXRseS5cXG5cXG5TZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvY29uZmlndXJhdGlvbiNsb2NhbGVcIik7cmV0dXJuIGwuY3JlYXRlRWxlbWVudCh0LGUoe2xvY2FsZTpvfSxpKSl9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOlsiZXh0ZW5kcyIsImUiLCJsIiwiSW50bFByb3ZpZGVyIiwidCIsInIiLCJsb2NhbGUiLCJvIiwiaSIsIkVycm9yIiwiY3JlYXRlRWxlbWVudCIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js":
/*!************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nvar React = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nconst IntlContext = /*#__PURE__*/React.createContext(undefined);\n\nexports.IntlContext = IntlContext;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L2RldmVsb3BtZW50L0ludGxDb250ZXh0LUJLZnNuekJ4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFlBQVksbUJBQU8sQ0FBQyxtRkFBTzs7QUFFM0I7O0FBRUEsbUJBQW1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L2RldmVsb3BtZW50L0ludGxDb250ZXh0LUJLZnNuekJ4LmpzP2Q0OGYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgUmVhY3QgPSByZXF1aXJlKCdyZWFjdCcpO1xuXG5jb25zdCBJbnRsQ29udGV4dCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVDb250ZXh0KHVuZGVmaW5lZCk7XG5cbmV4cG9ydHMuSW50bENvbnRleHQgPSBJbnRsQ29udGV4dDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/use-intl/dist/development/_IntlProvider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_IntlProvider.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar React = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(app-pages-browser)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(app-pages-browser)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\nfunction IntlProvider(_ref) {\n  let {\n    children,\n    defaultTranslationValues,\n    formats,\n    getMessageFallback,\n    locale,\n    messages,\n    now,\n    onError,\n    timeZone\n  } = _ref;\n  // The formatter cache is released when the locale changes. For\n  // long-running apps with a persistent `IntlProvider` at the root,\n  // this can reduce the memory footprint (e.g. in React Native).\n  const cache = React.useMemo(() => {\n    return initializeConfig.createCache();\n  }, [locale]);\n  const formatters = React.useMemo(() => initializeConfig.createIntlFormatters(cache), [cache]);\n\n  // Memoizing this value helps to avoid triggering a re-render of all\n  // context consumers in case the configuration didn't change. However,\n  // if some of the non-primitive values change, a re-render will still\n  // be triggered. Note that there's no need to put `memo` on `IntlProvider`\n  // itself, because the `children` typically change on every render.\n  // There's some burden on the consumer side if it's important to reduce\n  // re-renders, put that's how React works.\n  // See: https://blog.isquaredsoftware.com/2020/05/blogged-answers-a-mostly-complete-guide-to-react-rendering-behavior/#context-updates-and-render-optimizations\n  const value = React.useMemo(() => ({\n    ...initializeConfig.initializeConfig({\n      locale,\n      defaultTranslationValues,\n      formats,\n      getMessageFallback,\n      messages,\n      now,\n      onError,\n      timeZone\n    }),\n    formatters,\n    cache\n  }), [cache, defaultTranslationValues, formats, formatters, getMessageFallback, locale, messages, now, onError, timeZone]);\n  return /*#__PURE__*/React__default.default.createElement(IntlContext.IntlContext.Provider, {\n    value: value\n  }, children);\n}\n\nexports.IntlProvider = IntlProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/use-intl/dist/development/_IntlProvider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nlet IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    _defineProperty(this, \"code\", void 0);\n    _defineProperty(this, \"originalMessage\", void 0);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nfunction joinPath() {\n  for (var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++) {\n    parts[_key] = arguments[_key];\n  }\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return fastMemoize.memoize(fn, {\n    cache: createMemoCache(cache),\n    strategy: fastMemoize.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return new ConstructorFn(...args);\n  }, cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(_ref => {\n    let [key, messageOrMessages] = _ref;\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, \"Namespace keys can not contain the character \\\".\\\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid \".concat(invalidKeyLabels.length === 1 ? 'key' : 'keys', \": \").concat(invalidKeyLabels.join(', '), \"\\n\\nIf you're migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \\\"lodash\\\";\\n\\nconst input = {\\n  \\\"one.one\\\": \\\"1.1\\\",\\n  \\\"one.two\\\": \\\"1.2\\\",\\n  \\\"two.one.one\\\": \\\"2.1.1\\\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \\\"one\\\": {\\n//     \\\"one\\\": \\\"1.1\\\",\\n//     \\\"two\\\": \\\"1.2\\\"\\n//   },\\n//   \\\"two\\\": {\\n//     \\\"one\\\": {\\n//       \\\"one\\\": \\\"2.1.1\\\"\\n//     }\\n//   }\\n// }\\n\") ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig(_ref) {\n  let {\n    getMessageFallback,\n    messages,\n    onError,\n    ...rest\n  } = _ref;\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    messages,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"b1a44226246d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MGE4ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImIxYTQ0MjI2MjQ2ZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyt%2FDownloads%2F%E6%9D%AD%E5%B7%9E%E5%8D%A1%E6%81%A9%20vs%20code%20%E5%81%9A%E7%9A%84%E7%BD%91%E7%AB%99%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);