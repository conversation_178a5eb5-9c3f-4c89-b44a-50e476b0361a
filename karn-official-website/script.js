// ===== 全局变量 =====
let currentLanguage = 'zh';
const supportedLanguages = ['zh', 'en'];

// ===== DOM加载完成后初始化 =====
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
});

// ===== 网站初始化 =====
function initializeWebsite() {
    // 检测用户语言偏好
    detectUserLanguage();
    
    // 初始化导航功能
    initializeNavigation();
    
    // 初始化滚动效果
    initializeScrollEffects();
    
    // 初始化统计数字动画
    initializeCounterAnimation();
    
    // 初始化语言切换
    initializeLanguageSwitcher();
    
    // 初始化移动端菜单
    initializeMobileMenu();
    
    // 初始化平滑滚动
    initializeSmoothScroll();
}

// ===== 语言检测和切换 =====
function detectUserLanguage() {
    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const langParam = urlParams.get('lang');
    
    // 检查本地存储
    const savedLang = localStorage.getItem('preferred-language');
    
    // 检查浏览器语言
    const browserLang = navigator.language || navigator.userLanguage;
    const browserLangCode = browserLang.split('-')[0];
    
    let detectedLang = 'zh'; // 默认中文
    
    if (langParam && supportedLanguages.includes(langParam)) {
        detectedLang = langParam;
    } else if (savedLang && supportedLanguages.includes(savedLang)) {
        detectedLang = savedLang;
    } else if (browserLangCode === 'en') {
        detectedLang = 'en';
        showLanguageBanner('zh'); // 显示中文建议
    }
    
    switchLanguage(detectedLang);
}

function showLanguageBanner(suggestedLang) {
    const banner = document.getElementById('language-banner');
    const suggestedLink = document.getElementById('suggested-lang');
    
    if (banner && suggestedLink) {
        suggestedLink.textContent = suggestedLang === 'zh' ? '中文版本' : 'English Version';
        suggestedLink.onclick = () => {
            switchLanguage(suggestedLang);
            closeLangBanner();
        };
        
        banner.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            closeLangBanner();
        }, 5000);
    }
}

function closeLangBanner() {
    const banner = document.getElementById('language-banner');
    if (banner) {
        banner.style.display = 'none';
    }
}

function switchLanguage(lang) {
    if (!supportedLanguages.includes(lang)) return;
    
    currentLanguage = lang;
    
    // 隐藏所有语言内容
    supportedLanguages.forEach(l => {
        const elements = document.querySelectorAll(`[data-lang="${l}"]`);
        elements.forEach(el => {
            el.style.display = 'none';
        });
    });
    
    // 显示当前语言内容
    const currentElements = document.querySelectorAll(`[data-lang="${lang}"]`);
    currentElements.forEach(el => {
        el.style.display = el.tagName.toLowerCase() === 'span' ? 'inline' : 'block';
    });
    
    // 更新HTML lang属性
    document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
    
    // 更新当前语言显示
    const currentLangSpan = document.getElementById('current-lang');
    if (currentLangSpan) {
        currentLangSpan.textContent = lang === 'zh' ? '中文' : 'English';
    }
    
    // 保存语言偏好
    localStorage.setItem('preferred-language', lang);
    
    // 更新URL（不刷新页面）
    const url = new URL(window.location);
    url.searchParams.set('lang', lang);
    window.history.replaceState({}, '', url);
}

// ===== 导航功能 =====
function initializeNavigation() {
    const navbar = document.getElementById('navbar');
    
    // 滚动时改变导航栏样式
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // 导航链接点击处理
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    const offsetTop = target.offsetTop - 80; // 考虑固定导航栏高度
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                    
                    // 移动端关闭菜单
                    closeMobileMenu();
                }
            }
        });
    });
}

// ===== 移动端菜单 =====
function initializeMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const navMenu = document.getElementById('nav-menu');
    
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            this.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // 点击菜单外部关闭
        document.addEventListener('click', function(e) {
            if (!mobileMenuBtn.contains(e.target) && !navMenu.contains(e.target)) {
                closeMobileMenu();
            }
        });
    }
}

function closeMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const navMenu = document.getElementById('nav-menu');
    
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.classList.remove('active');
        navMenu.classList.remove('active');
    }
}

// ===== 滚动效果 =====
function initializeScrollEffects() {
    // 创建Intersection Observer用于元素进入视口动画
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    const animateElements = document.querySelectorAll('.hero-text, .hero-stats, .stat-item');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// ===== 统计数字动画 =====
function initializeCounterAnimation() {
    const counters = document.querySelectorAll('.stat-number[data-count]');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-count'));
        const duration = 2000; // 2秒动画
        const step = target / (duration / 16); // 60fps
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // 格式化数字显示
            if (target >= 1000) {
                counter.textContent = Math.floor(current).toLocaleString();
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 16);
    };
    
    // 使用Intersection Observer触发动画
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// ===== 语言切换器 =====
function initializeLanguageSwitcher() {
    const langBtn = document.getElementById('lang-btn');
    const langDropdown = document.querySelector('.lang-dropdown');
    
    if (langBtn && langDropdown) {
        // 点击按钮切换下拉菜单
        langBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            langDropdown.style.opacity = langDropdown.style.opacity === '1' ? '0' : '1';
            langDropdown.style.visibility = langDropdown.style.visibility === 'visible' ? 'hidden' : 'visible';
        });
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', function() {
            langDropdown.style.opacity = '0';
            langDropdown.style.visibility = 'hidden';
        });
    }
}

// ===== 平滑滚动 =====
function initializeSmoothScroll() {
    // 为所有内部链接添加平滑滚动
    const internalLinks = document.querySelectorAll('a[href^="#"]');
    
    internalLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;
            
            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                
                const offsetTop = target.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// ===== 工具函数 =====

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 防抖函数
function debounce(func, wait, immediate) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// 检查元素是否在视口中
function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// ===== 性能优化 =====

// 预加载关键资源
function preloadCriticalResources() {
    const criticalImages = [
        'images/hero-bg.jpg',
        'images/logo.png'
    ];
    
    criticalImages.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
    });
}

// 懒加载图片
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// 页面加载完成后执行优化
window.addEventListener('load', function() {
    preloadCriticalResources();
    initializeLazyLoading();
});

// ===== 错误处理 =====
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    // 可以在这里添加错误报告逻辑
});

// ===== 导出函数供HTML调用 =====
window.switchLanguage = switchLanguage;
window.closeLangBanner = closeLangBanner;
