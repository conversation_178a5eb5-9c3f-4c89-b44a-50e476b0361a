/* ===== CSS变量定义 ===== */
:root {
    /* 主色调 - 工业蓝 */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* 辅助色 - 专业灰 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* 功能色 */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #06b6d4;

    /* 字体 */
    --font-chinese: 'Noto Sans SC', sans-serif;
    --font-english: 'Inter', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* 过渡 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* 断点 */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* ===== 基础重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-chinese);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: #ffffff;
    overflow-x: hidden;
}

/* 英文内容使用英文字体 */
[data-lang="en"] {
    font-family: var(--font-english);
}

/* ===== 通用组件 ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-xl);
    }
}

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: var(--font-size-base);
    line-height: 1;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-lg);
}

/* ===== 语言横幅 ===== */
.language-banner {
    background: var(--primary-600);
    color: white;
    padding: var(--spacing-sm) 0;
    text-align: center;
    position: relative;
    z-index: 1000;
}

.language-banner .container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);
}

.lang-switch-link {
    color: white;
    text-decoration: underline;
    font-weight: 500;
}

.banner-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    position: absolute;
    right: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
}

/* ===== 导航栏 ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 999;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

/* Logo区域 */
.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.brand-logo {
    height: 40px;
    width: auto;
}

.brand-name {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-600);
    margin: 0;
}

.brand-tagline {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

/* 主导航 */
.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
    margin: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-normal);
    border-radius: var(--radius-md);
}

.nav-link:hover {
    color: var(--primary-600);
    background: var(--primary-50);
}

/* 下拉菜单 */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-md);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    list-style: none;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: var(--spacing-md);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.dropdown-menu a:hover {
    background: var(--primary-50);
    color: var(--primary-600);
}

/* 右侧操作区 */
.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

/* 语言切换器 */
.language-switcher {
    position: relative;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: none;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.lang-btn:hover {
    border-color: var(--primary-600);
    color: var(--primary-600);
}

.lang-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-md);
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.language-switcher:hover .lang-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lang-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    text-decoration: none;
    color: var(--gray-700);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.lang-option:hover {
    background: var(--primary-50);
    color: var(--primary-600);
}

.lang-option img {
    width: 20px;
    height: auto;
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.mobile-menu-btn span {
    width: 25px;
    height: 3px;
    background: var(--gray-700);
    border-radius: var(--radius-full);
    transition: all var(--transition-normal);
}

/* ===== Hero区域 ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -2;
}

.hero-image {
    width: 100%;
    height: 100%;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(51, 65, 85, 0.6));
    z-index: -1;
}

.hero-content {
    position: relative;
    z-index: 1;
    width: 100%;
    padding: var(--spacing-3xl) 0;
}

.hero-text {
    text-align: center;
    color: white;
    margin-bottom: var(--spacing-3xl);
}

.hero-badge {
    display: inline-block;
    background: rgba(59, 130, 246, 0.2);
    color: var(--primary-300);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--spacing-xl);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.hero-title {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-xl);
}

.title-main {
    display: block;
    color: white;
}

.title-sub {
    display: block;
    color: var(--primary-300);
    font-size: 0.7em;
    font-weight: 500;
    margin-top: var(--spacing-md);
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--gray-300);
    max-width: 600px;
    margin: 0 auto var(--spacing-2xl);
    line-height: 1.7;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

/* 统计数据 */
.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-3xl);
    padding-top: var(--spacing-3xl);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
    text-align: center;
    color: white;
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-300);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-base);
    color: var(--gray-300);
    font-weight: 500;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        flex-direction: column;
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-xl);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .mobile-menu-btn.active span:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .mobile-menu-btn.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-btn.active span:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: var(--gray-50);
        margin-top: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .nav-container {
        padding: var(--spacing-md);
    }

    .hero-stats {
        grid-template-columns: 1fr;
    }

    .stat-number {
        font-size: var(--font-size-3xl);
    }
}

/* ===== 页面内容区域 ===== */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-header h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-lg);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== 关于我们区域 ===== */
.about-section {
    padding: var(--spacing-3xl) 0;
    background: var(--gray-50);
}

.about-content {
    margin-top: var(--spacing-3xl);
}

.about-text h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xl);
}

.about-text p {
    font-size: var(--font-size-base);
    line-height: 1.8;
    color: var(--gray-700);
    margin-bottom: var(--spacing-xl);
}

.company-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.feature-item {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-600);
    margin-bottom: var(--spacing-md);
}

.feature-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* ===== 产品中心区域 ===== */
.products-section {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-3xl);
}

.product-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
}

.product-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-content {
    padding: var(--spacing-xl);
}

.product-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.product-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.product-content ul {
    list-style: none;
    padding: 0;
}

.product-content li {
    position: relative;
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--gray-700);
}

.product-content li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--primary-600);
    font-weight: bold;
}

/* ===== 应用领域区域 ===== */
.applications-section {
    padding: var(--spacing-3xl) 0;
    background: var(--gray-50);
}

.applications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-3xl);
}

.application-item {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
}

.application-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.app-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
}

.application-item h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.application-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* ===== 质量保证区域 ===== */
.quality-section {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.quality-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-3xl);
}

.quality-item {
    background: var(--gray-50);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    transition: all var(--transition-normal);
}

.quality-item:hover {
    background: var(--primary-50);
    transform: translateY(-5px);
}

.quality-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
}

.quality-item h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.quality-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* ===== 新闻动态区域 ===== */
.news-section {
    padding: var(--spacing-3xl) 0;
    background: var(--gray-50);
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-3xl);
}

.news-item {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.news-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.news-date {
    color: var(--primary-600);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--spacing-md);
}

.news-item h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
}

.news-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* ===== 联系我们区域 ===== */
.contact-section {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    margin-top: var(--spacing-3xl);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.contact-icon {
    font-size: 1.5rem;
    margin-top: var(--spacing-xs);
}

.contact-details h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
}

.contact-details p {
    color: var(--gray-600);
    margin: 0;
}

/* ===== 联系表单 ===== */
.contact-form {
    background: var(--gray-50);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
}

.contact-form h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xl);
}

.form-group {
    position: relative;
    margin-bottom: var(--spacing-xl);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-lg) var(--spacing-md);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    background: white;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-600);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group label {
    position: absolute;
    top: var(--spacing-lg);
    left: var(--spacing-md);
    color: var(--gray-500);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    pointer-events: none;
    background: white;
    padding: 0 var(--spacing-xs);
}

.form-group input:focus + label,
.form-group input:valid + label,
.form-group textarea:focus + label,
.form-group textarea:valid + label {
    top: -10px;
    font-size: var(--font-size-sm);
    color: var(--primary-600);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== 页脚 ===== */
.footer {
    background: var(--gray-900);
    color: white;
    padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-section h3,
.footer-section h4 {
    color: white;
    margin-bottom: var(--spacing-lg);
}

.footer-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.footer-section h4 {
    font-size: var(--font-size-base);
    font-weight: 500;
}

.footer-section p {
    color: var(--gray-300);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section li {
    margin-bottom: var(--spacing-sm);
}

.footer-section a {
    color: var(--gray-300);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-section a:hover {
    color: var(--primary-400);
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    padding-top: var(--spacing-xl);
    text-align: center;
}

.footer-bottom p {
    color: var(--gray-400);
    margin: 0;
}

/* ===== 移动端响应式样式 ===== */
@media (max-width: 768px) {
    /* 产品网格 */
    .products-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    /* 应用领域网格 */
    .applications-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    /* 质量保证网格 */
    .quality-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    /* 新闻网格 */
    .news-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    /* 联系内容 */
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    /* 公司特色 */
    .company-features {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    /* 页脚 */
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }

    /* 表单标签调整 */
    .form-group input:focus + label,
    .form-group input:valid + label,
    .form-group textarea:focus + label,
    .form-group textarea:valid + label {
        top: -8px;
        font-size: var(--font-size-xs);
    }
}

@media (max-width: 480px) {
    /* 应用领域网格 */
    .applications-grid {
        grid-template-columns: 1fr;
    }

    /* 产品卡片内边距 */
    .product-content {
        padding: var(--spacing-lg);
    }

    /* 联系表单内边距 */
    .contact-form {
        padding: var(--spacing-lg);
    }

    /* 各区域内边距 */
    .about-section,
    .products-section,
    .applications-section,
    .quality-section,
    .news-section,
    .contact-section {
        padding: var(--spacing-2xl) 0;
    }

    /* 标题字体大小 */
    .section-header h2 {
        font-size: var(--font-size-3xl);
    }

    .about-text h3 {
        font-size: var(--font-size-xl);
    }
}
