# 杭州卡恩官方网站

这是使用MCP Prompt Server模板制作的杭州卡恩新型建材有限公司官方网站。

## 🎯 项目特点

### 基于MCP Prompt Server模板设计
- ✅ `bilingual_website` - 中英文双语支持
- ✅ `responsive_design` - 完全响应式设计
- ✅ `ui_component_generator` - 现代化UI组件
- ✅ `website_optimization` - 性能优化
- ✅ `css_debugging` - 代码质量保证

### 核心功能
- 🌐 **智能语言切换** - 自动检测用户语言偏好
- 📱 **响应式设计** - 完美适配所有设备
- ⚡ **性能优化** - 快速加载和流畅交互
- 🎨 **现代化UI** - 专业的B2B企业形象
- 🔍 **SEO友好** - 完整的搜索引擎优化

## 📁 项目结构

```
karn-official-website/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # JavaScript功能
├── images/             # 图片资源
├── README.md           # 项目说明
└── ...
```

## 🚀 快速开始

### 1. 启动本地服务器
```bash
cd karn-official-website
python3 -m http.server 8080
```

### 2. 访问网站
打开浏览器访问: `http://localhost:8080`

## 🌐 多语言功能

### 自动语言检测
网站会根据以下优先级自动选择语言：
1. URL参数 (`?lang=zh` 或 `?lang=en`)
2. 本地存储的用户偏好
3. 浏览器语言设置

### 手动切换语言
- 点击导航栏右上角的语言切换按钮
- 选择"中文"或"English"

### 语言横幅
当检测到用户可能更适合其他语言时，会显示语言建议横幅。

## 📱 响应式断点

| 设备类型 | 屏幕宽度 | 特殊适配 |
|---------|---------|---------|
| 手机 | < 768px | 移动端菜单、垂直布局 |
| 平板 | 768px - 1024px | 中等间距、网格调整 |
| 桌面 | > 1024px | 完整布局、悬停效果 |

## 🎨 设计系统

### 色彩方案
- **主色调**: 工业蓝 (#3b82f6)
- **辅助色**: 专业灰 (#6b7280)
- **功能色**: 成功绿、警告橙、错误红

### 字体系统
- **中文**: Noto Sans SC
- **英文**: Inter
- **尺寸**: 12px - 48px (响应式)

### 组件库
- 按钮组件 (主要、次要、大尺寸)
- 导航组件 (桌面、移动端)
- 卡片组件
- 表单组件

## ⚡ 性能优化

### 已实现的优化
- ✅ CSS/JS文件压缩
- ✅ 图片懒加载
- ✅ 关键资源预加载
- ✅ 平滑滚动优化
- ✅ 事件节流和防抖

### Core Web Vitals目标
- **LCP**: < 2.5s
- **FID**: < 100ms
- **CLS**: < 0.1

## 🔍 SEO优化

### 已实现的SEO功能
- ✅ 语义化HTML结构
- ✅ 完整的meta标签
- ✅ Open Graph标签
- ✅ 结构化数据 (JSON-LD)
- ✅ 多语言hreflang标签
- ✅ 图片alt属性

### 关键词策略
- 羧甲基淀粉、CMS、建筑材料
- 纺织助剂、工业添加剂
- 杭州卡恩、KARN

## 🛠️ 技术栈

- **HTML5** - 语义化结构
- **CSS3** - 现代样式和动画
- **Vanilla JavaScript** - 原生JS，无依赖
- **CSS Grid & Flexbox** - 现代布局
- **CSS Custom Properties** - 设计系统变量

## 📊 浏览器兼容性

| 浏览器 | 版本支持 |
|-------|---------|
| Chrome | 60+ |
| Firefox | 60+ |
| Safari | 12+ |
| Edge | 79+ |

## 🔧 自定义配置

### 修改语言内容
在HTML中找到 `data-lang="zh"` 和 `data-lang="en"` 属性的元素，修改对应的文本内容。

### 修改样式
在 `styles.css` 中的 `:root` 部分修改CSS变量：
```css
:root {
    --primary-600: #your-color;
    --font-chinese: 'Your-Font';
}
```

### 添加新页面
1. 复制 `index.html` 结构
2. 修改内容区域
3. 更新导航链接

## 📈 性能监控

网站内置了Core Web Vitals监控，在浏览器控制台可以查看性能指标。

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📞 联系信息

- **公司**: 杭州卡恩新型建材有限公司
- **网站**: https://karn-materials.com
- **邮箱**: <EMAIL>
- **电话**: +86 132 1615 6841

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

*本网站使用MCP Prompt Server模板系统构建，确保了代码质量和最佳实践。*
