name: bilingual_website
description: 当用户需要创建或优化中英文双语网站时，可以使用这个提示词获得专业的多语言网站开发建议
arguments:
  - name: website_purpose
    description: 网站用途(企业官网/产品展示/电商/服务介绍等)
    required: true
  - name: primary_language
    description: 主要语言(中文/英文)
    required: true
  - name: target_markets
    description: 目标市场(国内/海外/全球等)
    required: true
  - name: content_complexity
    description: 内容复杂度(简单介绍/详细产品信息/技术文档等)
    required: false
messages:
  - role: user
    content:
      type: text
      text: |
        请为{{website_purpose}}创建中英文双语网站方案，主要语言为{{primary_language}}，目标市场为{{target_markets}}：

        {{#if content_complexity}}
        内容复杂度：{{content_complexity}}
        {{/if}}

        请提供以下方面的详细方案：

        ## 1. 多语言架构设计
        - 语言切换机制
        - URL结构设计(子域名/路径/参数)
        - 内容管理策略
        - SEO友好的多语言实现

        ## 2. 用户界面设计
        - 语言切换按钮设计
        - 中英文字体选择
        - 文本长度适配
        - 文化差异考虑

        ## 3. 技术实现
        - HTML lang属性设置
        - CSS文字方向和对齐
        - JavaScript语言检测
        - 本地化数据管理

        ## 4. 内容策略
        - 翻译质量控制
        - 文化本地化建议
        - 关键词本地化
        - 图片和媒体适配

        ## 5. SEO优化
        - hreflang标签设置
        - 多语言sitemap
        - 本地化关键词策略
        - 搜索引擎优化

        ## 6. 用户体验
        - 语言自动检测
        - 用户偏好记忆
        - 导航一致性
        - 表单本地化

        请提供具体的代码示例和实现指南。
