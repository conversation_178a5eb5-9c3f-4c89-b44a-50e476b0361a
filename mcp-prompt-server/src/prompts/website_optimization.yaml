name: website_optimization
description: 当用户想要优化网站性能、SEO或用户体验时，可以使用这个提示词来获得专业的优化建议
arguments:
  - name: website_type
    description: 网站类型(企业官网/电商/博客/应用等)
    required: true
  - name: current_issues
    description: 当前遇到的问题或需要优化的方面
    required: false
  - name: target_audience
    description: 目标用户群体
    required: false
  - name: performance_metrics
    description: 当前性能指标(如页面加载时间、Core Web Vitals等)
    required: false
messages:
  - role: user
    content:
      type: text
      text: |
        请为以下{{website_type}}网站提供全面的优化建议：

        {{#if current_issues}}
        当前问题：{{current_issues}}
        {{/if}}

        {{#if target_audience}}
        目标用户：{{target_audience}}
        {{/if}}

        {{#if performance_metrics}}
        性能指标：{{performance_metrics}}
        {{/if}}

        请从以下方面提供优化建议：

        ## 1. 性能优化
        - 页面加载速度优化
        - 图片和资源优化
        - 代码压缩和缓存策略
        - Core Web Vitals改进

        ## 2. SEO优化
        - 关键词策略
        - 元标签优化
        - 结构化数据
        - 内容优化

        ## 3. 用户体验(UX)
        - 导航结构
        - 响应式设计
        - 可访问性
        - 转化率优化

        ## 4. 技术架构
        - 代码结构优化
        - 安全性改进
        - 监控和分析

        请提供具体可执行的建议和最佳实践。
