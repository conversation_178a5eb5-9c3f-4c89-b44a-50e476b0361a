name: css_debugging
description: 当用户遇到CSS样式问题时，可以使用这个提示词来诊断和解决CSS相关问题
arguments:
  - name: issue_description
    description: 遇到的CSS问题描述
    required: true
  - name: css_code
    description: 相关的CSS代码
    required: false
  - name: html_structure
    description: 相关的HTML结构
    required: false
  - name: browser_info
    description: 浏览器信息和兼容性要求
    required: false
messages:
  - role: user
    content:
      type: text
      text: |
        请帮助诊断和解决以下CSS问题：

        ## 问题描述
        {{issue_description}}

        {{#if css_code}}
        ## CSS代码
        ```css
        {{css_code}}
        ```
        {{/if}}

        {{#if html_structure}}
        ## HTML结构
        ```html
        {{html_structure}}
        ```
        {{/if}}

        {{#if browser_info}}
        ## 浏览器信息
        {{browser_info}}
        {{/if}}

        请提供以下方面的分析和解决方案：

        ## 1. 问题诊断
        - 可能的原因分析
        - CSS优先级问题
        - 布局冲突检查
        - 浏览器兼容性问题

        ## 2. 解决方案
        - 具体的修复代码
        - 替代实现方案
        - 最佳实践建议

        ## 3. 预防措施
        - 如何避免类似问题
        - 代码组织建议
        - 调试技巧

        ## 4. 浏览器兼容性
        - 跨浏览器解决方案
        - Polyfill或fallback方案
        - 渐进增强策略

        请提供详细的代码示例和解释。
