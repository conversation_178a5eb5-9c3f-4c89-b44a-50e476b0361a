name: ui_component_generator
description: 当用户需要创建UI组件时，可以使用这个提示词来生成现代化的、可复用的UI组件
arguments:
  - name: component_type
    description: 组件类型(按钮/卡片/导航/表单/模态框等)
    required: true
  - name: design_style
    description: 设计风格(现代/简约/商务/创意等)
    required: true
  - name: technology_stack
    description: 技术栈(HTML+CSS/React/Vue/Angular等)
    required: true
  - name: color_scheme
    description: 色彩方案或品牌色
    required: false
  - name: special_features
    description: 特殊功能需求(动画/响应式/可访问性等)
    required: false
messages:
  - role: user
    content:
      type: text
      text: |
        请创建一个{{design_style}}风格的{{component_type}}组件，使用{{technology_stack}}技术栈：

        {{#if color_scheme}}
        色彩方案：{{color_scheme}}
        {{/if}}

        {{#if special_features}}
        特殊需求：{{special_features}}
        {{/if}}

        请提供以下内容：

        ## 1. 组件设计
        - 视觉设计说明
        - 交互行为定义
        - 状态变化说明

        ## 2. 代码实现
        - 完整的HTML结构
        - CSS样式代码
        - JavaScript交互逻辑(如需要)

        ## 3. 变体和状态
        - 不同尺寸变体
        - 不同状态样式(hover/active/disabled等)
        - 主题变体(如有需要)

        ## 4. 使用指南
        - 使用示例
        - 自定义选项
        - 最佳实践

        ## 5. 可访问性
        - ARIA属性
        - 键盘导航支持
        - 屏幕阅读器友好

        ## 6. 响应式适配
        - 移动端优化
        - 不同屏幕尺寸适配

        请确保代码现代化、可维护且符合最佳实践。
