name: responsive_design
description: 当用户需要创建或优化响应式网页设计时，可以使用这个提示词获得专业的响应式设计建议
arguments:
  - name: device_targets
    description: 目标设备(手机/平板/桌面/全部)
    required: true
  - name: design_framework
    description: 使用的CSS框架(Bootstrap/Tailwind/原生CSS等)
    required: false
  - name: content_type
    description: 内容类型(文本/图片/视频/表格等)
    required: false
  - name: current_breakpoints
    description: 当前使用的断点设置
    required: false
messages:
  - role: user
    content:
      type: text
      text: |
        请为{{device_targets}}设备提供响应式设计方案：

        {{#if design_framework}}
        使用框架：{{design_framework}}
        {{/if}}

        {{#if content_type}}
        主要内容：{{content_type}}
        {{/if}}

        {{#if current_breakpoints}}
        当前断点：{{current_breakpoints}}
        {{/if}}

        请提供以下方面的详细建议：

        ## 1. 断点策略
        - 推荐的断点设置
        - 设备适配范围
        - 媒体查询最佳实践

        ## 2. 布局设计
        - 网格系统设计
        - 弹性布局方案
        - 组件响应式行为

        ## 3. 内容适配
        - 文字大小和行高
        - 图片和媒体适配
        - 导航菜单响应式

        ## 4. 性能优化
        - 图片响应式加载
        - CSS优化策略
        - JavaScript适配

        ## 5. 用户体验
        - 触摸友好设计
        - 可访问性考虑
        - 加载性能优化

        请提供具体的CSS代码示例和实现方案。
