{"name": "mcp-prompt-server", "version": "1.0.0", "description": "MCP服务器，提供预设的prompt工具来引导Claude执行任务", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "claude", "prompt", "ai"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "express": "^4.18.2", "fs-extra": "^11.2.0", "yaml": "^2.3.4", "zod": "^3.22.4"}, "devDependencies": {"nodemon": "^3.0.3"}}